<template>
  <!-- 磅房信息 -->
  <div class="electricity padding"  :style="{color:bgcolor.font}">
    <!-- <div :class="['electricity-top','righticon']"
    :style="{background:'linear-gradient(90deg, rgba(1, 194, 255, 0) 0%,'+bgcolor.titlecolor+' 97%)'}"
    >
        <span class="padding-text-span" >{{electricity1.titles}}</span>
        <img :src="electricity1.src"  >
    </div> -->
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"></Chamfering>

    <div  class="electricity-two">
        <div class="electricity-two-top">
            <p v-for="(item,index) in electricitylable" :key="index">{{item.name}}:
                {{electricity1.titles=='智慧用水'?electric[item.value]:Smartwater[item.value]}}</p>
        </div>
        <div class="electricity-three">
            <ecahrts :ids="electricity1.ids" :options="option"></ecahrts>
        </div>
    </div>
	<!-- <div :class="'rightbefore'" 
    :style="{borderColor:bgcolor.chamfer}"
    ></div>
    <div :class="'rightafter'"
     :style="{borderColor:bgcolor.chamfer}"
     ></div> -->
    <Chamfering :homeindex="'4'" :horn="0"></Chamfering>

  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import ecahrts from "@/components/connet/personnelcon/echarts3d/workerkq.vue";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
props:['electricity1'],
components:{
    ecahrts,
    Chamfering
},
setup(props,exte){

  let bgcolor=ref({})
//   let echart=ref(true)
//   let countlist=ref([])
  let getform=ref({
      ProjectCode:store.getters.code,
    Type:''
    })
    let electric=ref({})
    let Smartwater=ref({})
    let colorlist=['#f77b66','#3edce0','#f94e76']
    let option=ref([])
    let electricitylable=ref([
        {
            name:'本次读数',
            value:'RealPower'
        },{
            name:'上次读数',
            value:'LastPower'
        },{
            name:'本次耗能',
            value:'XHPower'
        }
    ])
    let topforms=ref({
      url:props.electricity1.src,
      name:props.electricity1.titles
    })
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // console.log('获取智慧用电',props.electricity1.titles);

    if(props.electricity1.titles=='智慧用水'){
        getform.value.Type='智慧用水'

        getelectricity().then((res)=>{
                // console.log('获取数据智慧用水',res);
            if (res.code=="1000") {
                // props.electricity1.titles
                electric.value=res.data

                electric.value.Echarts.forEach((item,index)=>{
                    // console.log('图表',item);
                    electric.value.Echarts[index].itemStyle={
                        color:colorlist[index]
                    }
                });
                option.value=electric.value.Echarts
                // console.log('获取',option.value);
                
            }   
            })
    }
    if(props.electricity1.titles=='智慧用电'){
        getform.value.Type='智慧用电'

        getelectricity().then((res)=>{
                // console.log('获取数据智慧用电',res);
                if (res.code=="1000") {
                    Smartwater.value=res.data

                    Smartwater.value.Echarts.forEach((item,index)=>{
                    // console.log('图表',item);
                    Smartwater.value.Echarts[index].itemStyle={
                        color:colorlist[index]
                            }
                    });
                    option.value=Smartwater.value.Echarts
                }
                
            })
    }

  })
  const getelectricity=async()=>{
        const {data:res}=await gettable('GetTodayGreenConstruction',getform.value)
        // console.log('获取数据',res);
        return res
  }


 

	return{
		getform,
		bgcolor,
        option,
        electricitylable,
        electric,
        Smartwater,
        topforms,

        getelectricity

	// getelectricity,
	}
}
}
</script>

<style lang="scss" scoped>
.electricity{
&-two{
  height: 86%;
  padding: 10px;
//   display: grid;
  font-size: 14px;

    p{
        text-align: start;
        padding: 5px;
    }
    &-top{
        display: grid;
        grid-template-columns: 50% 50%;
        height: 25%;
    }
}
&-three{
    height: 75%;
    width: 100%;
}
}



</style>