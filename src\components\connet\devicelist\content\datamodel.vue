<template>
  <el-dialog v-model="dialogTableVisible" destroy-on-close
    class="delogss" :width="'70%'"
    :style="{ marginTop: '0px', top: '0px' }">
    <selection ref="selection"  :titles="titles" @colses="colses"></selection>
      <div class="personwqmits bodybottom" :style="`border:2px solid ${bgcolor.titlecolor};
        background:rgba(${bgcolor.delogcolor},0.35)`">
        <div class="eqmentdelog">
            <el-scrollbar height="500px">
              <div class="eqmentdelog-one">
                  <div v-for="(item,index) in lablelist" :key="index" 
                       :class="getItemClass(index, item)"
                       :style="getItemStyle(index, item)" 
                       @click="change(item,index)">
                    {{item.EquipName}}
                  </div>
              </div>
            </el-scrollbar>
            <div v-if="typestate!=3">
                <div class="towercranecount">
                    <div class="towercrane" v-if="type=='10101'">
                        <div class="towercrane-list" >
                            <img  src="@/assets/img/device/translation/tadiao2.png" alt="">
                            <div class="line" ></div>
                            <img  src="@/assets/img/device/translation/tadiao3.png" alt="">
                            <img  src="@/assets/img/device/translation/tadiao4.png" alt="">
                            <div class="taleft toptp"><p>实时高度：{{Craness.Height}}m</p></div>
                            <div class="taright toptp bton"><p>实时吊重：{{Craness.Weight}}t</p></div>
                            <div class="taright toptp topds"><p>实时幅度：{{Craness.Range}}m</p></div>
                        </div>
                    </div>
                    <div class="lifitingsum" v-else-if="type=='10103'">
                        <img class="leftBox_bg" src="@/assets/img/device/lifing/leftBox_bg.png" alt="">
                        <img class="room_bg" src="@/assets/img/device/lifing/room_bg.png" alt="">
                        <img class="rightBox_bg" src="@/assets/img/device/lifing/rightBox_bg.png" alt="">
                        <img class="room_bg1" src="@/assets/img/device/lifing/room_bg.png" alt="">
                        <div class="lifitleft lifitingsum-lsit">
                          <div v-for="(item,index) in lefttops" :key="index">{{item.name}}:{{Craness[item.value]}}{{item.unit}}</div>
                        </div>
                        <div class="lifitright lifitingsum-lsit">
                          <div v-for="(item,index) in lefttops" :key="index">{{item.name}}:{{Craness[item.value1]}}{{item.unit}}</div>

                        </div>
                    </div>
                    <div class="discharge"  v-else-if="type=='10104'">
                        <div class="taleft toptp"><p>实时载重：{{Craness.Weight}}T</p></div>

                    </div>
                    <div class="towercraneright lifitingsum-lsit" v-if="type=='10101'||type=='10104'">
                      <div id="ange"></div>
                    </div>
                    
                </div>
            </div>
            <div  v-if="typestate!=3" class="eqmentdelog-two Homebgco" :style="{color:bgcolor.font}">
                <div :class="['Typeworkss-top','lefticon']"
                :style="{background:'linear-gradient(90deg, '+bgcolor.titlecolor+' 0%, rgba(1, 194, 255, 0) 97%)'}"
                >     
                    <div class="rightshow">
                        <img src="@/assets/img/home/<USER>"  >
                        <span class="padding-text-span" >设备信息</span>
                    </div>
                </div>
                <div class="eqmentdelog-two-lable" v-for="(item,index) in eqmentlist" :key="index"
                v-show="type=='10101'?index<=4:(type=='10103'?(index!=3&&index!=4&&index<=6):(index<1||index>6))"
                >
                    <p>{{item.name}}：{{bases[item.value]}}</p>
                </div>
                <div :class="'leftbefore'" :style="{borderColor:bgcolor.chamfer}"></div>
                <div :class="'leftafter'" :style="{borderColor:bgcolor.chamfer}"></div>
            </div>
            <div  v-if="typestate!=3" class="Homebgco bttomechart" :style="{color:bgcolor.font}">
              <div class="bttomechart-one echatr">
                <div v-for="(item,index) in btncount " :key="index" 
                     class="echatr-one-name cursor" 
                     :style="getChartBtnStyle(index)" 
                     @click="change1(item,index)">
                    <p>{{item}}</p>
                </div>
              </div>
              <div class="bttomechart-two">
                  <btomsechart :ids="'btomsechart'"  :options1="options"></btomsechart>
              </div>
              <div :class="'leftbefore'"  :style="{borderColor:bgcolor.chamfer}"></div>
              <div :class="'leftafter'" :style="{borderColor:bgcolor.chamfer}" ></div>
            </div>
            <div  v-if="typestate==3" class="tablecout">
              <el-date-picker v-model="getform.CurrentDate" type="date" placeholder="选择日期"
               format="YYYY-MM-DD" value-format="YYYY-MM-DD"/>
              <el-button type="primary" @click="serch">查询</el-button>
              <el-table :data="tableDate" class="cursor" :style="['width: 100%',`color:${bgcolor.font};
                --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
                :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}" max-height="550px"
                empty-text="暂无数据"
                >
                    <template #empty>
                        <el-empty  v-loading="loading"></el-empty>
                    </template>
                    <el-table-column v-for="(item,index) in lables" :key="index" width="" :prop="item.value"
                    :label="item.name" align="center"> </el-table-column>
              </el-table>
              <el-pagination
                v-model:current-page="getform.page"
                v-model:page-size="getform.count"
                :page-sizes="[10, 20, 40, 50,100]"
                :background="background"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totles"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
            </div>
        </div>
      </div>

    </el-dialog>
</template>

<script>
import $ from 'jquery'
import { nextTick, onMounted, ref } from 'vue'
import btomsechart from "@/components/connet/Common/echartscom.vue";
import liftechart from "@/components/connet/Common/echartscom.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import selection from "@/components/connet/Common/selection.vue";

export default {
  components:{
    btomsechart,
    liftechart,
    selection
  },
    setup(){
        let bgcolor=ref({})
        let falge=ref(0)
        let falge1=ref(0)
        let dialogTableVisible=ref(false)
        let options=ref({})
        let option1=ref({})
        let type=ref(0)
        let bases=ref({})
        let totles=ref(0)
        let background=ref(false)
        let loading=ref(false)
        let tableDate=ref([])
        let lablelist=ref([
        ])
        let eqmentlist=[
            {
            name:'设备编号',
            value:'EquipCode',
            },
            {
            name:'操作人员',
            value:'CraneOperator'
            },{
            name:'累计工作时长',
            value:'hours'
            },{
            name:'累计吊次',
            value:'LiftingCount'
            },{
            name:'累计吊重',
            value:'WeightCount'
            },{
              name:'累计运行高度',
            value:'LiftingCount'
            },{
              name:'累计载重',
            value:'WeightCount'
            },{
              name:'所在位置',
              value:'unLoadPosition'
            },{
              name:'预警载重',
            value:'EarlyWarnWeight'
            },{
              name:'报警载重',
            value:'WarnWeight'
            },{
              name:'告警次数',
            value:'WarnCount'
            }

        ]
        let btncount=ref([])
        let lablenamr=['吊重','高度','转角','幅度','力矩比','倾角','更多']
        let Lifting=['载重','轨迹','速度','X倾角','Y倾角','更多']
        let discharge=['载重','倾角','更多']
        let lefttops=ref([
          {
            name:'实时载重',
            value:'LWindSpeed',
            value1:'RWindSpeed',
            unit:'t'
          },
          {
            name:'实时高度',
            value:'LHeight',
            value1:'RHeight',
            unit:'m'
          },{
            name:'实时速度',
            value:'LTorque',
            value1:'RTorque',
            unit:'m/s'
          },{
            name:'运行方向',
            value:'LDirection',
            value1:'RDirection',
            unit:''
          },
        ])
        let getform=ref({
              ProjectCode:store.getters.code,
              EquipCode:'',
              EquipType:'吊重',
              EquipType2:'',
              CurrentDate:'',
              page:1,
              count:10
        })
        let echartslist=ref({})
        let echartssjj=ref([])
        let Craness=ref({})
        let typestate=ref(1)
        let lables=ref([])
        let Towertable=ref([
          {
            name:'时间',
            value:'CurrentDate'
          },{
            name:'设备编号',
            value:'EquipCode'
          },{
            name:'吊重（t）',
            value:'Weight'
          },{
            name:'高度（m）',
            value:'Height'
          },{
            name:'幅度（m）',
            value:'Range'
          },{
            name:'转角（°）',
            value:'Corner'
          },{
            name:'倾角（°）',
            value:'Angle'
          },{
            name:'力矩比（%）',
            value:'Torque'
          },{
            name:'倾角（m/s）',
            value:'Torque'
          },
        ])
        let Elevatortable=ref([
          {
            name:'时间',
            value:'CurrentDate'
          },{
            name:'设备编号',
            value:'EquipCode'
          },{
            name:'载重(t)',
            value:'WindSpeed'
          },{
            name:'高度(m)',
            value:'Height'
          },{
            name:'运行速度(m/s)',
            value:'Torque'
          }
        ])
        let Unloadingtabl=ref([
          {
            name:'时间',
            value:'CurrentDate'
          },{
            name:'设备编号',
            value:'EquipCode'
          },{
            name:'载重（t）',
            value:'Weight'
          },{
            name:'倾角(°)',
            value:'Angle'
          },
        ])
        let titles='数据模型'
        window.addEventListener('setthcolor', ()=> {
            // console.log('导航');
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        })
        onMounted(()=>{
            bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
            // nextTick(()=)
            btncount.value=lablenamr

            // gettadechatr()
        })
        const showdelog=()=>{
            getequip()
            // type.value='10101'
            lables.value=Towertable.value

            dialogTableVisible.value=true
            // nextTick(()=>{
            //     getange()
            // })
        }
        // 获取列表所有10101塔机  10103升降机 10104卸料
        const  getequip=async()=>{
            const {data:res}=await gettable('GetAllEquipByPro',getform.value)
            // console.log('返回',res);
            
              if (res.code=="1000") {
                lablelist.value=res.data
                type.value=res.data?.[0].EquipType
                getform.value.EquipCode=res.data?.[0].EquipCode
                // console.log('获取设备',res.data);
                getBasic('GetCraneBeforeData')
                getechartslin('GetCraneAnalyse')
                getCrane('GetrealtCraneData2')
              }
        }
        // 获取
        const getBasic=async(val)=>{
            const {data:res}=await gettable(val,getform.value)
            // console.log('获取基础信息',res);
            if (res.code=="1000") {
              bases.value=res.data.length>0?res.data[0]:res.data
            }
        }
        // 塔机最新数据
        const getCrane=async(url)=>{
            const {data:res}=await gettable(url,getform.value)
            if (res.code=="1000") {
              // console.log('塔机数据',res);
              Craness.value=res.data
            }else{
              Craness.value={}
            }
            if (type.value=='10101') {
              getjqs()
              nextTick(()=>{
                getange()
              })
            }else if(type.value=='10103'){
              let leftcomputer=(parseFloat(Craness.value.LHeight)+20)*3
              let rightcomputer=(parseFloat(Craness.value.RHeight)+20)*3
              // console.log('计算',computer);
              
              let leheight=leftcomputer>480?480:leftcomputer
              let riheight=rightcomputer>480?480:rightcomputer
              $('.leftBox_bg,.room_bg,.lifitleft').animate({
              bottom: leheight+ "px"
              }, 3000, function() {
            
              });
              $('.rightBox_bg,.room_bg1,.lifitright').animate({
              bottom: riheight+ "px"
              }, 3000, function() {
            
              });
            }else if(type.value=='10104'){
              nextTick(()=>{
                getange()
              })
            }
        }
        // 升降机
        // const 
        // 动态显示
        const getjqs=()=>{
          
        let left=  Craness.value.Range+16>349?349:Craness.value.Range+16
          
          $('.towercrane-list').animate({
					left: left + "px"
          }, 3000, function() {
        
          });
          $('.line').animate({
            height: Craness.value.Height + "px"
          }, 3000, function() {
            // console.log('高度');
            
          });
        }
        const serch=()=>{
          getform.value.page=1
          getform.value.count=10
          getElevator()
        }
        // 获取升降机
        const getElevator=async(url)=>{
            tableDate.value=[]
            loading.value=true
            const {data:res}=await gettable('GetEquipHistoryDataTable',getform.value)
              // console.log('离线',res);
              loading.value=false
              tableDate.value=res.data
             totles.value= res.Total

        }
        const getechartslin=async(url)=>{
          // console.log('提交',getform.value);
          
            const {data:res}=await gettable(url,getform.value)
            // console.log('获取数据',res);
            
            if (res.code=="1000") {
              if (type.value=='10101') {
                echartslist.value=res.data
                echartslist.value= echartslist.value.map((item,index)=>{
                    // console.log('获取数据',item);
                    return {
                      name:item.time,
                      value:item.Number
                    }
                  })
                  gettadechatr()
              }else if(type.value=='10103'){
                echartssjj.value=res.data[0]
                  getlifechart()
              }else if(type.value=='10104'){
                echartslist.value=res.data
                echartslist.value= echartslist.value.map((item,index)=>{
                    // console.log('获取数据',item);
                    return {
                      name:item.time,
                      value:item.Number
                    }
                  })
                  gettadechatr()

              }

              }else{
                echartslist.value=[]
                echartssjj.value=[]
                type.value=='10101'||type.value=='10104'?gettadechatr():getlifechart()
              }
        }

        const change=(val,index)=>{
        // console.log('点击',val,index);
          // 设备类型配置映射
          const equipTypeConfig = {
            '10101': { // 塔机
              onlineActions: {
                btnList: lablenamr,
                basicApi: 'GetCraneBeforeData',
                chartApi: 'GetCraneAnalyse',
                realDataApi: 'GetrealtCraneData2'
              },
              offlineConfig: {
                tableLabels: Towertable.value
              }
            },
            '10103': { // 升降机
              onlineActions: {
                btnList: Lifting,
                basicApi: 'GetElevatorBeforeData2',
                chartApi: 'GetElevatorAnalyse',
                realDataApi: 'GetElevatorRealData2'
              },
              offlineConfig: {
                tableLabels: Elevatortable.value
              }
            },
            '10104': { // 卸料平台
              onlineActions: {
                btnList: discharge,
                basicApi: 'GetUnLoadEquipBeforeData',
                chartApi: 'GetUnLoadCurve',
                realDataApi: 'GetUnLoadRealData'
              },
              offlineConfig: {
                tableLabels: Unloadingtabl.value
              }
            }
          };

          // 更新基础状态
          falge.value = index;
          type.value = val.EquipType;
          getform.value.EquipCode = val.EquipCode;
          getform.value.EquipType2 = val.EquipType;
          typestate.value = val.CraneState;
          
          const config = equipTypeConfig[val.EquipType];
          if (!config) return; // 防御性编程，确保配置存在
          
          // 根据设备状态执行不同操作
          if (val.CraneState != 3) { // 设备在线
            // 应用在线设备配置
            btncount.value = config.onlineActions.btnList;
            getBasic(config.onlineActions.basicApi);
            getechartslin(config.onlineActions.chartApi);
            getCrane(config.onlineActions.realDataApi);
          } else { // 设备离线
            // 应用离线设备配置
            lables.value = config.offlineConfig.tableLabels;
            getElevator();
          }
        }
        const getange=()=>{
        var echarts = require('echarts');
      //  let myChart = echarts.init(document.getElementById(props.ids));

        let myChart = echarts.getInstanceByDom(document.getElementById('ange'));
        if (myChart == null) {
          myChart = echarts.init(document.getElementById('ange'));
        }
          let  option = {
          series: [
            // 双刻度 数字
            {
              type: 'gauge',
              center: ['50%', '60%'],
              radius: "80%",
              startAngle: 210,
              endAngle: -30,
              min: 0,
              max: 360,
              splitNumber: 10,
              itemStyle: {
                color: 'rgba(11,162,154, 1)'
              },
              progress: {
                show: true,
                width: 20
              },
              pointer: {
                show: false,
              },
              axisLine: {
                lineStyle: {
                  width: 0,
                  opacity: 0,
                  color: [
                    [0.5, 'rgba(0,194,2555, 1)'], // 进度色
                    [0.75, 'rgba(148,58,255, 0.6)'], 
                    [1, 'rgba(189,19,255, 1)'], // 背景色
                  ]
                }
              },
              axisTick: {
                distance: -26,
                length: 3,
                splitNumber: 7,
                lineStyle: {
                  width: 3,
                  color: 'auto'
                }
              },
              splitLine: {
                distance: -35,
                length: 20,
                lineStyle: {
                  width: 3,
                  color: 'auto'
                }
              },
              axisLabel: {
                show:false,
                distance: -24,
                color: 'rgba(187, 235, 255, 1)',
                fontSize: 12
              },
              anchor: {
                show: false
              },
              title: {
                show: false
              },
              detail: {
                valueAnimation: true,
                width: '60%',
                lineHeight: 20,
                borderRadius: 8,
                offsetCenter:'50%',
                fontSize: 25,
                fontWeight: 'bolder',
                formatter: '{value} °',
                color: 'rgba(0,255,243, 1)'
              },
              data: [
                {
                  value:type.value=='10101'?Craness.value.Corner:Craness.value.Weight
                }
              ]
            }, 
            // 细线背景板
            {
              type: 'gauge',
              center: ['50%', '60%'],
              radius: "80%",
              startAngle: 210,
              endAngle: -30,
              min: 0,
              max: 360,
              progress: {
                show: false,
              },
              pointer: {
                show: false,
              },
              axisLine: {
                lineStyle: {
                  width:20,
                  color: [
                    [0.1, '#2E3756'], // 0~10% 红轴
                    [1, 'rgba(39,120,150, 0.8'], // 20~30% 蓝轴
                  ]
                }
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              anchor: {
                show: false
              },
              title: {
                show: false
              },
              detail: {
                show: false
              },
              data: [
                {
                  value: 26
                }
              ]
            },
          ]
        };
        myChart.setOption(option);
        window.addEventListener("resize", function() {
          myChart.resize();
        });
        }
        const gettadechatr=()=>{
              const times=echartslist.value?.map((item,index)=>{
                  return item.name
              })
          
           options.value={
            tooltip: {
              trigger: 'axis'
            },
            grid: {
              top:'5%',
              left: '3%',
              right: '4%',
              bottom: '5%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              axisLine: {
                  lineStyle: {
                    color: 'rgba(3, 251, 255, 0.3)'
                  }
                  
                },
                axisTick: {
                  show: false
                },
                axisLabel: {
                  interval: 4,
                  // textStyle: {
                  color:'#03FBFF',
                  // },
                  // 默认x轴字体大小
                  fontSize: 12,
                  // margin:文字到x轴的距离
                  margin: 15
                },
              data: times
            },
            yAxis: {
              type: 'value',
              axisTick: {
                  show: false
                },
                axisLabel: {
                  // textStyle: {
                    color: '#03FBFF'
                  // }
                },
                splitLine :{    //网格线
                    lineStyle:{
                        type:'dashed',
                        color:'rgba(3, 251, 255, 0.3)'   //设置网格线类型 dotted：虚线   solid:实线
                    },
                    show:true //隐藏或显示
                }
            },
            series: [
              {
                name: '塔机数据',
                type: 'line',
                stack: 'Total',
                color:'#03FBFF',
                data:echartslist.value
              },
            ]
           }
        }
        const getlifechart=()=>{
          // console.log('获取升降机',echartssjj.value);
          const leftsx=echartssjj.value.LElevatorAnalyse?.map((item,index)=>{
            // console.log('升降机',item);
            
            return item.Number
          })
          const rightsx=echartssjj.value.RElevatorAnalyse?.map((item,index)=>{
            return{
              name:item.time,
              value:item.Number
            }
          })
          const names=echartssjj.value.LElevatorAnalyse?.map((item,index)=>{
            return item.time
          })
          // console.log('获取数据ss',names);
          

          option1.value={
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              itemGap: 24,
              textStyle: {
            			fontSize: '12px',
            			color: '#A8D6FF',
            		},
              data: ['左机箱', '右机箱',]
            },
            grid: {
              top:'15%',
              left: '3%',
              right: '4%',
              bottom: '5%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              axisLine: {
                  lineStyle: {
                    color: 'rgba(3, 251, 255, 0.3)'
                  } 
                },
                axisTick: {
                  show: false
                },
                axisLabel: {
                  interval: 4,
                  color:'#03FBFF',
                  fontSize: 12,
                  margin: 15
                },
              data:names
            },
            yAxis: {
              type: 'value',
              axisTick: {
                  show: false
                },
                axisLabel: {
                  // textStyle: {
                    color: '#03FBFF'
                  // }
                },
                splitLine :{    //网格线
                    lineStyle:{
                        type:'dashed',
                        color:'rgba(3, 251, 255, 0.3)'   //设置网格线类型 dotted：虚线   solid:实线
                    },
                    show:true //隐藏或显示
                }
            },
            series: [
              {
                name: '左机箱',
                type: 'line',
                stack: 'Total',
                color:'#07B9B9',
                data: leftsx
              },
              {
                name: '右机箱',
                type: 'line',
                stack: 'Total',
                color:'#FD7071',
                data:rightsx
              },
            ]
           }
          options.value=option1.value
        }
        const colses=(val)=>{
          // console.log('关闭',val);
          dialogTableVisible.value=false
        }
        const change1=(val,index)=>{
          // console.log('切换',val);
          falge1.value=index
          getform.value.EquipType=val
          getechartslin(type.value=='10101'?'GetCraneAnalyse':'GetElevatorAnalyse')
          
        }
        const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
        }
        const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        getElevator()
        }
        const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        getform.value.page=val
        getElevator()

        }
        // 设备项样式类名计算
        const getItemClass = (index, item) => {
          return [
            'eqmentdelog-two-btn', 
            'cursor',
            { 'changindex': falge.value === index }
          ];
        }

        // 设备项动态样式计算
        const getItemStyle = (index, item) => {
          const isActive = falge.value === index;
          const isOffline = item.CraneState === '3';
          
          // 基础样式
          let style = {};
          
          // 活动状态样式
          if (isActive) {
            style.background = bgcolor.value.changcolor;
            style.color = '#FFF';
          } else {
            style.background = `linear-gradient(108deg, ${bgcolor.value.bgcolor} 8%, rgba(7, 93, 184, 0.6) 100%)`;
            style.color = '#FFF';
          }
          
          // 离线状态覆盖
          if (isOffline) {
            style.color = 'red';
          }
          
          return style;
        }
        // 图表按钮样式计算
        const getChartBtnStyle = (index) => {
          const isActive = falge1.value === index;
          
          if (isActive) {
            return {
              background: bgcolor.value.changcolor,
              color: '#FFF'
            };
          } else {
            return {
              background: `linear-gradient(108deg, ${bgcolor.value.bgcolor} 8%, rgba(7, 93, 184, 0.6) 100%)`,
              color: '#FFF'
            };
          }
        }
    return{
        bgcolor,
        getform,
        falge1,
        dialogTableVisible,
        lablelist,
        falge,
        discharge,
        eqmentlist,
        btncount,
        lablenamr,
        options,
        option1,
        type,
        Lifting,
        lefttops,
        bases,
        echartslist,
        echartssjj,
        Craness,
        typestate,
        totles,
        background,
        loading,
        tableDate,
        Towertable,
        lables,
        Elevatortable,
        Unloadingtabl,
        titles,
        getItemClass,
        getItemStyle,
        getChartBtnStyle,

        showdelog,
        change,
        getange,
        gettadechatr,
        getlifechart,
        change1,
        getequip,
        getBasic,
        getechartslin,
        getCrane,
        getjqs,
        getElevator,
        handleSizeChange,
        handleCurrentChange,
        tableRowClassName,
        serch,
        colses
        
        
    }
    }
}
</script>
<style lang="scss">
.personwqmits{
    opacity: 1;
    box-sizing: border-box!important;
    .el-dialog__header{
        display: none!important;
    }
    .el-dialog__body{
        padding: 10px!important;
    }
    .el-table .warning-row {
        background: rgba(15, 43, 63, 0.6)!important;
    }
    
}

</style>
<style lang="scss" scoped>
// 直接针对el-dialog组件
:deep(.delogss.el-dialog) {
  margin-top: 0!important;
  --el-dialog-margin-top: 0!important;
  top: 0!important;
}

// 针对Dialog的包装器
:deep(.el-overlay-dialog) {
  display: flex!important;
  align-items: flex-start!important;
  padding-top: 0!important;
}
.personwqmits{
    &-body-one{
        display: flex;
        align-items: center;
        
        }
    .eqmentdelog{
        display: grid;
        padding: 20px;
        grid-template-columns: 33% 67%;
        &-two-btn{
            position: relative;
            margin: 10px;
            padding: 10px;
        }
        &-one{
            display: grid;
    grid-template-columns: 50% 50%;
        }
        &-two{
            height: 250px;
            padding: 10px;
        // display: grid;
        // grid-template-columns:repeat(2,50%);
        &-lable{
            padding: 10px;
            text-align: start;
        }
        }
    }
}
.tablecout{
  text-align: left;
}
.changindex::before{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        left: -2px;
        top: -2px; 
        opacity: 1;
        border-top: 2px solid #E0A538;
        border-left: 2px solid #E0A538;
    }
.changindex::after{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        right: -2px;
        bottom: -2px; 
        opacity: 1;
        border-bottom: 2px solid #E0A538;
        border-right: 2px solid #E0A538;
    }
@mixin homeflex($deg) {
  height: 31.3%;
  width: 95%;
  opacity: 1;
  margin:7px 10px;
  background: linear-gradient($deg, #0096C7 7%, rgba(0,52,75,0.00) 97%);
}
.Homebgco{
  position: relative;
  @include homeflex(90deg)
  }
.towercranecount{
    height: 100%;
    display: flex;
    align-items: flex-end;
}
.towercraneright{
    width: 36%;
    height: 36%;
}
#ange{
        width: 100%;
        height: 100%;
    }
.towercrane{
    position: relative;
    height: 100%;
    width: 64%;
    background-image: url('@/assets/img/device/translation/tadiao.png');
    background-repeat: no-repeat;
    &-list{
        position: relative;
        top: 10%;
        left: 34%;
        width: 64.5%;
        .line{
            width: 5px;
            height: 20px;
            margin: 0 auto;
            border-left: 1px solid #6f7b89;
            border-right: 1px solid #6f7b89;
        }
        img{
            position: static;
            display: block;
            margin: 0 auto;
        }
        
    }
    .toptp{
        width: 52%;
        height: 73px;
        line-height: 63px;
        position: absolute;
        color: #fff;
    }
    .taleft{
        background-image: url('@/assets/img/device/translation/taleft.png');
        background-repeat: no-repeat;
        margin-top: -42%;
        p{
            margin-left: -20%;
        }
    }
    .taright{
        background-repeat: no-repeat;
        background-image: url('@/assets/img/device/translation/taright.png');
    p{
        margin-left: 15%;
    }
    }
    .bton{
        margin-top: -22%;
        margin-left: 61%;
    }
    .topds{
        // margin-top: -52%;
        top: -43px;
        margin-left: 49%;
    }
}
.lifitingsum{
  position: relative;
    height: 100%;
    width: 64%;
    background-image: url('@/assets/img/device/lifing/middle_bg.png');
    background-repeat: no-repeat;
    background-position: center center; 
    // &-list{
      .lifitingsum-lsit{
        position: absolute;
        padding: 5px 15px;
        text-align: center;
        width: 35%;
        height: 25%;
        display: grid;
        grid-template-rows: repeat(4,24%);
        align-items: center;
        justify-content: start;
        color: #fff;
      }
      .lifitleft{
        background-image: url('@/assets/img/device/lifing/leftboxtips.png');
        background-repeat: no-repeat;
        margin-top: 20px;
        margin-left: 20px;
        text-align: left;
        // display: grid;
        // grid-template-rows: repeat(4,24%);
        //     align-items: center;
        //     justify-content: start;
      }
      .lifitright{
        background-image: url('@/assets/img/device/lifing/rightboxtops.png');
        background-repeat: no-repeat;
        text-align: left;
        right: 0;
        margin-top: 20px;
        margin-right: 10px;
      }
      .room_bg{
        position: absolute;
        left: 39.5%;
        margin-top: 9%;
      }
      .leftBox_bg{
        position: absolute;
        left: 36.5%;
      }
      .rightBox_bg{
        position: absolute;
        right: 36.5%;

      }
      .room_bg1{
        position: absolute;
        right: 39.7%;
        margin-top: 9%;
      }
}
.discharge{
  position: relative;
    height: 100%;
    width: 64%;
    background-image: url('@/assets/img/device/discharge.png');
    background-repeat: no-repeat;
    background-position: center center; 
    .taleft{
        background-image: url('@/assets/img/device/translation/taleft.png');
        background-repeat: no-repeat;
        width: 35%;
        height: 15%;
        line-height: 60px;
        color: #fff;
            margin-top: 50%;
        p{
            margin-left: -20%;
        }
    }
}
.bttomechart{
  width: 100%;
  height: 100%;
  &-one{
    display: flex;
  }
  &-two{
    height: 85%;
    width: 100%;
    #btomsechart{
      width: 100%;
      height: 100%;
    }
    #liftechart{
      width: 100%;
      height: 100%;
    }
  }
}
.echatr{
&-one{
        width: 100%;
        height: 18%;
        margin-top: 10px;
        &-name{
            display: flex;
        align-items: center;
        height: 29px;
        width: 100px;
        margin:10px;
        opacity: 1;
        // margin: ;
        padding: 10px;
        color: #fff;
        font-size: 12px;
        clip-path: polygon(0 0px, -61px 100%, 5px 20px, 14% 100%, 100% 100%, 100% 0, 0 0);
        background: #4582ff;
        P{
            width: 75PX;
        }
        }
    }
}
</style>