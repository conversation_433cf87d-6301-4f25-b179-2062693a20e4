<template>
  <div class="echartpic" v-if="falgeecharts">
    <p class="titles">{{forms.topone}}<el-button type="primary" link @click="preview(forms.img)">
      {{ btns }}
    </el-button></p>
    <el-scrollbar class="topscrol" v-if="toplist.includes(getform.Type)" >
        <div  class="echartpic-one" v-if="getform.Type=='可回收建筑垃圾管理记录表'">
            <div class="box box1">
                <p>垃圾产量</p>
                <p>回收利用</p>
            </div>
            <div class="box box2" v-for="(item,index) in topform" :key="index" :style="getcolor(item,index)">
                <span>{{item.MaterialName}}</span>
                <span>{{item.WasteProduct}}</span>
                <span>{{item.RecyclingNum}}</span>
                <span>{{item.RecyclingRate}}</span>
            </div>
        </div>
        <div v-else v-for="(item,index) in topform" :key="index" class="topscrol-box padding" :style="`background:${$colorlist()[index]}`">
            <p>{{item.GarbageNames}}</p>
            <p>{{item.GarbageNumber }}</p>
        </div>
    </el-scrollbar>
    <div v-else-if="statistics.includes(getform.Type)" class="topboxs">
        <ecahrtline v-if="barlist.includes(getform.Type)" :ids="'waterbir'" :options="optionvalue" :show="1"></ecahrtline>
        <inversion v-if="getform.Type=='大型机械保养记录表'" ref="inversion" :ids="'bigtable'" :types="types" :options="optionvalue"></inversion>
        <div v-for="(item,index) in rightlist" :key="index" class="padding" :style="`background:${topcolor[index]}`">
            <p v-for="(items,i) in rightform" :style="getclass(i)" :key="i">{{comname(item,items,index,i)}}</p>
        </div>
    </div>
    <div v-else-if="getform.Type=='严重污染天气记录表'" class="allbox">
        <workecharts :ids="'AQIid'" :options="optionvalue" ></workecharts>
        <div class="allbox-right" >
            <span v-for="(item,index) in $labelist(getform.Type)" :key="index" :class="`allbox-p p${index}`">{{item.name}}</span>
            <div v-for="(its,i) in PolluteWeatherTable" :key="i" class="allbox-bar"
                 :style="`background:${its.Color1}`">
                <span v-for="(item,index) in $labelist(getform.Type)" :title="its[item.value]" :key="index" :class="`p${index}`">{{its[item.value]}}</span>
            </div>
        </div>
    </div>
    <swiper v-else class="swiperlines"  :slides-per-view="5" :space-between="10"
        :autoplay="{ disableOnInteraction: false }" :modules="modules" navigation
      >
      <swiper-slide class="picimgs" v-for="(item, index) in piclist" :key="index">
          <img v-lazy="item.FileImg" :key="index" @click="preview(item.FileImg)" alt=""
           class="cursor" style="height:100%;width:100%">
          <div class="teacher_pW">{{item.index+1}}</div>
      </swiper-slide>
    </swiper>
    <h3  v-if="bottable.includes(getform.Type)">{{forms.toptwo}}</h3>
    <tablelist v-if="bottable.includes(getform.Type)" ref="tablelists" class="listpie"></tablelist>
    <div class="echartdom" v-else>
        <el-scrollbar class="check" v-if="checkbox.includes(getform.Type)" >
            <el-checkbox-group v-model="getform.MaterialType" @change="getcheck">
                <el-checkbox v-for="(item,index) in checklist" :key="index" :label="item" :value="item" />
            </el-checkbox-group>
        </el-scrollbar>
        <el-select  v-model="getform.Dates"  placeholder="请选择"  size="small"  style="width: 100px" @change="optionchan">
            <el-option  v-for="item in options"  :key="item"  :label="item"  :value="item"/>
        </el-select>
        <echarts ref="echartsss" v-if="falgeecharts" :forms="forms"></echarts>
    </div>
    <picimg ref="picimg"></picimg>
  </div>
</template>

<script>

import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/scss';
import 'swiper/scss/navigation';
import 'swiper/scss/pagination';

import { onMounted, ref, watch,getCurrentInstance,nextTick } from 'vue';
import picimg from "@/components/connet/Common/picimg.vue";
import echarts from "./echarts.vue";
import inversion from "./inversion";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import ecahrtline from "@/components/connet/technology/ecahrtline/ecahrtline.vue";
import workecharts from "@/components/connet/personnelcon/echarts3d/workerkq.vue";
import tablelist from "./tablelist";

export default {
props:['getforms'],

components: {
  Swiper,
  SwiperSlide,
  picimg,
  echarts,
  ecahrtline,
  inversion,
  workecharts,
  tablelist
},
setup(porps){
  const $http = getCurrentInstance().appContext.config.globalProperties.$http
  const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist
  const $colorlist = getCurrentInstance().appContext.config.globalProperties.$colorlist


    let piclist=ref([])
    let forms=ref({
        Type:porps.getforms.Type,
        data:[],
        falge:0,
        img:'',
        topone:'',
        toptwo:''
    })
    let picimg=ref(null)
    let getform=ref(
        {
        ProjectCode:store.getters.code,
        Dates:'近一年',
        Type:porps.getforms.Type,
        TypeValue:porps.getforms.Type,
        BZTTypeValue:'',
        MaterialType: []
        }
    )
    let url=ref('')
    let options=['近一年','近六月','近三月']
    let echartsss=ref(null)
    let labellist=['现场扬尘控制洒水记录表','隔油池清理记录表','化粪池清理记录表','施工现场移动厕所清理记录表',
    '有毒有害垃圾管理记录表','项目用水记录及工程用水总量汇总表','施工用电记录表及工程用电总量汇总表','大型机械保养记录表',
    '石化气燃料使用台账表','可回收建筑垃圾管理记录表','雨水回用记录表'
    ]//顶部没有布置图
    let statistics=['项目用水记录及工程用水总量汇总表','基坑降水收集记录表','中水回用记录表','雨水回用记录表',
    '施工用电记录表及工程用电总量汇总表','大型机械保养记录表','石化气燃料使用台账表'
    ]//有3d饼状图
    let toplist=['可回收建筑垃圾管理记录表','有毒有害垃圾管理记录表']
    let checkbox=['可回收建筑垃圾管理记录表']
    let barlist=['项目用水记录及工程用水总量汇总表','施工用电记录表及工程用电总量汇总表']//是否是3d饼状图
    let toplabele=['#A03FF2','#434FE6','#2692FF','#0DBBFF','#339900','#F7980B','#F75380','#941568',]
    let filelist=['生活、办公垃圾外运记录台账','生活垃圾外运记录表']
    let topform=ref([])
    let btns=ref('布置图')
    let source=ref(null)
    let checklist=ref([])
    let times=ref('')
    let rightlist=ref([])
    let topcolor=ref(['#167EF3','#6054F6','#30C6FC'])
    let rightform=ref([])
    let optionvalue=ref([]);
    let labellisttop=[]
    let inversion=ref(null)
    let falgeecharts=ref(true)
    let types=ref({
        type:'bar',
        colorlist:[{
           name:'#006600',
           color:'#66FF00',
           type:'bar'
           },{
           name:'#9900FF',
           color:'#FFCCFF',
           type:'bar'
           },{
           name:'#0000FF',
           color:'#00FFFF',
           type:'bar'
           },]
    })
    let PolluteWeatherTable=ref([]);
    let bottable=['洒水记录表','餐具消毒记录表','施工现场消毒记录表']
    let tablelists=ref(null)
    let picfiles=['扬尘监控测量记录表','PM2.5、PM10监控测量记录表','噪声监控测量记录表','污水监控测量记录表','现场扬尘控制洒水记录表',
    '隔油池清理记录表','化粪池清理记录表','施工现场移动厕所清理记录表'
    ]//布置图
    onMounted(()=>{
        // 如果初始化时就是可回收建筑垃圾管理记录表，则获取数据并设置默认全选
        if (porps.getforms.Type === '可回收建筑垃圾管理记录表') {
            console.log('组件初始化时检测到可回收建筑垃圾管理记录表，开始获取数据');
            setTimeout(() => {
                gettop();
            }, 100);
        }
    })
    const showdelog=(val)=>{
        // console.log('出发',porps.forms);
        
        getform.value.Type=val.Type
        getform.value.TypeValue=val.Type
        getform.value.BZTTypeValue=val.Type
        forms.value.Type=val.Type
        btns.value='查看'
        forms.value.img=''
        forms.value.topone=val.Type.split('记录表')[0]+'布置图'

        if (labellist.includes(val.Type)) {
            btns.value=''
            forms.value.topone=val.Type.split('记录表')[0]
        }
        if (source.value) {
            source.value.cancel("取消")
        }
        times.value=''
        url.value=''
        // forms.value.topone=val.Type.split('记录表')[0]+'布置图'
        forms.value.toptwo=val.Type
        // console.log('图表数据显示',val);
         switch (val.Type) {
             case '扬尘监控测量记录表':
             case '噪声监控测量记录表':
             case '污水监控测量记录表':
             case '现场扬尘控制洒水记录表':
             case '隔油池清理记录表':
             case '化粪池清理记录表':
             case '施工现场移动厕所清理记录表':
                //  forms.value.topone=val.Type.split('记录表')[0]+'布置图'
                 url.value='GetMeasureRecordChartByDate'
                 getpic()
                 break;
             case 'PM2.5、PM10监控测量记录表':
                 url.value='Getpm25MeasureRecordChartByDate'
                 getpic()
                 break;
            case '生活垃圾外运记录表':
            case '生活、办公垃圾外运记录台账':
                forms.value.topone=val.Type=='生活垃圾外运记录表'?'生活垃圾外运照片':'生活、办公垃圾外运照片'
                btns.value=val.Type=='生活垃圾外运记录表'?'生活垃圾外运协议':'生活、办公垃圾外运协议'

                getform.value.Type='生活、办公垃圾外运记录台账'
                getform.value.TypeValue='生活、办公垃圾外运记录台账'
                getform.value.BZTTypeValue='生活、办公垃圾外运记录台账文件'
                url.value='GetGarbageTransportCharts'
                 getpic()
                 break;
            case '可回收建筑垃圾管理记录表':
                //  btns.value='施工渣土、建筑废弃物等处置手续'
                 url.value='GetRecyclableWasteChartByDate'
                 times.value=300
                 gettop()
                 break;
            case '有毒有害垃圾管理记录表':
                 btns.value=''
                 url.value='GetHarmOfficeWasteChartByDate'
                //  getpic()
                 break;
            case '项目用水记录及工程用水总量汇总表':
                rightform.value=$labelist(val.Type)
                 btns.value=''
                 url.value='GetUsingWaterRecordCharts'
                 break;
            case '基坑降水收集记录表':
                rightform.value=$labelist(val.Type)
                 btns.value=''
                 url.value='GetPrecipitateRecordCharts'
                 break;
            case '中水回用记录表':
                rightform.value=$labelist(val.Type)
                 btns.value=''
                 url.value='GetReclaimeWaterRecordCharts'
                 break;
            case '雨水回用记录表':
                rightform.value=$labelist(val.Type)
                forms.value.topone='雨水回用统计'
                 btns.value=''
                 url.value='GetRainWaterRocordCharts'
                 break;
            case '施工用电记录表及工程用电总量汇总表':
                rightform.value=$labelist(val.Type)
                 btns.value=''
                 url.value='GetUsingElectRecordCharts'
                 break;
            case '大型机械保养记录表':
                topcolor.value=['#2C6CDF','#B259DF','#2B9800']
                forms.value.topone='大型机械保养数据统计'
                rightform.value=$labelist(val.Type)
                btns.value=''
                 url.value='GetMaintainRecordCharts'
                 break;
            case '石化气燃料使用台账表':
                topcolor.value=['#2C6CDF','#B259DF','#2B9800']
                forms.value.topone='石化气燃料使用统计'
                rightform.value=[
                    {
                    name:'',
                    value:'name'
                    },{
                    name:'',
                    value:'value'
                    },
                ]
                btns.value=''
                 url.value='GetGasFuelLedgerCharts'
                 break;
            case '严重污染天气记录表':
                forms.value.topone='AQI空气质量指数'
                btns.value=''
                url.value='GetPolluteWeatherCharts'
                 break;
            case '洒水记录表':
                forms.value.topone='洒水过程照片'
                btns.value='查看'
                getpic()
                // getimgcd()
                nextTick(()=>{
                    tablelists.value.showdelog(val)
                })
                 break;
            case '餐具消毒记录表':
                forms.value.topone='餐具消毒照片'
                btns.value='查看'
                getpic()
                // getimgcd()
                nextTick(()=>{
                    tablelists.value.showdelog(val)
                })
                 break;
            case '施工现场消毒记录表':
                forms.value.topone='施工现场消毒照片'
                btns.value='查看'
                getpic()
                // getimgcd()
                nextTick(()=>{
                    tablelists.value.showdelog(val)
                })
                 break;
            
                 
         }
        const CancelToken = $http.CancelToken;
    	source.value = CancelToken.source();
        setTimeout(()=>{
        if (!bottable.includes(val.Type)) {
            getlines()
            
        }
        },times.value)
    }
    const optionchan=(val)=>{
        // console.log('选择',val);
        getform.value.Dates=val
        getlines()
    }
    const getlines=async()=>{
        let list=[]
        let labes={}
        falgeecharts.value=false
        const {data:res}=await gettable(url.value,getform.value,source.value)
        // console.log('表格',res.data);
        
        forms.value.data=res.data
        falgeecharts.value=true

        if (res.code=='1000') {
        if (Array.isArray(res.data)) {
            let obj={}
            switch (getform.value.Type) {
                case '扬尘监控测量记录表':
                obj.yclist=res.data
                    break;
                case '噪声监控测量记录表':
                obj.zylist=res.data
                obj.zylist1=[]
                    break;
                case '污水监控测量记录表':
                obj.wslist=res.data
                obj.wslist1=[]
                    break;
                default:
                obj.echart=res.data
                    break;
            }
            forms.value.data=obj
            forms.value.falge=0
        }else {
            switch (getform.value.Type) {
                case '有毒有害垃圾管理记录表':
                    // console.log('有毒有害垃圾管理记录表',res.data);
                    
                    forms.value.data=res.data.WasteList[0]
                    forms.value.WasteList=res.data.WasteName
                    topform.value=res.data.WasteNumByNameList
                    break;
                case '项目用水记录及工程用水总量汇总表':
                case '施工用电记录表及工程用电总量汇总表':
                    rightlist.value=res.data.WaterTotalInfo
                    forms.value.data=res.data.UsingWaterByEquip[0]
                    forms.value.WasteList=res.data.EquipList
                    optionvalue.value= res.data.WaterCharts.map((item,index)=>{
                    return {
                        name:item.name,
                        value:item.value,
                        itemStyle: {
                                color: topcolor.value[index],
                            },
                        }
                    })
                    break;
                case '基坑降水收集记录表':
                    labellisttop=[
                        {
                        name:'降水收集次数：',
                        value:''
                        },{
                        name:'近期抽水：',
                        value:''
                        },{
                        name:'近期收集：',
                        value:''
                        },
                    ]
                    labes={
                        name:'PrecipitateStatics',
                        echarts:'PrecipitateEcharts',
                        max1:'MaxTime',
                        max2:'MaxPrecipitate',
                        lablelist:'ItemList',
                        laberbar:'降水量/抽水量（m³）',
                        laberline:'降水时间（小时）'
                    }
                    comdata(res.data,labes,list)
                    break;
                case '中水回用记录表':
                    labellisttop=[
                        {
                        name:'用水次数：',
                        value:'平均用水：'
                        },{
                        name:'月度用水：',
                        value:'年度用水：'
                        }
                    ]
                    labes={
                        name:'ReclaimeWaterStatics',//顶部多个框的数据
                        echarts:'ReclaimeWaterEcharts',//图表的数据
                        max1:'MaxTotalUsingWater',//柱状图
                        max2:'MaxUsingWater',//折线图
                        lablelist:'ItemList',//lablex显示
                        laberbar:'本次用水量（m³）',//左侧y轴数据
                        laberline:'累计用水量（m³）'//右侧y轴数据
                    }
                    comdata(res.data,labes,list)
                    break;
                case '雨水回用记录表':
                    labes={
                        name:'RainWaterRocordStatics',//顶部多个框的数据
                        echarts:'RainWaterRocordEcharts',//图表的数据
                        max1:'MaxTotalUsingWater',//柱状图
                        max2:'MaxUsingWater',//折线图
                        lablelist:'ItemList',//lablex显示
                        laberbar:'本次用水量（m³）',//左侧y轴数据
                        laberline:'累计用水量（m³）'//右侧y轴数据
                    }
                    res.data.ItemList=["生活区","办公区","施工现场","生活区总数","办公区总数","施工现场总数"]
                    comdata(res.data,labes,list)
                    break;
                case '大型机械保养记录表':
                    optionvalue.value=res.data.MaintainCharts
                    labes={
                        name:'EquipMaintainInfo',//顶部多个框的数据
                        echarts:'MaintainRecordByEquip',//图表的数据
                        max1:'',//柱状图
                        max2:'',//折线图
                        lablelist:'ItemList',//lablex显示
                        laberbar:'',//左侧y轴数据
                        laberline:''//右侧y轴数据
                    }
                    inversion.value?.showdelog(forms.value)

                    comdata(res.data,labes,list)
                    break;
                case '石化气燃料使用台账表':
                    // console.log('石化气',res.data);
                    labes={
                        name:'',//顶部多个框的数据
                        echarts:'GasFuelLedgerList',//图表的数据
                        max1:'',//柱状图
                        max2:'',//折线图
                        lablelist:'ItemList',//lablex显示
                        laberbar:'燃料使用量（L）',//左侧y轴数据
                        laberline:'液化气使用量（L）'//右侧y轴数据
                    }
                    comdata(res.data,labes,list)
                    let namelist=[
                        {
                        name:'汽油用油量',
                        value:'qyCount'
                        },{
                        name:'柴油用油量',
                        value:'cyCount'
                        },{
                        name:'液化气使用量',
                        value:'yhqCount'
                        },
                    ]
                    rightlist.value=namelist.map((item,index)=>{
                        return {
                            name:item.name,
                            value:res.data[item.value]
                        }
                    })
                    break;
                case '严重污染天气记录表':
                    optionvalue.value=res.data.PolluteCharts
                    PolluteWeatherTable.value=res.data.PolluteWeatherTable
                    labes={
                        name:'',//顶部多个框的数据
                        echarts:'AQIeCharts',//图表的数据
                        max1:'',//柱状图
                        max2:'',//折线图
                        lablelist:'',//lablex显示
                        laberbar:'',//左侧y轴数据
                        laberline:''//右侧y轴数据
                    }
                    comdata(res.data,labes,list)
                    break;
            }
            forms.value.falge=1
        
        }
        nextTick(()=>{
        echartsss.value?.showdelog(forms.value)
        })
        }else{
            // falgeecharts.value=false

        }

    }
    const comdata=(val,lable,lists)=>{
        // console.log('多个数据处理',val,lable,lists);
        rightlist.value=val[lable?.name]
        let keys1=Object.keys(val[lable.echarts][0])
        keys1.splice(0,1)
        let objs1={}
        keys1.forEach((its,i)=>{
            objs1[its]=val[lable.echarts].map((item,index)=>{
                return {
                    name:item.name,
                    value:item[its],
                }
            })
        })
        forms.value.WasteList=val[lable?.lablelist]
        forms.value.data=objs1
        forms.value.max1=val[lable.max1]//柱状图
        forms.value.max2=val[lable.max2]//折线图
        forms.value.laberbar=lable.laberbar
        forms.value.laberline=lable.laberline
        // }

        // console.log('顶部',objs1);
    }
    // 计算顶部盒子
    const comname=(item,items,index,i)=>{
        let names=item[items?.value]
        // console.log('计算',item,items,index,i);
        
        switch (getform.value.Type) {
            case '基坑降水收集记录表':
                if (i==1) {
                    names= labellisttop[index]?.name+item[items.value]
                }
                break;
            case '中水回用记录表':
                if (i==1) {
                    names= labellisttop[index]?.name+item[items.value]
                }
                if (i==3) {
                    names= labellisttop[index]?.value+item[items.value]
                }
                break;
            case '雨水回用记录表':
                if (i==0) {
                    names=item[items.value]+'累计收集雨水量'
                }
                break;
        }
        return `${items.name+names}`
    }
    // 获取照片
    const getpic=async()=>{
        const {data:res}=await gettable('GetProtectImgTable',getform.value)
            // console.log('获取图片',res);
            piclist.value=res.data
            if (res.code=="1000") {
                piclist.value.map((item,index)=>{
                    item.index=index
                })
            }
            if (!labellist.includes(getform.value.Type)) {
                getimgcd()
            }
            // console.log('获取照片',piclist.value);
    }
    const getclass=(index)=>{
        switch (getform.value.Type) {
            case '基坑降水收集记录表':
            case '中水回用记录表':
            case '雨水回用记录表':
            case '大型机械保养记录表':
            // case '石化气燃料使用台账表':
                if (index=='0') {
                    return `grid-column: 1/span 2;`
                }
                if (index=='2') {
                    return `font-size: 18px;font-weight: bold;`
                }
                break;
            case '石化气燃料使用台账表':
                // console.log('石化',index);
                
                if (index=='0') {
                    return `grid-column: 1/span 2;font-size: 18px;font-weight: bold;`

                }
                if (index=='1') {
                    return `grid-column: 1/span 2;font-size: 18px;font-weight: bold;text-align: center;`
                }
                break;
        }
        return ``

    }
    const getcolor=(val,index)=>{
        // console.log('获取颜色',index);
        return `background:${toplabele[index]}`
    }
    // 获取可回收建筑垃圾顶部数据
    const gettop=async()=>{
        const {data:res}=await gettable('GetRecyclableWasteMaterialInfo',getform.value)
        console.log('获取顶部数据',res);
        if (res.code=="1000") {
            topform.value= res.data.MaterialInfo
            checklist.value=res.data.MaterialType
            // 默认全选所有材料类型
            await nextTick(() => {
                getform.value.MaterialType=[...res.data.MaterialType]
                console.log('设置默认全选材料类型:', getform.value.MaterialType);
            });

        }
    }
    // 获取多选可回收垃圾
    const getcheck=(val)=>{
        console.log('用户选择的材料类型:', val);
        getform.value.MaterialType=val
        getlines()
    }
    // 获取测点图数据GetCDProtectImgTable
    const getimgcd=async()=>{
        // const {data:res}=await gettable('GetCDProtectImgTable',getform.value)
        const {data:res}=await gettable('GetCDProtectImgTableBZT',getform.value)
            // console.log('获取测点图数据',res);
            if (res.code=="1000") {
                forms.value.img=res.data.FileImg
            }
    }
    const preview=(val)=>{
        let imgtype=['jpg','png','Jpeg']
      if (val) {
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1)
          if (imgtype.includes(file)) {
              picimg.value.piclist(val)
          }else{
            window.open('https://f.zqface.com/?fileurl='+val,'_slef')
          }
            }
	  }
    return{
        modules:[ Pagination, Navigation],
        piclist,
        forms,
        options,
        getform,
        picimg,
        url,
        falgeecharts,
        echartsss,
        labellist,
        toplist,
        toplabele,
        topform,
        btns,
        source,
        checklist,
        times,
        statistics,
        rightlist,
        topcolor,
        rightform,
        optionvalue,
        labellisttop,
        barlist,
        inversion,
        types,
        PolluteWeatherTable,
        bottable,
        tablelists,
        filelist,
        picfiles,
        checkbox,

        gettop,
        getlines,
        preview,
        getpic,
        optionchan,
        showdelog,
        getcolor,
        getimgcd,
        getcheck,
        getclass,
        comdata,
        comname,
        // getifream

    }
}
}
</script>
<style lang="scss">
.swiperlines{
    .swiper-wrapper{
        display: flex!important;
        flex-direction: row!important;
    }
    .picimgs{
        width: 260px;
        height: 100%!important;
        position: relative;
    }
    .swiper-button-prev, .swiper-button-next{
        top:var(--swiper-navigation-top-offset, 32%)
    }
}
</style>
<style lang="scss" scoped>
.titles{
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
}
.echartpic{
    width: 100%;
    &-one{
        display: flex;
    }
}
.echartdom{
    width: 100%;
    height: 80%;
    margin-top: 10px;
}
.el-select{
    position: absolute;
    right: 2%;
    z-index: 20;
}
.topscrol{
    height: 140px!important;
    &-box{
        color: #fff;
        height: 80px;
        width: 100px;
        margin: 10px;
        font-size: 14px;
        border-radius: 10px;
        p:nth-child(2){
            font-size: 30px;
            font-weight: bold;
            text-align: center;
        }
    }
}
.check{
    height: 35px!important;
    width: 500px;
    position: absolute;
    z-index: 20;
    left: 35%;
}
.el-checkbox-group{
    display: flex;
}
.el-checkbox{
    flex-shrink: 0;
}
.box1{
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    width: 150px;
    color: #000!important;
    p{
        text-align: end;
        margin: 8px;
    }
}
.box2{
    display: grid;
    grid-template-columns: repeat(2,50%);
    align-items: center;
    justify-items: start;
    padding: 0 10px;
    span:nth-child(1){
        font-size: 18px;
        font-weight: bold;
        grid-row: 1;
        grid-column: 1/span 2;
    }
    span:nth-child(4){
        grid-column: 2;
        grid-row: 2/span 3;
        font-weight: bold;
    }   
}
.box{
    height: 100px;
    width: 150px;
    margin: 10px;
    flex-shrink: 0;
    color: #fff;
}

.swiperlines{
    height: 150px!important;
    width: 100%!important;
    .teacher_pW{
        position: absolute;
        top: 0;
        left: 0;
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        background: #fff;
        border: 1px solid #ccc;
        border-radius: 100%;
    }
}
.topboxs{
    display: grid;
    grid-template-columns: repeat(4,25%);
    height: 150px;
    color: #fff;
    .padding{
        display: grid;
        grid-template-columns: 40% 60%;
        align-items: center;
    }
    div{
        margin: 10px;
        width: 90%;
        height: 90%;
        p{
            margin: 5px;
        }
        p:nth-child(1){
            font-size: 18px;
            font-weight: bold;
        }
        p:nth-child(3){
            grid-column: 1;
            grid-row: 2/span 2;
        }
    }
}
@mixin grid(){
    display: grid;
    grid-template-columns: repeat(auto-fill,10%);
    gap: 0;
}
.allbox{
    display: grid;
    grid-template-columns: 30% 70%;
    &-right{
        height: 200px;
        @include grid;
        .p5{
            grid-column: span 5;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    &-p{
        height: 25px;
        font-size: 14px;
        font-weight: bold;
    }
    &-bar{
        height: 25px;
        width: 100%;
        border-radius: 20px;
        padding: 3px 10px;
        grid-column: 1/span 10;
        color: #fff;
        font-size: 12px;
        span{
        line-height: 20px;

        }
        @include grid;

    }
}
h3{
    margin: 10px;
}

</style>