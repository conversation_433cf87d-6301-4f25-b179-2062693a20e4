<template>
<!-- 质量安全 -->
  <div class="model"  >
    <div class="leftmodel left wid" v-show="amplify==0">
        <quality class="Homebgco" 
        :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`"  :homeindex="'1'"></quality>
         <teamslistss  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :teamtype="teamtype"></teamslistss>
        <Majorhazard  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`"></Majorhazard>
    </div>
    <div class="homecontent" :style="amplify==0?'width:60%':'width:100%'" >
      <home  @getamplify="getamplify2"></home>
    </div>
    <div class="rightmodel right wid"  v-show="amplify==0">
        <secure class="Homeright" :style="`background:linear-gradient(90deg,
         rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
         :homeindex="4"></secure>
        <safetyhazards  class="Homebgco" :style="`background:linear-gradient(90deg,
         rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`"></safetyhazards>
        <ConstructionLog class="Homeright" 
            :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
            :form="form"
            ></ConstructionLog>
    </div>

  </div>
</template>

<script>

import ConstructionLog from "@/components/connet/technology/ConstructionLog.vue";
import quality from "@/components/connet/Home/quality.vue";
import teamslistss from "@/components/connet/personnelcon/teamslist.vue";
import Majorhazard from "@/components/connet/safety/Majorhazard.vue";
import safetyhazards from "@/components/connet/safety/safetyhazards.vue";
import secure from "@/components/connet/Home/secure.vue";
import home from "@/components/connet/safety/content/home.vue";

import { onMounted, ref ,computed} from 'vue';

export default {
components:{

        safetyhazards,
        ConstructionLog,
        quality,
        teamslistss,
        Majorhazard,
        secure,
        home,

    },
setup(){
    let themelist=ref([])

    let bgcolor=ref({})
    let amplify=ref(0)
    let teamtype=ref({
        src:require('@/assets/img/safety/quresafe.png'),
        titles:'质量问题类型分析',
        type:'disclosure',
        ids:'questiontype'
    })
    let form=ref({
      titles:'安全日志',
    })
    window.addEventListener('setItem', ()=> {
      // console.log('项目引导');
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
   })
   window.addEventListener('setthcolor', ()=> {
      // console.log('主题切换');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(()=>{
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    

    })
    const getamplify2=(val)=>{
      // console.log('放大',val);
      
      amplify.value=val
    }
    return{
        // materialtype,
        bgcolor,
        themelist,
        teamtype,
        amplify,
        form,
        // teamtype1,
        // bigcew1,
        // Typeworlist,
        getamplify2
    }
}
}
</script>
<style lang="scss" scoped>
@mixin homeflex($deg) {
  height: 31.3%;
  width: 95%;
  opacity: 1;
  margin:7px 10px;
  background: linear-gradient($deg, #0096C7 7%, rgba(0,52,75,0.00) 97%);
}
@mixin leftdjx($left,$right,$top,$bottom,) {
  position: absolute;
  content: '';
  display: block;
  left: $left;
  top: $top;
  right: $right;
  bottom: $bottom;
  width: 10.06px;
  height: 10.84px;
  opacity: 1;
}

.Homebgco{
  position: relative;
  @include homeflex(90deg)
  }
.Homeright{
  position: relative;
  @include homeflex(-90deg);

}
</style>
<style lang="scss" scoped>
.model{
  width: 100%;
  height: 100%;
  display: flex;
  .wid{
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .homecontent{
    width: 60%;
    height: 100%;
    // position: relative;
  }
}
</style>