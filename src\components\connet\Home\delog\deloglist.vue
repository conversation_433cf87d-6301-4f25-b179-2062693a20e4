<template>
  <div class="eqments">
    <div class="eqment">
        <div v-for="(item,index) in topset" :key="index" @click="change(item,index)"
         :class="['eqment-one cursor',{'changindex':falge==index}]"
         v-show="topname=='卸料'?index!=2:item"
        :style="[falge==index?`background:${bgcolor.changcolor};color:${bgcolor.font}`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
         rgba(7, 93, 184, 0.6) 100%);color:#FFF`]"
        >
            <p class="eqment-one-p">{{item}}</p>
        </div>
    </div>
    <div class="eqment-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
         <div class="eqment-body-one">
            <img src="@/assets/img/home/<USER>" alt="">
            <p class="eqment-body-one-p">{{titlename+falgename}}</p>
        </div>
    </div>
    <div class="eqment-content" v-if="falgename=='维保记录'" >
        <div class="eqment-content-one eqment-title pmss" :style="[`color:${bgcolor.font}`]">
            <div v-for="(item,index) in foundation" :key="index" :class="['eqment-title-p','p'+index]">
                <p>{{item.name}}：{{formes[item.value]}}</p>
            </div>
        </div>
        <el-scrollbar height="400px">
            <div class="eqment-content-one eqment-two" v-for="(ies,i) in formes.MaintainInfo" :key="i"
            :style="[`color:${bgcolor.font};background: rgba(${bgcolor.delogcolor}, 0.33);`]">
                <div v-for="(item,index) in detils" :key="index">
                    <p>{{item.name}}：{{ies[item.value]}}</p>
                </div>
            </div>
        </el-scrollbar>

    </div>
    <div v-else-if="falgename=='实时数据'">
        <div  class="Realtime" v-for="(ite,s) in 2" :key="s" >
            <div class="DeviceName" v-if="topname=='塔式起重机'?s<1:2">
                <img :src="formes.HeadImage" alt="" style="width:230px;height:270px">
                <div class="DeviceName-p" v-for="(item,index) in tawter" :key="index" :style="[`color:${bgcolor.font};`]">
                    <p>{{item.name}}：{{formes[item.value]}}</p>
                </div>
            </div>
            <div v-if="topname=='施工升降机'" class="lefttop" :style="`background:rgba(${bgcolor.delogcolor},0.6)`">{{s==0?'LEFT':'RIGHT'}}</div>

        </div>
        
    </div>
    <div v-else-if="falgename=='设备信息'">
        <div class="eqmentname">
            <div class="eqmentname-one">
                <div class="DeviceName-p" v-for="(item,index) in eqmentname" :key="index"
                :style="[`color:${bgcolor.font};`]" v-show="topname=='施工升降机'?(index!=9&&index!=12):(topname=='卸料'?index<=8:item)">
                    <p>{{topname=='施工升降机'&&(index==11||index==13)?item.name1:item.name}}：
                        {{topname=='施工升降机'&&(index==11||index==13)?formes[item.value1]:formes[item.value]}}</p>
                </div>
                <div v-if="topname=='卸料'" class="DeviceName-p" :style="[`color:${bgcolor.font};`]">
                    <p>额定限载</p>
                </div>
            </div>
            <div class="eqmentname-right">
                <div class="pic">
                    <img :src="formes.EquipPhoto" alt="" style="width:100%;height:100%">
                </div>
                <div class="pic" v-if="topname=='施工升降机'">
                    <img :src="formes.EquipPhoto" alt="" style="width:100%;height:100%">
                </div>
            </div>
        </div>
        <div class="DeviceName-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
         <div class="DeviceName-body-one" >
            <img src="@/assets/img/home/<USER>" alt="">
            <p class="eqment-body-one-p">文件预览</p>
        </div>
        </div>
        <div class="prowe" v-for="(item,index) in formes.FileInfos" :key="index">
                <div class="prowe-one">
                    <el-icon class="ions"><Document /></el-icon>
                    <p>{{item.FileName}}</p>
                </div>
        </div>
    </div>
    <div  v-else-if="falgename=='安装信息'">
        <div class="DeviceName install pmss">
            <div v-for="(item,index) in install" :key="index"  :class="['DeviceName-p install-one','install'+index]"
             :style="[`color:${bgcolor.font};`]" v-show="topname=='塔式起重机'?index<11:topname=='卸料'?index<11:item">
                <p>{{item.name}}{{topname=='施工升降机'&&(index>=8&&index<12)?'(左)':topname=='施工升降机'&&(index>=12)?'(右)':''}}：
                {{topname=='施工升降机'&&(index>=6&&index<12)?formes[item.value1]:formes[item.value]}}</p>
            </div>
            <div v-if="topname=='卸料'" class="DeviceName-p" :style="[`color:${bgcolor.font};`]">
                <p>具体安装部位</p>
            </div>
        </div>
        <div class="DeviceName-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
         <div class="DeviceName-body-one">
            <img src="@/assets/img/home/<USER>" alt="">
            <p class="eqment-body-one-p">文件预览</p>
        </div>
        </div>
        <div class="prowe" v-for="(item,index) in formes.FileInfos" :key="index">
            <div class="prowe-one">
                <el-icon class="ions"><Document /></el-icon>
                <p>{{item.FileName}}</p>
            </div>
        </div>
    </div>
    <div class="waangings" v-else-if="falgename=='告警信息'">
        <div class="waanging">
            <div class="DeviceName-p waanging-one" v-for="(item,index) in waringlist" :key="index" :style="[`color:${bgcolor.font};`]">
                <p>{{item.name}}</p>
            </div>
        </div>
        <el-table :data="tableData"  :style="['width: 100%',`color:${bgcolor.font};
        --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
        :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
        empty-text="暂无数据" max-height="400px"
        >   
            <template #empty>
                <el-empty  v-loading="loading"></el-empty>
            </template>
            <el-table-column prop="rowNum" label="序号" width="180" align="center" />
            <el-table-column prop="warnStartTime" label="告警时间" width="180"  align="center"/>
            <el-table-column prop="WarningType" label="告警级别" align="center" />
            <el-table-column prop="WarningType" label="告警/违章类型" align="center" />
            <el-table-column prop="WarningDescribe" label="告警/违章内容" align="center" />
        </el-table>
        <el-pagination
        v-model:current-page="getform.count"
        v-model:page-size="getform.page"
        :page-sizes="[100, 200, 300, 400]"
        class="pagepopr"
        :background="background"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        />
    </div>
    <div class="Disassembly pmss" v-else-if="falgename=='拆除信息'">
        <div v-for="(item,index) in Disassembly" :key="index"  
        :class="['Disassembly-one','Disassembly'+index]" :style="[`color:${bgcolor.font};`]">
            <p>{{item.name}}：{{formes[item.value]}}</p>
            <div class="cursor" v-for="(cout,its) in (index==1?2:1)" :key="its" @click="open(formes[item[`value${its+1}`]])"
            v-show="index==1&&((its==0&&formes[item.value1])||its==1&&formes[item.value2])||index==2&&formes[item.value1]">
                <i class="iconfont icon-PDF"></i>
                <span>{{index==1&&its==1?formes[item.name2]:formes[item.name1]}}</span>
            </div>
        </div>
    </div>
    <picimg ref="picimg"></picimg>
  </div>
</template>

<script>
import { onMounted, ref, toRef, watch,watchEffect,isProxy,toRaw ,getCurrentInstance, nextTick} from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import picimg from "@/components/connet/Common/picimg.vue";
import axios from 'axios';
export default {
props:['changname','datatable','formlist'],
components:{
        picimg
    },
setup(props){
    const $http = getCurrentInstance().appContext.config.globalProperties.$http
    const $moist = getCurrentInstance().appContext.config.globalProperties.$moist

    let topset=ref(['设备信息','安装信息','实时数据','维保记录','告警信息','拆除信息'])
    let bgcolor=ref({})
    let falge=ref(0)
    let picimg=ref(null)
    let loading=ref(false)
    let falgename=ref(topset.value[0])
    let titlename=ref(props.changname)
    const background = ref(false)
    let topname=ref("")//选择种类状态
    let getform=ref({
        ProjectCode:props.formlist.ProjectCode,
        UsingPage:props.formlist.UsingPage,
        IconType:props.formlist.IconType,
        DetialType:'',
        EquipCode:'',
        page:1,
        count:10
    })
    let total=ref(0)
    let formes=ref({})
    let foundation=ref([
        {
        name:'设备类型',
        value:'EquipType'
        },{
        name:'设备名称',
        value:'EquipName'
        },{
        name:'设备编号',
        value:'EquipCode'
        },{
        name:'维修周期',
        value:'MaintainCycle'
        },{
        name:'地理位置',
        value:'CranePosition'
        },
    ])
    let detils=ref([
        {
        name:'维修时间',
        value:'OperateDate'
        },{
        name:'维修人员',
        value:'Submitter'
        },{
        name:'维修类型',
        value:'MaintainType'
        },{
        name:'维修部位',
        value:'MaintainPosition'
        },{
        name:'检查情况',
        value:'MaintainCondition'
        },{
        name:'维修结果',
        value:'MaintainMode'
        },
        // {
        // name:'地理位置',
        // value:''
        // },
    ])
    let tawter=[
        {
        name:'司机姓名',
        value:'CraneOperator'
        },{
        name:'证件号码',
        value:'IDCardNumber'
        },{
        name:'特种证编号',
        value:'CertificateNumber'
        },{
        name:'实时吊重',
        value:'Weight'
        },{
        name:'实时高度',
        value:'Height'
        },{
        name:'实时幅度',
        value:'Range'
        },{
        name:'实时转角',
        value:'Corner'
        },{
        name:'实时力矩比',
        value:'Torque'
        },{
        name:'实时风速',
        value:'WindSpeed'
        },{
        name:'实时倾角',
        value:'Angle'
        },{
        name:'今日开机时长',
        value:'StartUpTime'
        },{
        name:'今日工作时长',
        value:'WorkeringTime'
        },{
        name:'累计吊重',
        value:'SumWeight'
        },
    ]
    let eqmentname=[
        {
        name:'备案编号',
        value:'RecordCode'
        },
        {
        name:'设备名称',
        value:'EquipName'
        },{
        name:'规格型号',
        value:'Specifications'
        },{
        name:'生产厂家',
        value:'Manufactor'
        },{
        name:'设备编号',
        value:'EquipCode'
        },{
        name:'制造许可证',
        value:'license'
        },{
        name:'出厂时间',
        value:'QualifiedDate',
        // value1:'Production'
        },{
        name:'出厂编号',
        value:'QualifiedCode'
        },{
        name:'使用年限',
        value:'Years'
        },{
        name:'额定起重力矩',
        value:'Lifting'
        },{
        name:'最大独立起升高度',
        value:'MaxAloneHeight'
        },{
        name:'最大幅度',
        value:'MaxRange',
        name1:'额定提升速度',
        value1:'RatedSpeed'
        },{
        name:'附着最大起升高度',
        value:'MaxStickHeight'
        },{
        name:'最大额定起重量',
        value:'MaxWeight',
        name1:'额定载重量',
        value1:'Ratedload'
        },
    ]
    let install=[
        {
        name:'进场安装时间',
        value:'InstallationDate'
        },{
        name:'使用单位',
        value:'ContractorCorpName'
        },{
        name:'社会信用统一代码',
        value:'ContractorCorpCode'
        },{
        name:'使用项目名称',
        value:'ProjectName'
        },{
        name:'安装人员',
        value:'Installation'
        },{
        name:'检测单位',
        value:'TestingUnit'
        },{
        name:'安装位置',
        value:'CranePosition'
        },{
        name:'监控设备厂商',
        value:'Manufactor',
        value1:'LManufactor'
        },{
        name:'监控设备型号',
        value:'HardwareModel',
        value1:'LHardwareModel'
        },{
        name:'监控设备序列号',
        value:'HardwareSN',
        value1:'LHardwareSN'
        },{
        name:'出厂日期',
        value:'Production',
        value1:'LProduction'
        },{
        name:'监控设备厂商',
        value1:'RManufactor'
        },{
        name:'监控设备型号',
        value1:'RHardwareModel'
        },{
        name:'监控设备序列号',
        value1:'RHardwareSN'
        },{
        name:'出厂日期',
        value1:'RProduction'
        },
    ]
    let waringlist=[
        {
        name:'告警次数',
        value:''
        },{
        name:'司机违章次数',
        value:''
        },
    ]
    let Disassembly=[
        {
        name:'拆卸时间',
        value:'DismantleDate'
        },{
        name:'拆卸单位',
        value:'DismantleCompany',
        value1:'DismantleUnitCertificate',
        name1:"DismantleUnitCertificateName",
        value2:'DismantleUnitLicense',
        name2:"DismantleUnitLicenseName",
        },{
        name:'拆卸人员',
        value:'DismantleWorker',
        value1:'DismantleWorkerCertificate ',
        name1:"DismantleWorkerCertificateName",
        },{
        name:'出厂人员',
        value:'LeaveDate'
        },
    ]
    let tableData=ref([])
    let source =ref('')
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    // console.log('监听数据',props.changname);
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        getform.value.DetialType='设备信息'
        getdetil()
    })
    watch(()=>props.changname, (newVal, oldVal) => {
        // console.log('监听数据',props.formlist);
        if (newVal) {
            titlename.value=newVal
        }
      },
    {immediate: true})
    watch(()=>props.datatable, (newVal, oldVal) => {
        // console.log('监听数据',toRaw(newVal));
        if (newVal) {
            topname.value=newVal
        }
      },
    {immediate: true})
    watch(()=>props.formlist?.EquipCode, (newVal, oldVal) => {
        // console.log('监听传值数据',newVal);
        if (newVal) {
            getform.value.EquipCode=newVal
            nextTick(()=>{
            getdetil()
            })
            // topname.value=newVal
        }
      },
    {immediate: true})
    const open=(val)=>{
        // console.log('预览文件',val);
        let type=['png','jpg']
        if (val) {
            
        if (type.includes(val.substr(val.length - 3))) {
            // console.log('打开');
            picimg.value.piclist(val)
            
        }else{
            window.open('https://f.zqface.com/?fileurl='+val,'123')

        }
        }
    }
    const change=(val,index)=>{
        // this.cancel()
        source.value.cancel('请求超时')
        falge.value=index
        falgename.value=val
        getform.value.DetialType=val
        
        getdetil()
    }

    const getdetil=async()=>{
        source.value = axios.CancelToken.source();
        tableData.value=[]
        // const {data:res}=await gettable('GetEquipDetailMore',getform.value)
        const {data: res} = await $http.post($moist.httpsurl+'/Api.ashx?PostType=get&Type=GetEquipDetailMore'
        ,getform.value,{cancelToken: source.value.token})
        // console.log('返回数据',res);
        if (res.code=="1000") {
            formes.value=res.data
            tableData.value=res.data
            total.value=res.Total?res.Total:0
        }else{
            formes.value={}
        }
    }
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
    const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        }
    return{
        
        topname,
        tableRowClassName,
        titlename,
        getform,
        background,
        source,
        picimg,
        falge,
        loading,
        formes,
        topset,
        bgcolor,
        falgename,
        total,

        foundation,
        detils,
        tawter,
        eqmentname,
        install,
        waringlist,
        Disassembly,
        tableData,
        change,
        handleSizeChange,
        handleCurrentChange,
        getdetil,
        open,
        // getdetable

    }
}
}
</script>
<style lang="scss">
// :deep(.eqments){
    // .el-picker-panel{
    //     color: #fff!important;
    //     background: rgba(15, 43, 63, 1)!important;
    // }
    // .el-popper.is-light{
    //     color: #fff!important;
    //     background: rgba(15, 43, 63, 1)!important;
    //     border: 1px solid #46587c!important;
    // }
// }

</style>
<style lang="scss" scoped>
.eqments{
    color: #fff;
    .eqment{
        display: grid;
        grid-template-columns: repeat(6, 17%);
        justify-content: center;
        justify-items: center;
        // position: relative;
        &-one{
            border: 2px solid #03558F;
            width: 70%;
            height: 35px;
            opacity: 1;
            position: relative;

            &-p{
                height: 100%;
                line-height: 30px;
            }
        }
        &-title{
        // margin: 20px;
            grid-template-columns: repeat(4, 25%);
        &-p{
            padding: 10px;
        }
        }
        &-header{
            display: flex;
            margin: 10px;
            // margin-left: 4%;
            height: 50px;
        }
        &-body-one{
                display: flex;
                align-items: center;
                justify-content: center;
                &-p{
                    font-size: 20px;
                    font-weight: bold;
                }
            }
        &-content{
            font-weight: bold;
        }
        &-content-one{
            display: grid;
            // grid-template-columns: repeat(4, 25%);
            justify-content: center;
            justify-items: center;
            margin: 10px;
            padding: 10px;
        div{
            width: 100%;
            text-align: start;
            }
        }
        &-two{
            // margin: 10px;
            grid-template-columns: repeat(4, 25%);
            height: 90px;
            div{
                display: flex;
                align-items: center;
            }
             div:nth-child(7) {
                grid-column: span 2;
            }
        }
    }
    .pmss{
        margin: 10px;
        padding: 10px;
        margin-bottom: 0px;
        padding-bottom: 0px;
        .icon-PDF{
            font-size: 50px!important;
            color: #D81E06!important;
        }

    }
    .Realtime{
        position: relative;
        .lefttop{
           position: absolute;
            left: 25px;
            top: 0px;
            width: 98px;
            height: 34px;
            border-radius: 8px 8px 187px 8px;
            opacity: 1;
            font-weight: bold;
            line-height: 30px;
            font-size: 18px;
            // background: rgba(5, 215, 219, 0.8);
        }
    }
    .DeviceName{
        display: grid;
        grid-template-columns: repeat(4, 25%);
        justify-content: center;
        justify-items: center;
        margin-bottom: 20px;
        div{
            width: 100%;
            display: flex;
            align-items: center;
            // text-align: start;
        }
        img{
            grid-row: span 5;
        }
        &-p{
            font-size: 14px;
            font-weight: bold;
        }
        &-header{
            height: 35px;
            margin: 10px;
        }
        &-body-one{
            display: flex;
            align-items: center;
            height: 100%;

        }
        
    }

    .waangings{
        padding: 0px 20px;
    }
    .install{
        // margin: 10px;
        // padding: 10px;
        grid-template-columns: repeat(3, 33.3%)!important;
        div:nth-child(8) {
                // grid-column: span 2;
                // grid-area: myArea;
                // width: 50%;
            }
        &-one{
            height: 50px;
            // grid-template-rows:repeat(3, 50px) ;

        }
    }
    .waanging{
        display: grid;
        margin: 10px;
        padding: 10px;
        grid-template-columns: repeat(2, 50%);
        &-one{
            text-align: start;
        }
        
    }
    .eqmentname{
        display: flex;
        margin: 10px;
        padding: 10px;
        &-one{
            width: 80%;
            display: grid;
            // grid-template-columns: repeat(3, 33.3%);
            grid-template-columns: 40% 30% 30%;
            justify-content: center;
            justify-items: center;
            grid-template-rows:repeat(5, 50px) ;
            div{
            width: 100%;
            display: flex;
            align-items: center;
            // text-align: start;
        }
        }
        &-right{
            width: 22%;
            
        }
        .pic{
            width: 100%;
            height: 150px;
            border: 2px solid #ccc;
            margin: 5px;
            // grid-row: span 4;

        }
        &-p{
            font-size: 14px;
            font-weight: bold;
        }
    }
    .prowe-one{
                // display: flex;
                    display: flex;
                    align-items: center;
                    margin: 10px;
                .ions{
                    font-size: 40px;
                }
            }
    .changindex::before{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        left: -2px;
        top: -2px; 
        opacity: 1;
        border-top: 2px solid #E0A538;
        border-left: 2px solid #E0A538;
    }
    .changindex::after{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        right: -2px;
        bottom: -2px; 
        opacity: 1;
        border-bottom: 2px solid #E0A538;
        border-right: 2px solid #E0A538;
    }
    .Disassembly-one{
        margin: 10px;
        height: 50px;
        text-align: start;
        p{
            font-size: 14px;
            font-weight: bold;
        }
    }
    .Disassembly{
        display: grid;
        justify-content: start;
        grid-template-columns: 100%;
        .Disassembly1{
            height: 110px;
        }
    }
    
}
.install6{
    grid-column: 1/3 span;
}
.el-table{
    --el-table-tr-bg-color:transparent;
    // border:1px solid transparent!important;
    // --el-table-border-color:transparent;
    --el-table-row-hover-bg-color:transparent;
}
.el-table :deep(.warning-row) {
//   --el-table-tr-bg-color: #000 !important;
  background: rgba(15, 43, 63, 0.6)!important;
}
// .el-table :deep(.success-row) {
//   --el-table-tr-bg-color: #000 !important;
// }

.el-pagination{
    margin-top: 10px;
}
 .el-table,
  .el-table__expanded-cell {
  background-color: transparent !important;
//   color: #606266;
}
.el-pagination{
--el-pagination-bg-color:transparent !important;
--el-pagination-text-color:#fff!important;
--el-pagination-button-color:#fff!important;
--el-pagination-button-disabled-bg-color:transparent !important;
}
:deep(.el-pagination__total){
    color: #fff!important;
}

:deep(.el-pagination__jump){
    color: #fff!important;

}
.p4{
    grid-column: 1/3 span;
}
</style>