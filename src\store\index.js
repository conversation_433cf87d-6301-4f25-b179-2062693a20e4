import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import login from "@/store/modules/login";
// import loading from "@/store/modules/loading";

export default createStore({
  state: {
  },
  getters: {
    username: state => state.login.username,
    code: state => state.login.code,
    date:state => state.login.data,
    falgenav:state => state.login.falgenav,
    // isLoading: state => state.loading.isLoading
  },
  mutations: {
  },
  actions: {
  },
  modules: {
    login,
    // loading
  },
  // plugins: [createPersistedState({
  //   storage: window.sessionStorage,
  //   // key: "date"
  //   key: "store",
  //   // paths: ['date']
  //  })]
})
