<template>
  <div class="foundations">
    <div>
      <titiesbg :titles="'监测概况'"></titiesbg>
      <div class="foundations-two">
         <div class="prows cursor" v-for="(item,index) in btns" :key="index" @click="open(formlabe[item.value])">
             <img v-if="formlabe[item.value]" :src="require(`@/assets/img/construction/file/${getfile(formlabe[item.value])}.png`)" alt="">
             <span>{{item.name}}</span>
         </div>
      </div>
      <titiesbg :titles="'设备统计'"></titiesbg>
      <div id="eqmentid" class="eqmentid"></div>
      <titiesbg :titles="'报警统计'"></titiesbg>
      <div class="rightbom">
        <div id="Alarmstatistics"></div>
        <el-scrollbar ref="myScrollbar" style="height:245px">
            <div v-for="(item,index) in woring" :key="index" class="labelechart">
                <div class="icons" :style="`background:${WorkTypecolor[index]}`"></div>
                <span>{{item.name}}</span>
                <span>{{item.value2}}</span>
                <span>{{item.value}}</span>
            </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="bs-panel mt20 posting" style="height:630px">
      <div class="manager_detail" style="position: absolute">
          <canvas id="canvas" width="710" height="630" ref="canvas"></canvas>
      </div>
    </div>
    <deepdelog ref="deepdelog"></deepdelog>
  </div>
</template>

<script>
var canvas
var pointer
import { fabric } from 'fabric'
import titiesbg from "@/components/connet/Common/titiesbg.vue";
import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
import picimg from "@/components/connet/Common/picimg.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import deepdelog from "@/components/connet/construction/content/deepdelog.vue";
export default {
components:{
    titiesbg,
    deepdelog
  },
    setup(){
      let formlabe=ref({})
      let getform=ref({
          ProjectCode:store.getters.code,
          InUserName:store.getters.username,
          Type:1,
          MonitorID:''
      })
      let btns=ref([{
                name:'基坑监测方案',
                value:'JKjcfa'
            },{
                name:'基坑勘测报告',
                value:'JKjcbg'
            },{
                name:'基坑设计图纸',
                value:'JKsjtz'
            }])
      let yjcount=ref([])
      let woring=ref([])
      let deepdelog=ref(null)
      let WorkTypecolor=["#407fff",'#1F9DF5','#21F5D6','#5c2223','#eea2a4',
			"#a682e6",'#b598a1','#c08eaf','#813c85','#806d9e',
			"#e15d68",'#5e616d','#3170a7','#8fb2c9','#c3d7df',
			"#f29961",'#12a182','#737c7b','#92b3a5','#1a6840',
			"#00cccd",'#bec936','#373834','#5bae23','#e4bf11',
			"#dedede",'#b78d12','#f0d695','#b4a992','#fa5d19',
			"#FE8463",'#de7622','#f1908c','#207f4c','#22a2c3',
			"#9BCA63",'#815c94','#e16c96','#12a182','#bec936',
			'#D7504B', '#C6E579', '#F4E001', '#F0805A', '#26C0C0',
			'#FFB7DD', '#660077', '#FFCCCC', '#FFC8B4', '#550088',
			'#FFFFBB', '#FFAA33', '#99FFFF', '#CC00CC', '#FF77FF',
			'#C63300', '#9955FF', '#66FF66', '#129393', '#395203',
			'#C1232B', '#B5C334', '#FCCE10', '#E87C25', '#27727B',
			'#FAD860', '#F3A43B', '#60C0DD', '#0D7CAA']
      let bjcounts=ref([])
      let counts=ref([])
      let bardata=ref([])
      let postin=ref([])
      let urls=ref('')

      onMounted(()=>{
        getmorties()
        geteqments()
        getyjcout()
        // nextTick(()=>{
        //   getinit()
        // })
        getpmt()
      })
      const getfile=(val)=>{
             let filetype=''
             let lastIndex= val.lastIndexOf('.')
             let file=val.substring(lastIndex+1)
            switch (file) {
                case 'pdf':
                    filetype='pdf'
                    break;
                case 'docx':
                case 'doc':
                    filetype='word'
                    break;
                case 'xlsx':
                case 'xls':
                    filetype='excel'
                    break;
                case 'ppt':
                    filetype='ppt'
                    break;
            }
            
            return filetype
      }
      const open=(val)=>{
        if (val) {
          let imgtype=['png','jpg']
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1)
          if (imgtype.includes(file)) {
              picimg.value.piclist(val)
          }else{
              window.open('https://f.zqface.com/?fileurl='+val,'_slef')
          }
        }
      }
      // 获取监测概况
      const getmorties=async()=>{
          const {data:res}=await gettable('GetJKBaseInfo',getform.value)
          // console.log('获取概况',res);
            if (res.code=="1000") {
                formlabe.value=res.data
            }
      }
      // 设备统计
      const geteqments=async()=>{
          const {data:res}=await gettable('GetJKEquipStatistics',getform.value)
            if (res.code=="1000") {
                // console.log('获取设备统计数据',res);
                bardata.value=res.data
            }else{
                bardata.value=[]
            }
              geteqment()

      }
      const geteqment=()=>{
            var echarts = require('echarts');
			      let myChart = echarts.getInstanceByDom(document.getElementById("eqmentid"))
            if (myChart == null) {
                myChart = echarts.init(document.getElementById('eqmentid'));
            }
            let names
            let option = {
					// title: {
                    //         text: '单位：万元',
                    //         textStyle:{
                    //             color:'#fff'
                    //         }
                    //     },
              tooltip: {
                  trigger: 'axis',
                  axisPointer: {
                  type: 'shadow'
                  }
              },
              legend: {
                  textStyle:{
                      color:'#fff'

                  }
              },
              grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '3%',
                  containLabel: true
              },
              xAxis: {
                  type: 'value',
                  axisLine: {
                      lineStyle: {
                          color: '#fff' // 设置为红色
                      }
                  },
              },
              yAxis: {
                  type: 'category',
                  axisLine: {
                      lineStyle: {
                          color: '#fff' // 设置为红色
                      }
                  },

                  axisTick:{
                      color:'#fff'
                  },
                  data:bardata.value.name
              },
              series: [
                 {
                 name: '正常',
                 type: 'bar',
                 stack: 'total',
                 label: {
                     show: false
                 },
                 emphasis: {
                     focus: 'series'
                 },
                 data: bardata.value.value1
                 },
                 {
                 name: '异常',
                 type: 'bar',
                 stack: 'total',
                 color:'red',
                 label: {
                     show: false
                 },
                 emphasis: {
                     focus: 'series'
                 },
                 data: bardata.value.value2
                 }
              ]
				    };
        myChart.setOption(option);
        window.addEventListener("resize", function() {
          myChart.resize();
        });
      }
      // 报警统计
      const getyjcout=async()=>{
          const {data:res}=await gettable('GetWarningJKHighFormworkInfo',getform.value)
            // console.log('预警统计',res);
            if (res.code=="1000") {
                woring.value=res.data.ECharts
                yjcount.value=res.data.PointNum
                getworing()
            }
      }
      const getworing=()=>{
        // console.log('获取值',woring.value);
        
          var echarts = require('echarts');
			    let myChart = echarts.getInstanceByDom(document.getElementById("Alarmstatistics"))
          if (myChart == null) {
            myChart = echarts.init(document.getElementById('Alarmstatistics'));
          }
          let colorList=WorkTypecolor
          let option = {
					title: [{
							text: '总数量',
							textStyle: {
								color: "#fff",
								fontSize: 18,
							},
							itemGap: 10,
							left: "center",
							top: "54%",
						},
						{
							text: yjcount.value,
							textStyle: {
								color: "#fff",
								fontSize: 15,
								fontWeight: "normal",
							},
							itemGap: 10,
							left: "center",
							top: "40%",
						},
					],
					tooltip: {
						trigger: "item",
					},
					series: [{
						hoverAnimation: false,
						type: "pie",
						center: ["50%", "50%"],
						radius: ["60%", "90%"],
						clockwise: true,
						avoidLabelOverlap: true,
						hoverOffset: 15,
						itemStyle: {
							normal: {
								color: function(params) {
									return colorList[params.dataIndex];
								},
							},
						},
						label: {
							show: false,
						},
						labelLine: {},
						data: woring.value,
					}, ],
				  };
				// myChart.setOption(option);
				myChart.setOption(option);
				window.addEventListener("resize", function() {
					myChart.resize();
				});
      }
      const getpmt=async()=>{
          const {data:res}=await gettable('GetPlaneImageDetail',getform.value)
                // console.log('获取',res);
                if (res.code=="1000") {
                urls.value=res.data.PlaneImage
                getinit()
                }
                
      }
      // 获取图片
      const getinit=async()=>{
            let _this=this
          const {data:res}=await gettable('GetJKXYpointInfo',getform.value)
                // if (res.code=="1000") {
            postin.value=res.data
            let circle
            canvas = new fabric.Canvas('canvas', {
                perPixelTargetFind: true, //这一句说明选中的时候以图形的实际大小来选择而不是以边框来选择
                hasBorders: false,
                selection: false
            })
            // 2. 设置背景图片做为底图@/assets/img/dw/003.png
            fabric.Image.fromURL(urls.value, function (img) {
                //保证背景图1:1铺满容器 
                canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                scaleX: canvas.width / img.width,
                scaleY: canvas.height / img.height,
                })
            })
            // this.getposting()
            canvas.clear()
            // canvas.selection = false 
            // 获取坐标点位置
            // this.postin.forEach((item,index)=>{
            for (let index = 0; index < postin.value.length; index++) {
            // console.log('获取位置',parseInt(_this.postin[index].Xpoint),);
            
                circle = new fabric.Circle({
                left:parseInt(postin.value[index].Xpoint),
                top: parseInt(postin.value[index].Ypoint),
                // left:50,
                // top: 100,
                radius: 5,
                fill: 'black',
                hasControls: false,
                lockMovementX:true,
                lockMovementY:true,
                GUID:postin.value[index].GUID,
                MeasurementPoints:postin.value[index].MeasurementPoints,
                MonitorID:postin.value[index].MonitorID,
                MonitorItems:postin.value[index].MonitorItems,
                // stroke: 'red', // 描边颜色
                // strokeWidth: 2, // 描边宽度
                selection: false

            })
            const text = new fabric.Text(postin.value[index].MeasurementPoints, {
                left:parseInt(postin.value[index].Xpoint)+10,
                top: parseInt(postin.value[index].Ypoint)-8,
                fontSize: 20,
                hasControls: false,
                lockMovementX:true,
                lockMovementY:true,
                fill: 'orange',
                hasControls:false,
                selectable:false
            })
            canvas.add(circle)
            canvas.add(text)

            }
            canvas.on('mouse:down', function (e) {    
        // console.log('鼠标按下',e.e.deltaY)
        // canvas.selectable = false
            var target = canvas.findTarget(e.e);
            let evt = e.e
            // if (evt.altKey === true) { // 是否按住alt
            //     canvas.isDragging = true // isDragging 是自定义的，开启移动状态
            //     // canvas.lastPosX = evt.clientX // lastPosX 是自定义的
            //     // canvas.lastPosY = evt.clientY // lastPosY 是自定义的
            //     _this.panning = true

            // }else if (_this.falge==1) {
                pointer = canvas.getPointer(e.e); // 获取鼠标相对于画布的坐标
            if (target) {
            // console.log('获取点击',target);
            // _this.getdrtils(target)
            //  _this.$refs.deepdelog.showdelog(target)
              deepdelog.value.showdelog(target)
            }
                // 更新Canvas显示
                // updateCanvas();
                // _this.$refs.deepdelog.showdelog(,target)
            // _this.dialogVisible=true
            // }

      })
      //鼠标抬起事件
      canvas.on('mouse:up', function (e) {
        // _this.panning = false
        canvas.selectable = false
      })

        }
      onBeforeUnmount(()=>{
        canvas.dispose()
      })
      return{
        formlabe,
        btns,
        yjcount,
        woring,
        WorkTypecolor,
        bjcounts,
        counts,
        bardata,
        getinit,
        deepdelog,

        getfile,
        open,
        getmorties,
        geteqments,
        geteqment,
        getyjcout,
        getworing
      }
    }
}
</script>
<style lang="scss">
.foundations{
.canvas-container{
  position: relative!important;
}
}
</style>
<style lang="scss" scoped>
.foundations{
  display: grid;
  grid-template-columns: 40% 60%;
  height: 100%;
  width: 100%;
  color: #fff;
  &-two{
    display: flex;
    margin: 10px;
    img{
      width: 30px;
      height: 30px;
    }
  }
}

.rightbom{
  width: 100%;
}
.el-scrollbar{
  width: 70%;
}
.posting{
    margin: 0 20px;
    grid-column: 2;
    grid-row: 1/span 2;
    position: relative;
}
.icons{
    width: 12px;
    height: 12px;
    border-radius: 100%;
}
.rightbom{
    display: flex;
    width: 100%;
}
.labelechart{
    display: grid;
    grid-template-columns: 10% 60% 15% 15%;
    margin: 10px;
    align-items: center;
}
#Alarmstatistics{
    width: 250px;
    height: 250px;
}
.eqmentid{
  width: 100%;
  height: 260px;
}
.prows{
  margin: 10px;
}
.btns{
  margin-left: 10px;
}
</style>