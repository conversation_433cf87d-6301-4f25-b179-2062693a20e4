<template>
  <div class="container">
    <div :id="ids" style="width:100%;height:100%;"></div>
  </div>
</template>

<script>
import { nextTick, onMounted,watch } from 'vue';
export default {
props:['ids','options','distances', 'enableClick'],
emits: ['pieClick'],
setup(props, { emit }){
// 添加防抖标志变量
let isProcessingClick = false;

// 防抖函数 - 重置标志
const resetClickFlag = () => {
    setTimeout(() => {
        isProcessingClick = false;
    }, 300); // 300ms 防抖时间
};

watch(()=>props.options, (newVal, oldVal) => {
     // console.log('监听数据',newVal);
     if (newVal) {
         nextTick(()=>{
             getshwo()
         })
     }
   },
{immediate: true})

function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height, i) {
    // 计算
    let midRatio = (startRatio + endRatio) / 2;

    let startRadian = startRatio * Math.PI * 2;
    let endRadian = endRatio * Math.PI * 2;
    let midRadian = midRatio * Math.PI * 2;
    if (i === 1) {
        startRadian = 0;
        //  endRadian = 1
    }
    // console.log('endRatio', startRatio, endRatio);
    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    // k = typeof k !== 'undefined' ? k : 1 / 3 ;
    k = 1;

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
    let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    let hoverRate = isHovered ? 1.05 : 1;

    // 返回曲面参数方程
    return {
        u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32,
        },
        v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
        },
        x: function (u, v) {
            if (u < startRadian) {
                return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
                return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        y: function (u, v) {
            if (u < startRadian) {
                return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
                return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        z: function (u, v) {
            if (u < -Math.PI * 0.5) {
                return Math.sin(u);
            }
            if (u > Math.PI * 2.5) {
                return Math.sin(u);
            }
            if (i === 0) {
                return Math.sin(v) > 0 ? 4 : 1;
            }
            return Math.sin(v) > 0 ? 1 : -1;
        },
    };
}

// 更新选中状态并重新计算参数方程
function updateSelectedStatus(options, seriesIndex, isSelected) {
    // 更新选中状态
    for (let i = 0; i < options.series.length; i++) {
        if (options.series[i].type === 'surface') {
            // 将所有扇区重置为非选中状态
            options.series[i].pieStatus.selected = false;
            // 更新参数方程
            options.series[i].parametricEquation = getParametricEquation(
                options.series[i].pieData.startRatio,
                options.series[i].pieData.endRatio,
                false,
                options.series[i].pieStatus.hovered,
                options.series[i].pieStatus.k,
                options.series[i].pieData.value,
                i
            );
        }
    }
    
    // 设置当前选中扇区的状态
    if (seriesIndex >= 0 && seriesIndex < options.series.length && options.series[seriesIndex].type === 'surface') {
        options.series[seriesIndex].pieStatus.selected = isSelected;
        
        // 更新选中扇区的参数方程
        options.series[seriesIndex].parametricEquation = getParametricEquation(
            options.series[seriesIndex].pieData.startRatio,
            options.series[seriesIndex].pieData.endRatio,
            isSelected,
            options.series[seriesIndex].pieStatus.hovered,
            options.series[seriesIndex].pieStatus.k,
            10, // 设置固定高度，保持顶部和底部宽度一致
            seriesIndex
        );
    }
    
    return options;
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData, internalDiameterRatio) {
    let series = [];
    let sumValue = 0;
    let startValue = 0;
    let endValue = 0;
    let legendData = [];
    let k =
        typeof internalDiameterRatio !== 'undefined'
            ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
            : 1 / 3;

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;

        let seriesItem = {
            name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
            type: 'surface',
            parametric: true,
            wireframe: {
                show: false,
            },
            pieData: pieData[i],
            pieStatus: {
                selected: false,
                hovered: false,
                k: k,
            },
        };

        if (typeof pieData[i].itemStyle != 'undefined') {
            let itemStyle = {};

            typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null;
            typeof pieData[i].itemStyle.opacity != 'undefined'
                ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
                : null;

            seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
    }

    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametric 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;
        // console.log(series[i]);
        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            false,
            k,
            10, // 设置固定高度，保持顶部和底部宽度一致
            i
        );

        startValue = endValue;

        legendData.push(series[i].name);
    }

    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    let option = {
        // backgroundColor: '#000',
        tooltip: {
            formatter: (params) => {
                // console.log('鼠标移入',params,option.series,params.seriesIndex);

                if (params.seriesName !== 'mouseoutSeries'&&params.seriesName !== 'pie2d') {
                        let valuename=''
                        option.series[option.series.length-1].data.forEach((item)=>{
                        if(item.name==params.seriesName){
                            valuename=item.value
                        }
                        })
                    return `${
                        params.seriesName
                    }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                        params.color
                    };"></span>${valuename}`;

                }
            },
        },
        legend: {
            data: legendData,
            icon: 'circle',
            textStyle: {
                color: '#fff',
                fontSize: 10,
            },
        },
    //     label: {
    //       show: true,
    //       position: 'outside',
    //       formatter: '{b} \n{c} {d}%',
    //   },
        xAxis3D: {
            min: -1,
            max: 1,
        },
        yAxis3D: {
            min: -1,
            max: 1,
        },
        zAxis3D: {
            min: -1,
            max: 1,
        },
        grid3D: {
            show: false,
            boxHeight: 20,
            //top: '30%',
            bottom: '10%',
            // light:{
            //     ambient:{
            //         color:"red",
            //         intensity:.2
            //     }
            // },
            viewControl: {
                distance: props.distances, //视距
                alpha: 40,
                beta: 80,
                autoRotate:false,
                damping:1,
                zoomSensitivity:0,
                rotateSensitivity:0,//旋转操作的灵敏度，值越大越灵敏。支持使用数组分别设置横向和纵向的旋转灵敏度
            },
        },
        series: series,
    };
    return option;
}

// 当前图表实例和配置项的引用
let currentChart = null;
let currentOptions = null;

const getshwo=()=>{
    var echarts = require('echarts');
    let myChart = echarts.getInstanceByDom(document.getElementById(props.ids));
    if (myChart == null) {
      myChart = echarts.init(document.getElementById(props.ids));
    }
    //  var myChart = echarts.init(document.getElementById(props.ids));
    let options= getPie3D(props.options,0);
    options.series.push({
			name: 'pie2d',
			type: 'pie',
			labelLine:{
				length:30,
				// length2:10
			},
            label: {
                position: 'inner',
                opacity: 1,
                fontSize: 12,
                lineHeight: 20,
                color:'#fff',
                formatter: '{b} \n{c}',
            },
			startAngle: -40 , //起始角度，支持范围[0, 360]。
			clockwise: false,//饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
			// radius: ['50%', '100%'],
			radius: '80%',
			// center: ['50%', '50%'],
			data:props.options,
			itemStyle:{
				opacity:0
			}
		});
    
    // 保存引用，便于后续更新
    currentChart = myChart;
    currentOptions = options;
    
    myChart.setOption(options);
    
    // 添加点击事件监听
    if (props.enableClick !== false) {
        myChart.off('click'); // 移除可能存在的旧事件监听器
        myChart.off('legendselectchanged'); // 同时移除 legend 事件监听器
        
        myChart.on('click', function(params) {
            // 防抖处理：如果正在处理点击事件，则跳过
            if (isProcessingClick) return;
            
            // 仅处理表面图形的点击事件
            if (params.componentType === 'series' && 
                params.seriesType === 'surface' &&
                params.seriesName !== 'mouseoutSeries' &&
                params.seriesName !== 'pie2d') {
                
                // 设置防抖标志
                isProcessingClick = true;
                
                // 获取点击的扇区数据
                const clickedData = {
                    name: params.seriesName,
                    seriesIndex: params.seriesIndex,
                    value: params.seriesIndex < props.options.length ? props.options[params.seriesIndex].value : null,
                    // 可以添加更多需要的数据
                };
                
                // 更新选中状态
                let updatedOptions = updateSelectedStatus(JSON.parse(JSON.stringify(currentOptions)), params.seriesIndex, true);
                myChart.setOption(updatedOptions);
                currentOptions = updatedOptions;
                // console.log('点击',clickedData);
                
                // 向父组件发送点击事件
                emit('pieClick', clickedData);
                
                // 重置防抖标志
                resetClickFlag();
            }
        });
        
        // 添加 legend 点击事件监听
        myChart.on('legendselectchanged', function(params) {
            // 防抖处理：如果正在处理点击事件，则跳过
            if (isProcessingClick) return;
            
            // 设置防抖标志
            isProcessingClick = true;
            
            // 查找对应的扇区索引
            let seriesIndex = -1;
            for (let i = 0; i < currentOptions.series.length; i++) {
                if (currentOptions.series[i].name === params.name && 
                    currentOptions.series[i].type === 'surface') {
                    seriesIndex = i;
                    break;
                }
            }
            
            if (seriesIndex !== -1) {
                // 获取点击的扇区数据
                const clickedData = {
                    name: params.name,
                    seriesIndex: seriesIndex,
                    value: seriesIndex < props.options.length ? props.options[seriesIndex].value : null,
                };
                
                // 更新选中状态
                let updatedOptions = updateSelectedStatus(JSON.parse(JSON.stringify(currentOptions)), seriesIndex, true);
                myChart.setOption(updatedOptions);
                currentOptions = updatedOptions;
                // console.log('legend点击',clickedData);
                
                // 向父组件发送点击事件，与扇区点击保持一致
                emit('pieClick', clickedData);
            }
            
            // 重置防抖标志
            resetClickFlag();
        });
    }
    
    window.addEventListener("resize", function() {
      myChart.resize();
    });
}
    return{
        // getecharts,
        getshwo
    }
}
}
</script>
<style lang="scss" scoped>
.container {
            width: 100%;
            height: 100%;
            // background-color: #000000;
            position: relative;
        }

.imgContent {
            width: 164px;
            height: 86px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 5;
}
</style>