
export default function(key, newVal) {
    //注意categoryNum为要监听的属性，实际开发中请自行修改
      if (key === 'theme') {
          // 创建一个StorageEvent事件
        setsseion(key, newVal,'setItem')

      }
      if (key === 'themecolor') {
        setsseion(key, newVal,'setthcolor')
    }

}
function setsseion(key,newVal,setval) {
    var newStorageEvent = document.createEvent('StorageEvent');
          const storage = {
              setItem: function (k, val) {
                  sessionStorage.setItem(k, val); 
                  // 初始化创建的事件
                  newStorageEvent.initStorageEvent(setval, false, false, k, null, val, null, null); 
                  // 派发对象
                  window.dispatchEvent(newStorageEvent)
              }
          }
          return storage.setItem(key, newVal);
}
    