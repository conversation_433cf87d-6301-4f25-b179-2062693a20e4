<template>
    <el-container class="large-bg">
      <el-header>
        <div class="el-header-left">
            <svg class="iconfont svg"> 
                <use :xlink:href="`#icon-fuwuchaoshi`"></use> 
            </svg> 
            <p class="titles">安全积分云超市</p>
            <p>Security Points Cloud Supermarket</p>
        </div>
        <div class="el-header-right">
            <el-icon ><Clock /></el-icon>
            <span class="time">{{$formatDateTime(new Date(), 'yyyy-MM-dd w') }}</span>
            <h2 >{{$formatDateTime(new Date(), 'HH:mm:ss') }}</h2>
            <img class="weater" :src="`https://ai-zqface-com-wjgl.oss-cn-hangzhou.aliyuncs.com/fillist/Realsystem/icons/${topform.icon}.png`" 
                alt="" style="width:50px;height:50px">
            <span>{{ topform.text }}</span>
            <span class="weater-temp">{{ topform.temp }}°C</span>
        </div>
      </el-header>
      <dv-decoration-2 style="width:100%;height:5px;" />
      <el-main>
        <div class="left-section">
            <div class="left-section-top">
            <div class="left-section-item" v-for="item in leftlist" :key="item.name">
                <div class="shop-title-box">
                    <h3 class="shop-title " :style="{'color':item.color}">{{item.name}}</h3>
                    <dv-decoration-6 style="width:100px;height:20px;" />
                    <img :src="item.src" alt="" style="width: 100%;height: 8px;">
                </div>
                <div class="shop-content" v-for="(ites,i) in item.list" :key="i" :style="{'color':item.color}">
                    <span class="title shop-title1">{{ites.name}}</span>
                    <span class="title shop-title2">环比+{{leftfrom[ites.value1]}}</span>
                    <div class="counter-container">
                        <template v-for="(digit, index) in formattedCounts[ites.value]" :key="index">
                            <template v-if="digit === ','">
                                <div class="separator">，</div>
                            </template>
                            <template v-else>
                                <div class="digit-box" :style="{'background-color':item.bgcolor}">
                                    <div :class="['digit-flip-container', animationStates[ites.value] && animationStates[ites.value][index] ? 'flip-animate' : '']">
                                        <div class="digit-current">{{ digit }}</div>
                                        <div class="digit-next">{{ getNextDigit(index, ites.value) }}</div>
                                    </div>
                                </div>
                            </template>
                        </template>
                        <span class="indicator">{{item.unit}}</span>
                    </div>
                </div>
            </div>
            </div>
            <div class="left-section-bottom">
                <h3 class="titlesh3">售货机商品统计</h3>
                <Productstatistics ref="Productstatistics1"></Productstatistics>
            </div>
        </div>
        
        <div class="center-section">
            <h3 class="titlesh3">人员积排名</h3>
            <div class="center-section-top">
                <dv-scroll-ranking-board :config="config" style="width:100%;height:300px" />
            </div>
            <h3 class="titlesh3">今日积分获取来源</h3>
            <div class="center-section-bottom" id="echarts2"></div>
        </div>
        <div class="right-section">
            <h3 class="titlesh3">总收入分析图</h3>
            <div class="right-section-top" id="echarts3"></div>
            <h3 class="titlesh3">今日商品兑换明细</h3>
            <div class="right-section-bottom">
                <Shops ref="Shops1"></Shops>
            </div>
        </div>
      </el-main>
    </el-container>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
const $http = getCurrentInstance().appContext.config.globalProperties.$http
import { gettable, setdata, deldata } from "@/network/api/requestnet";
import { useStore } from 'vuex'
import { color } from 'echarts';
// import  echarts  from "@/views/multiple/index/echarts.vue";
import Productstatistics from "@/views/multiple/index/delog/Productstatistics.vue";
import Shops from "@/views/multiple/index/delog/shops.vue";
const { proxy } = getCurrentInstance()
const formattedDate = proxy.$formatDateTime(new Date(), 'yyyy-MM-dd')
const store = useStore()

// 计数器数据
const count = ref(133);
const previousCount = ref(133); // 记录前一个数值
const animationState = ref([]); // 记录每个数字位的动画状态
// 修改为使用多个字段的对象
const counts = ref({
    ZCRS: 0,         // 在场人员数量
    SoldNum: 0,      // 今日商品售出数量
    TotalScore: 0,   // 人员积分总数 
    TodayUsingScore: 0 // 今日积分使用数量
});
const previousCounts = ref({...counts.value}); // 记录前一个数值
const animationStates = ref({}); // 记录每个字段数字位的动画状态
let config=ref({
    data:[]
})
let getform=ref({
    ProjectCode:store.getters?.code,
})
// 为每个字段创建格式化计算属性
const formattedCounts = computed(() => {
    const result = {};
    for (const key in counts.value) {
        const formatted = counts.value[key].toString().padStart(6, '0');
        result[key] = formatted.slice(0, 3) + ',' + formatted.slice(3);
    }
    return result;
});

// 左上角列表
let leftlist = [
    {
        name: '人员及商品统计',
        value: '',
        src:require('@/assets/img/all/001.svg'),
        color:'#07a6ff',
        bgcolor:'rgba(7, 166, 255, 0.3)',
        unit:'人',
        list:[
            {
                name:'在场人员数量',
                value:'ZCRS',
                value1:'HBZCRSRate',
            },{
                name:'今日商品售出数量',
                value:'SoldNum',
                value1:'HBSoldRate',
            },
        ]
    },{
        name: '积分统计',
        value: '',
        src:require('@/assets/img/all/002.svg'),
        color:'#00fffc',
        bgcolor:'rgba(0, 255, 252, 0.3)',
        unit:'分',
        list:[
            {
                name:'人员积分总数',
                value:'TotalScore',
                value1:'HBTotalScoreRate',
            },{
                name:'今日积分使用数量',
                value:'TodayUsingScore',
                value1:'HBTodayUsingScoreRate',
            },
        ]
    }
]
let topform = ref({
    icon: '999'
})
let leftfrom=ref({})
let countTimer = null;
let Productstatistics1=ref(null)
let Shops1=ref(null)
let forms=ref({})
onMounted(() => {
    // console.log('获取');
    // console.log('获取',window.location.href?.split('?code=')[1]);
   let code= window.location.href?.split('?code=')[1]
//    console.log('获取',code);
   
//    store.dispatch('getcode',code)

//    console.log('获取',code);
   getform.value.ProjectCode=code
    getweater();
    
    // 初始化各个字段的动画状态
    for (const key in counts.value) {
        animationStates.value[key] = formattedCounts.value[key].split('').map(() => false);
    }
    
    // 首次获取数据
    getscore();
    getranking();
    getecharts();
    getechartsdata()
    // console.log('获取url',geturls());
});

// watch(() => store.getters.code,(val) => {
//     console.log('监听数据',val);
    
// },
// { immediate: true })
// const getdata=async()=>{
// 获取积分数量
const getscore=async()=>{
    const {data:res}=await gettable('GetJFScoreStatistics',getform.value)
    // console.log('获取积分数量',res);
    
    if(res.code=='1000'){
        leftfrom.value=res.data
        
        // 记录前一个数值用于动画
        previousCounts.value = {...counts.value};
        
        // 检查数据是否有变化
        let hasChanges = false;
        
        // 更新所有相关字段
        for (const key in counts.value) {
            if (res.data[key] !== undefined && counts.value[key] !== res.data[key]) {
                counts.value[key] = res.data[key];
                hasChanges = true;
            }
        }
        
        // 只有数据有变化时才触发翻页动画
        if (hasChanges) {
            // 为每个字段重置动画状态
            for (const key in counts.value) {
                animationStates.value[key] = formattedCounts.value[key].split('').map(() => false);
            }
            
            // 触发动画
            triggerDigitFlip();
        }
    }
}
// 获取售货机商品积分
const getranking=async()=>{
    let form={}
    const {data:res}=await gettable('GetJFScoreByPerson',getform.value)
    // console.log('获取排名',res);
    if(res.code=='1000'){
        // comdata(res.data,'GoodsEcharts')
        config.value = {
            ...config.value,
            data: res.data?.ScoreList.map(item => ({
                name: item.name,
                value: item.value
            }))
        }
        nextTick(()=>{
            let doms=document.querySelectorAll('.rank')
            // console.log('获取排名',doms);
            doms.forEach((item,i)=>{
                item.textContent= i+1  
                item.style.backgroundImage= `url(${require('@/assets/img/all/max'+(i+1>3?'4':i+1)+'.svg')})`
            })

        })
        // console.log('获取排名',doms);
    }
}
// 获取图表
const getecharts=async()=>{
    // let form={}
    const {data:res}=await gettable('GetJFScoreByToday',getform.value)
    // console.log('获取图表',res);
    if(res.code=='1000'){
        nextTick(()=>{
            domecharts(res.data)
        })
    }
}
// 获取总收入
const getechartsdata=async()=>{
    const {data:res}=await gettable('GetJFRevenueAnalysis',getform.value)
    // console.log('获取总收入',res);
    if(res.code=='1000'){
        nextTick(()=>{
            getecharts1(res.data)
        })
    }
}
const domecharts=(data)=>{
    let echarts = require('echarts');
   let myChart = echarts.getInstanceByDom(document.getElementById('echarts2'))
    if (myChart == null) {
        myChart = echarts.init(document.getElementById('echarts2'));
    }
    let option = {
        tooltip: {
    trigger: 'item'
  },
        series: [
        
        {
            name: '数据来源',
            type: 'pie',
            radius: '60%',
            data:data??[] ,
            label: {
                show: true,
                formatter: '{b}: {c} ({d}%)',
                color: '#fff',
                fontSize: 12
            }
        }
        ]
    }
    myChart?.setOption(option)

    window.addEventListener("resize", function() {
          myChart?.resize();
    });
}
// 总收入分析图
const getecharts1=(data)=>{
    let echarts = require('echarts');
    let myChart = echarts.getInstanceByDom(document.getElementById('echarts3'))
    if (myChart == null) {
        myChart = echarts.init(document.getElementById('echarts3'));
    }
    const option = {
          tooltip: {
			trigger: "axis",
			axisPointer: {
				label: {
					show: true,
					backgroundColor: "#fff",
					color: "#556677",
					borderColor: "rgba(0,0,0,0)",
					shadowColor: "rgba(0,0,0,0)",
					shadowOffsetY: 0,
				},
				lineStyle: {
					width: 0,
				},
			},
			backgroundColor: "#012259",
			textStyle: {
				color: "#ffffff",
			},
			padding: [10, 10],
			extraCssText: "box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)",
		},
         legend: {
            data: ['积分', '人民币'],
            textStyle: {
				color: "#fff"
			}
          },
          grid: {
				left: "2%",
				right: "4%",
				bottom: "2%",
				top: "20%",
				containLabel: true,
			},
          xAxis: {
            type: 'category',
            // boundaryGap: false,
            data:data?.map(item=>item.name)??[],
            axisLine: {
			lineStyle: {
					color: "#002860",
				},
			},
			axisTick: {
				show: false,
			},
			axisLabel: {
				interval: 1,
				textStyle: {
					color: "#ffffff",
				},
				// 默认x轴字体大小
				fontSize: 12,
				// margin:文字到x轴的距离
				margin: 15,
			},
			axisPointer: {},
			boundaryGap: false,
          },
          yAxis: {
            type: 'value',
            axisTick: {
				show: false,
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: "#002860",
				},
			},
			axisLabel: {
				textStyle: {
					color: "#ffffff",
				},
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: "#002860",
				},
			},
          },
          series: [
            {
				name: "积分",
				type: "line",
				data: data?.map(item=>item.value1)??[],
				symbolSize: 1,
				symbol: "circle",
				smooth: true,
				yAxisIndex: 0,
				showSymbol: false,
				lineStyle: {
					width: 3,
					color: "#00aff0",
					shadowColor: "rgba(158,135,255, 0.3)",
				},
				itemStyle: {
					normal: {
						color: "#00aff0",
					},
				},
			},
			{
			name: "人民币",
			type: "line",
			stack: "Total",
			data: data?.map(item=>item.value2)??[],
			symbolSize: 1,
			symbol: "circle",
			smooth: true,
			yAxisIndex: 0,
			showSymbol: false,
			lineStyle: {
				width: 3,
				color: "#95ED6A",
				shadowColor: "rgba(158,135,255, 0.3)",
			},
			itemStyle: {
				normal: {
					color: "#95ED6A",
				},
			},
			},
          ]
        }
    myChart?.setOption(option)

    window.addEventListener("resize", function() {
          myChart?.resize();
    });
}
// 获取下一个数字（用于翻页后的数字）
const getNextDigit = (index, field) => {
    // 如果没有指定字段，使用默认的count
    if (!field) {
        const formatted = previousCount.value.toString().padStart(6, '0');
        const str = formatted.slice(0, 3) + ',' + formatted.slice(3);
        return index >= str.length || str[index] === ',' ? '' : str[index];
    }
    
    // 使用指定字段
    const formatted = previousCounts.value[field].toString().padStart(6, '0');
    const str = formatted.slice(0, 3) + ',' + formatted.slice(3);
    return index >= str.length || str[index] === ',' ? '' : str[index];
};

// 定时更新计数器（注释掉或移除，因为我们不再使用模拟数据）

// 触发数字翻转动画
const triggerDigitFlip = () => {
    // 设置每个位置的动画状态
    setTimeout(() => {
        
        // 为每个字段设置动画状态
        for (const key in counts.value) {
            const newFormatted = formattedCounts.value[key];
            const oldFormatted = previousCounts.value[key].toString().padStart(6, '0');
            const oldStr = oldFormatted.slice(0, 3) + ',' + oldFormatted.slice(3);
            
            newFormatted.split('').forEach((digit, index) => {
                if (index < oldStr.length && digit !== oldStr[index]) {
                    if (!animationStates.value[key]) {
                        animationStates.value[key] = [];
                    }
                    animationStates.value[key][index] = true;
                    
                    // 动画结束后重置状态，以便下次触发
                    setTimeout(() => {
                        animationStates.value[key][index] = false;
                    }, 600);
                }
            });
        }
    }, 0);
};


// 获取天气数据
const getweater = async () => {
    const { data: res } = await $http.get(`https://api.qweather.com/v7/weather/now?location=101210102&key=2fea52dc1c3a48299b93406599ae5362`)
    if (res.code == "200") {
        topform.value = res.now
    }
}

// 获取项目所在城市
// const getcity=async()=>{
//         // const {data: res} = await gettable('GetCityByProject2',this.getform)
// 		const {data: res} = await $http.post(this.$moist.empowers+'/Api.ashx?PostType=get&Type=GetCityByProject2', this.getform)

//             if (res.code=="1000") {
//                 this.aresid=res.data.Area1
// 				        this.aresid1=res.data.Area2
//                 this.getweatherlist()
//             }
//     }
onBeforeUnmount(() => {
    // 组件销毁前清除定时器
    if (countTimer) clearInterval(countTimer);
});
</script>
<style lang="scss">
// .dv-scroll-ranking-board{
//     .rank{
//         height: 30px!important;
//         background-size: 100% 100%!important;
//         font-size: 18px!important;
//         color: #fff!important;
//         font-weight: bold!important;
//         line-height: 30px!important;
//     }
//     .row-item{
//         flex-direction: row!important;
//         justify-content: flex-start!important;
//         position: relative;
//         align-items: center!important;

//     }
//     .ranking-info{
//         width: 25%!important;
//         align-items: center!important;
//         .ranking-value{
//             position: absolute!important;
//             right: 0!important;
//             top: 50%!important;
//         }
//     }
//     .ranking-column{
//         width: 60%!important;
//         height: 8px!important;
//         border-radius: 140px;
//         background-color: rgba(0, 204, 255, 0.1);
//         border-bottom:0!important;
//         .inside-column{
//             height: 8px!important;
//             border-radius: 140px;
//             background-color: #00ccff!important;
//             box-sizing: border-box;

//         }
//     }
// }
</style>
<style lang="scss" scoped>
.el-container{
    height: 100%;
    // color: #000;
    background-image: url('@/assets/img/all/bgimg.jpg');
    background-size: 100% 100%;
    background-repeat: no-repeat;

}
.svg{
    width: 50px;
    height: 50px;
    grid-row: 1/span 2;
}
.el-main{
    display: grid;
    grid-template-columns: 35% 30% 35%;
    .left-section{
        height: 100%;
        .left-section-item{
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            height: 30px;
            width: 45%;
            margin: 10px 20px;
            img{
            grid-column: 1 / -1;
            }
        }
        &-top{
            display: flex;
            justify-content: center;
            height: 40%;
        }
        &-bottom{
            padding: 10px 20px;
            height: 60%;
            
        }
    
    .shop-content{
        margin-top: 25px;
        width: 100%;
        display: grid;
        grid-template-columns: 1fr 1fr;
        // flex-direction: column;
        justify-content: space-between;
        .title{
            // margin: 10px 0;
            height: 20px;
        }
        .counter-container {
            width: 100%;
            display: flex;
            align-items: center;
            margin-top: 10px;
            grid-column: 1 / -1;
            justify-content: center;
        }
        .digit-box {
            width: 50px;
            height: 60px;
            border-radius: 5px;
            border: 1px solid rgba(7, 166, 255, 0.5);
            background-color: rgba(7, 166, 255, 0.3);
            box-sizing: border-box;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: "DIN", sans-serif;
            font-size: 50px;
            font-weight: bold;
            margin: 0 2px;
            box-shadow: inset 0 -5px 10px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            perspective: 300px;
        }
        
        .digit-flip-container {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
            transition: all 0.5s ease;
        }
        
        .digit-current, .digit-next {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            // text-shadow: 0 0 8px rgba(0, 204, 255, 0.7);
        }
        
        .digit-next {
            transform: rotateX(180deg);
        }
        
        .flip-animate {
            transform: rotateX(180deg);
        }
        .separator {
            // color: white;
            font-size: 32px;
            margin: 0 2px;
            font-weight: bold;
        }
        .indicator {
            color: #00ccff;
            font-size: 16px;
            margin-left: 5px;
            align-self: flex-end;
            margin-bottom: 15px;
        }
        .shop-digit{
            width: 50px;
            height: 60px;
            padding: 2px 2px 2px 2px;
            border-radius: 5px;
            border: 1px solid rgba(7, 166, 255, 0.5);
            background-color: rgba(7, 166, 255, 0.3);
            box-sizing: border-box;
            font-family: "DIN", sans-serif;
            font-weight: 700;
            color: #ffffff;
            text-align: center;
            line-height: normal;
        }
    }
    .shop-title{
            display: inline-block;
            // height: 30px;
            font-size: 18px;
            margin-bottom: 10px;
        }
    .shop-title-box{
        display: flex;
        flex-wrap: wrap;
        // align-items: center;
        justify-content: space-between;
    }
    .shop-title1{
        font-size: 15px;
        text-align: start;
    }
    .shop-title2{
        font-size: 12px;
        text-align: end;
    }
    }
    .center-section{
        height: 100%;
        width: 100%;
        padding: 10px 40px;
        &-top{
            height: 50%;
            padding: 10px;
        }
        &-bottom{
            height:40%;
            width: 100%;
        }
    }
    .right-section{
        height: 100%;
        &-top{
            height: 30%;
            width: 100%;
        }
        &-bottom{
            height: 60%;
        }
    }
    .titlesh3{
        text-align: start;
        position: relative;
    }
    .titlesh3::before{
        display: inline-block;
        content: '';
        vertical-align: middle;
        width: 5px;
        height: 25px;
        margin-right: 10px;
        background: #00ccff;
    }
}
.el-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px!important;
    .titles{
        font-size: 25px;
        font-weight: 600;
    }
    &-left{
        display: grid;
        grid-template-columns: 20% 1fr;
        align-items: center;
        justify-items: start;
        width: 17%;
    }
    &-right{
        display: grid;
        grid-template-columns: 13% 35% 15% 35%;
        align-items: center;
        justify-items: start;
        width: 20%;
        .el-icon{
            font-size: 35px!important;
            grid-row: 1/span 2;
        }
        h2{
            grid-row: 2;
            grid-column: 2;
            color: #00ccff;
        }
        .time{
            font-size: 12px;
        }
        span{
            display: inline-block;
            height: 25px;
        }
        img{
            grid-row: 1/span 2;
            grid-column: 3;
        }
        .weater-temp{
            color: #00ccff;
            font-size: 20px;
        }
    }
}
</style>