<template>
    <div style="z-index: 3006;" v-if="imgViewerVisible"  >
        <el-image-viewer 
         @close="closeImgViewer"
         :hide-on-click-modal="true"
        :url-list="[pics]" 
        />
    </div>
</template>
<script>
export default {
    data() {
     return{
        imgViewerVisible:false,
        pics:''
     }   
    },
    methods:{
        piclist(value){
            // console.log('触发',value);
				if (value) {
				this.pics = value
				this.showImgViewer()
				}
				
			},
		showImgViewer(){
			this.imgViewerVisible = true;
			const m = (e) => { e.preventDefault() };
			document.body.style.overflow = 'hidden';
			document.addEventListener("touchmove", m, false); // 禁止页面滑动

			},
		closeImgViewer(){
			this.imgViewerVisible = false;
			const m = (e) => { e.preventDefault() };
			document.body.style.overflow = 'auto';
			document.removeEventListener("touchmove", m, true);
			}, 
        closeimg(){
            this.imgViewerVisible=false
        }
    }
}
</script>