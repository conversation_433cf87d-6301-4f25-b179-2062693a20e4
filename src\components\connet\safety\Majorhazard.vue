<template>
  <!-- 出勤 -->
  <div class="teamslist padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="'1'" :horn="1" :form="topforms"></Chamfering>

    <div class="teamslist-two">
        <div v-for="(item,index) in btnlist" :key="index" :class="['monitor-two-btn cursor',{'changindex':falge==index}]" 
        :style="[falge==index?`background:${bgcolor.changcolor};color:#FFF`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
         rgba(7, 93, 184, 0.6) 100%);color:#FFF`]" @click="change(item,index)"
        >{{item}}</div>
        <div class="teamslist-two-table">
            <el-table :data="gridData"  :style="['width: 100%',`color:${bgcolor.font};
            --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
            :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
            empty-text="暂无数据" :loading="loading" max-height="160px"
            >
            <el-table-column  prop="RiskIdentification" align="center" label="风险辨认" ></el-table-column>
            <el-table-column  prop="HazardSources" align="center"  label="潜在危险源"  width="100"></el-table-column>
            <el-table-column  prop="PreventiveMeasures" align="center"  label="防范措施" ></el-table-column>
            <el-table-column  prop="PersonLiable" align="center"  label="负责人" ></el-table-column>
          </el-table>
        </div>
    </div>
    <Chamfering :homeindex="'1'" :horn="0"></Chamfering>
    
  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
props:['homeindex','teamtype'],
components:{
  Chamfering
},
setup(props){

  let bgcolor=ref({})
  let falge=ref(0)
  let btnlist=['基础阶段','主体阶段','装饰阶段']
  let loading=ref(false)
  let getform=ref({
      ProjectCode:store.getters.code,
      HazardsStage:"基础阶段",//阶段
      RiskIdentification:"",//风险辨识 
      page:1,
      count:100

    })
  let gridData=ref([])
  let topforms=ref({
    url:require('@/assets/img/safety/Majorhazard.png'),
    name:"重大危险源防护"
  })
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      gettbaless()
  })
  const gettbaless=async()=>{
      loading.value=true

    const {data:res}=await gettable('GetHazardsTable',getform.value)
      loading.value=false
      // if (res.code=="1000") {
      gridData.value=res.data
      // }

    }
    const change=(val,index)=>{
        falge.value=index
        getform.value.HazardsStage=val
        gettbaless()
    }
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
	return{
		falge,
        loading,
		// countlist,
		getform,
		bgcolor,
    btnlist,
    topforms,
    change,
    gridData,
    tableRowClassName,
    gettbaless
	}
}
}
</script>
<style lang="scss">
.el-table .warning-row {
    background: rgba(15, 43, 63, 0.6)!important;
  }
</style>
<style lang="scss" scoped>
.teamslist{
   .rightshow{
    display: flex;
    align-items: center;
  }
  .monitor-two-btn{
    // height: 30%;
  }
&-two{
  height: 87%;
//   &-one{
    display: grid;
    grid-template-columns: repeat(3,33.3%);
    font-size: 14px;
    grid-template-rows: 22% 78%;
//   }
&-table{
    grid-area: 2/ span 3/1 span;
    height: 100%;
}
}
}
.changindex::before{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        left: -2px;
        top: -2px; 
        opacity: 1;
        border-top: 2px solid #E0A538;
        border-left: 2px solid #E0A538;
    }
.changindex::after{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        right: -2px;
        bottom: -2px; 
        opacity: 1;
        border-bottom: 2px solid #E0A538;
        border-right: 2px solid #E0A538;
    }

</style>