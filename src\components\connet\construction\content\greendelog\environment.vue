<template>
<el-dialog append-to-body :title="titles" class="environments" :close-on-click-modal="modals" v-model="dialogVisible" width="85%"
:destroy-on-close="false">
    <div class="leftbtn">
        <el-button v-for="(item,index) in list" :key="index" :title="item" :type="falge==index?'primary':'default'"
        @click="openright(item,index)"
        >{{item}}</el-button>
    </div>
    <div  v-if="pdfprew.includes(titletops)||padfright.includes(titletops)" :class="getclass()" :style="getstyle()">
        <el-scrollbar class="btnspdf" v-if="btns.length>0">
            <el-button v-for="(item,index) in btns" :key="index"
             :type="falge1==index?'primary':'default'" @click="rightchang(item.ContractFile,index,item)">{{item.Unit}}</el-button>
        </el-scrollbar>
        <iframe v-if="FileImg" :src="FileImg" frameborder="0" style="width:100%;height:82vh"></iframe>
        <p class="font" v-else>暂无文件预览</p>
        <div v-if="padfright.includes(titletops)" :class="padfright.includes(titletops)?'files':''">
            <div class="files-right padding" v-for="(ites,i) in boxs" :key="i" :style="getbgimg(ites)">
                <span v-for="(labl,l) in lables" :key="l" :class="`p${l}`">{{labl.name?labl.name:ites[labl.value]}}</span>
                <i class="iconfont icon-area2"></i>
                <i class="iconfont icon-mianji"></i>
            </div>
        </div>
    </div>
    <echartspic v-else-if="echartslist.includes(titletops)" ref="echartspic" :getforms="getform"></echartspic>
    <echartsbre v-else-if="bires.includes(titletops)" ref="echartsbres1"></echartsbre>
    <tablelist v-else-if="tablelists.includes(titletops)" ref="tablelists1"></tablelist>
</el-dialog>
</template>

<script>
import { ref,getCurrentInstance, nextTick } from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import echartspic from "./echartspic.vue";
import echartsbre from "./echartsbre.vue";
import tablelist from "./tablelist";
export default {
props:['environment'],
components:{
    echartspic,
    echartsbre,
    tablelist
},
setup(props){
  const $http = getCurrentInstance().appContext.config.globalProperties.$http
  const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist

    let titles=ref('')
    let modals=ref(true)
    let dialogVisible=ref(false)
    let list= ref([])
    let falge=ref(0)
    let getform=ref({
        ProjectCode:store.getters.code,
        InUserName:store.getters.username,
        TypeValue:'',
        Type:''
    })
    let FileImg=ref('')
    let titletops=ref('')
    let btns=ref([])
    let falge1=ref(0)
    let echartspic=ref(null)
    let echartsbres1=ref(null)
    let falgeshow=ref(false)
    // 预览pdf
    let pdfprew= ['施工许可证','绿色施工关键指标','绿色施工宣传资料','包含绿色施工关键指标要求的劳务、分包合同',
    '第三方水质检测报告','回填检测报告','环境保护管理制度','材料进场计划',
    '节材与材料资源利用管理制度','节水指标纳入合同','节水与水资源利用管理制度','节能与能源利用管理制度',
    '临时用地借地协议','活动板房建立验收资料','活动板房进场合格证、防火材料证明','节地与土地资源利用管理制度'

    ]
    let rightbtns=['包含绿色施工关键指标要求的劳务、分包合同','环境保护管理制度','节材与材料资源利用管理制度','节水指标纳入合同',
    '节水与水资源利用管理制度','节能指标纳入合同','节能与能源利用管理制度','节地与土地资源利用管理制度'
    ]//顶部有多选
    let echartslist=['扬尘监控测量记录表','PM2.5、PM10监控测量记录表','噪声监控测量记录表','污水监控测量记录表',
    '现场扬尘控制洒水记录表','隔油池清理记录表','化粪池清理记录表','施工现场移动厕所清理记录表','可回收建筑垃圾管理记录表',
    '有毒有害垃圾管理记录表','生活垃圾外运记录表','有毒有害垃圾管理记录表','项目用水记录及工程用水总量汇总表','基坑降水收集记录表',
    '中水回用记录表','雨水回用记录表','施工用电记录表及工程用电总量汇总表','大型机械保养记录表','石化气燃料使用台账表',
    '严重污染天气记录表','生活、办公垃圾外运记录台账','洒水记录表','餐具消毒记录表','施工现场消毒记录表'
    ]//顶部有多个图片有饼状图
    let bires=['主要材料采购记录表','材料、机具进出场台账','主要建筑材料损耗率','非实体工程材料重复利用',
    '节水设备使用统计表','设备总体耗能计划','节能设备及太阳能、风能、空气能设备配备情况登记表',
    '现场临时用房、硬化、植草砖铺装等各临建建设面积台账','特种作业人员登记表','劳动力计划表、劳动力使用台账'
    ]//多个饼状图列表右侧多个列表
    let padfright=['三阶段场布规划图']
    let tablelists=['保护用地措施','施工现场人员实名制登记表','食堂从业人员健康证明登记表','职业病防治体检登记表',
    '施工现场卫生保洁责任表','培训计划','培训台账'
    ]
    let tablelists1=ref(null)

    let source=ref(null)
    let boxs=ref([
        {
        name:'生活区',
        url:require('@/assets/img/construction/fileimg/01.svg')
        },{
        name:'办公区',
        url:require('@/assets/img/construction/fileimg/02.svg')
        },{
        name:'生产区',
        url:require('@/assets/img/construction/fileimg/03.svg')
        },{
        name:'施工道路',
        url:require('@/assets/img/construction/fileimg/04.svg')
        }
    ])
    let lables=ref([])
    const showdelog=(val)=>{
        console.log('获取弹窗',val);
        // lables=$labelist('三阶段场布规划图')
        titles.value=val.name
        list.value=val.list
        getform.value.TypeValue=val.list[0]
        getform.value.Type=val.list[0]
        titletops.value=val.list[0]
        falge1.value=0
        falge.value=0
        openright(val.list[0],0)
        if (padfright.includes(getform.value.Type)) {
            lables.value=$labelist('三阶段场布规划图')
        }

        dialogVisible.value=true
    }
    const openright=(val,index)=>{
        console.log('点击切换',val);
        // falgeshow.value=false
        if (source.value) {
            source.value.cancel("取消")
        }
        falge.value=index
        titletops.value=val
        getform.value.TypeValue=val
        getform.value.Type=val
        btns.value=[]
        let typename=[...pdfprew]
        const uniqueArr1 = typename.filter(item => !rightbtns.includes(item));
        // falgeshow.value=true;
        
        const CancelToken = $http.CancelToken;
    	source.value = CancelToken.source();
        if (uniqueArr1.includes(val)) {
            // console.log('制度管理');
            getpdfs()
        }else if (rightbtns.includes(val)) {
            getgreentable()
        }else if(padfright.includes(val)){
            lables.value=$labelist('三阶段场布规划图')
            getrights()
        }
        nextTick(()=>{
        if (echartslist.includes(val)) {
            // console.log('获取',echartspic.value);
        echartspic.value?.showdelog(getform.value)
        }else if (bires.includes(val)) {
         echartsbres1.value.showdelog(getform.value)
        }else if (tablelists.includes(val)) {
        tablelists1.value.showdelog(getform.value)
        }
        })
    }
    const getbgimg=(url)=>{

        return `background-image:url(${url.url})`
    }
    const getclass=(val)=>{
        if(padfright.includes(titletops.value)){
            return 'pdfright'
        }else{
            return `rightshow`
        }
        return ``
    }
    const getrights=async()=>{
        const {data:res}=await gettable('GetTemporaryConstructionCharts',getform.value)
        // console.log('获取场布图',res);
        if (res.code=="1000") {
        res.data.map((item,index)=>{
            item.ContractFile=item.StageImage
            item.Unit=item.ConstructionStage
        })
        boxs.value.map((its,i)=>{
            if(its.name==res.data[0].AreaLedgerList[i].Block){
              Object.assign(its, res.data[0].AreaLedgerList[i])
            }
        })
        btns.value=res.data
        }
    }
    const getpdfs=async()=>{
        const {data:res}=await gettable('GetProtectImgTable',getform.value)
        if (res.code=="1000") {
            FileImg.value=res.data[0].FileImg
        }else{
            FileImg.value=''
        }
    }
    const rightchang=(val,i,item)=>{
        // console.log('切换右侧数据显示',item);
        falge1.value=i
        if (item) {
            boxs.value.map((its,i)=>{
                if(its.name==item.AreaLedgerList[i].Block){
                Object.assign(its, item.AreaLedgerList[i])
                }
            })
        }
        
        FileImg.value=val
    }
    // 获取包含绿色施工关键指标要求的劳务、分包合同
    const getgreentable=async()=>{
        const {data:res}=await gettable('GetHJGreenContractTargetTable',getform.value)
            btns.value=res.data
            rightchang(btns.value[0]?.ContractFile,0,null)
    }
    const getstyle=()=>{
        let str=''
        if (FileImg.value) {
            str=`${rightbtns.includes(titletops.value)?'':'align-items: center;justify-content: center;align-content: flex-start'}`
        }else if(!padfright.includes(titletops.value)){
            str=`align-items: center;justify-content: center;align-content: center`
        }else{
            str=`flex-direction: column;`
        }
        return str
    }
    return{
        titles,
        getform,
        modals,
        dialogVisible,
        list,
        falge,
        FileImg,
        pdfprew,
        titletops,
        btns,
        rightbtns,
        falge1,
        echartslist,
        echartsbres1,
        echartspic,
        source,
        bires,
        falgeshow,
        padfright,
        boxs,
        lables,
        tablelists,
        tablelists1,
        // colors,

        showdelog,
        openright,
        getpdfs,
        getgreentable,
        getstyle,
        rightchang,
        getrights,
        getbgimg,
        getclass,

    }
}
}
</script>
<style lang="scss">
.environments{
--el-dialog-margin-top:5vh!important;
.el-dialog__body{
    display: grid;
    grid-template-columns: 18% 82%;
    padding-top: 10px; /* 增加上边距 */
}
.el-scrollbar__view{
    display: flex;
}
/* VSCode风格的标题栏和关闭按钮 */
.el-dialog__header {
    position: relative;
    width: 100%;
    padding: 8px 15px;
    background-color: #f3f3f3;
    border-bottom: 1px solid #e0e0e0;
}

.el-dialog__title {
    font-size: 14px;
    font-weight: normal;
    color: #333;
}

.el-dialog__headerbtn {
    position: absolute;
    top: 8px;
    right: 15px;
    width: 16px;
    height: 16px;
    padding: 0;
    background: transparent;
    opacity: 0.6;
    transition: opacity 0.2s;
}

.el-dialog__headerbtn:hover {
    opacity: 1;
}

.el-dialog__close {
    color: #5a5a5a;
    font-size: 16px;
}

.el-dialog__headerbtn:hover .el-dialog__close {
    color: #000;
}
}
</style>
<style lang="scss" scoped>
.leftbtn{
    display: flex;
    flex-direction: column;
.el-button{
    margin: 10px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    // width: 250px;
}
}
.btnspdf{
    height:50px;
    width: 100%;
    .el-button{
        flex-shrink: 0;
    }
}
.el-button:focus, .el-button:hover{
    border-color: #409EFF;
    background-color: #409EFF;
    color: #fff;
}
.font{
    font-size: 20px;
    font-weight: bold;
    text-align: center;
}
.rightshow{
    display: flex;
    flex-wrap: wrap;
    
}
@mixin grow($row,$column){
    grid-row:$row;
    grid-column:$column;
}
.pdfright{
    display: grid;
    grid-template-columns: 70% 30%;
    .btnspdf{
        grid-column: 1/span 2;
        grid-row: 1;
    }
    .files{
        height: 100%;
        &-right{
            color: #fff;
            display: grid;
            grid-template-columns: 20% 15% 65%;
            align-items: center;
            width: 80%;
            margin: 20px;
            .iconfont{
                font-size: 50px!important;
                
            }
            .icon-area2{
                @include grow(1/span 2,2);
            }
            .icon-mianji{
                @include grow(3/span 4,2);
            }
            span{
                display: inline-block;
                margin: 5px 20px;
            }
            .p2,.p4{
                font-size: 18px;
                font-weight: bold;
            }
            .p0{
                font-size: 20px;
                font-weight: bold;
                @include grow(1/span 4,1);
            }
        }
    }
}

</style>