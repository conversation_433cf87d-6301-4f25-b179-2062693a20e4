<template>
<div class="large-screen">
    <largescreen1 v-if="index==0" ref="largescreen"></largescreen1>
    <largescreen2 v-else-if="index==1" ref="largescreen2"></largescreen2>
</div>
</template>

<script setup>
import { onMounted, ref, onUnmounted } from 'vue';
import { useStore } from 'vuex'

import largescreen1 from './index/largescreen1.vue'
import largescreen2 from './index/largescreen2.vue'
let index =ref(1) // 默认为1，确保largescreen2组件能正常显示
const store = useStore()

// 处理URL参数的函数
const handleUrlParams = () => {
    // console.log('触发URL参数处理');

    try {
        // const urlParams = new URLSearchParams(window.location.search);
        const urls=window.location.hash
        // const code = urlParams.get('code')
        const urlParts = urls.split('?code=')[1].split('&');

        const code = urlParts[0]
        // console.log('URL参数code:', code);
        
        // 处理code参数
        if (code) {
            store.dispatch('getcode', code);
        }

        // 处理index参数，确保有合理的默认值
            index.value = 1; // 如果没有index参数，默认显示largescreen2
    } catch (error) {
        // console.error('URL参数处理错误:', error);
        // 出错时设置默认值
        index.value = 1;
    }
}

// 监听浏览器前进后退和地址栏直接输入
const handlePopstate = () => {
    // console.log('popstate事件触发');
    handleUrlParams();
}

onMounted(() => {
    // 初始加载时处理URL参数
    handleUrlParams();

    // 监听浏览器前进后退和地址栏直接输入事件
    window.addEventListener('popstate', handlePopstate);
})

onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener('popstate', handlePopstate);
})


</script>
<style lang="scss" scoped>
.large-screen{
    width: 100%;
    height: 100vh;
    overflow: hidden;
    position: relative;
    // background: url('@/assets/images/large-screen.png') no-repeat center center;
    // background-size: 100% 100%;
}
</style>