<template>
    <div class="decoration">
        <span></span>
        <span></span>
        <span></span>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
    // 可以添加自定义属性，比如颜色、大小等
    color: {
        type: String,
        default: '#00ffff'
    },
    size: {
        type: Number,
        default: 8
    },
    middleSize: {
        type: Number,
        default: 10
    },
    gap: {
        type: Number,
        default: 4
    }
})

// 计算透明色值
const colorWithOpacity = computed(() => props.color + '4D')
</script>

<style lang="scss" scoped>
.decoration {
    display: flex;
    align-items: center;
    gap: v-bind('gap + "px"');
    // 添加最小宽度确保可见
    min-width: 40px;

    span {
        display: block; // 确保元素显示
        min-width: v-bind('size + "px"');
        min-height: v-bind('size + "px"');
        width: v-bind('size + "px"');
        height: v-bind('size + "px"');
        background: v-bind('color');
        transform: rotate(45deg);
        position: relative;
        animation: twinkle 1.5s ease-in-out infinite;
        
        &::after {
            content: '';
            position: absolute;
            inset: 1px;
            background: v-bind('colorWithOpacity');
        }

        &:nth-child(2) {
            min-width: v-bind('middleSize + "px"');
            min-height: v-bind('middleSize + "px"');
            width: v-bind('middleSize + "px"');
            height: v-bind('middleSize + "px"');
            animation-delay: 0.5s;
        }

        &:nth-child(3) {
            animation-delay: 1s;
        }
    }
}

@keyframes twinkle {
    0% {
        opacity: 1;
        box-shadow: 0 0 5px v-bind('color');
    }
    50% {
        opacity: 0.5;
        box-shadow: 0 0 10px v-bind('color');
    }
    100% {
        opacity: 1;
        box-shadow: 0 0 5px v-bind('color');
    }
}
</style> 