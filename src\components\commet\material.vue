<template>
  <div class="model" >
    <div class="leftmodel left wid"  v-show="amplify==0">
        <Attendance  class="Homebgco" 
        :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`"  :materialtype="materialtype" ></Attendance>
        <teamslistss  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :teamtype="Typeworlist"></teamslistss>
        <teamslistss  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :teamtype="teamtype"></teamslistss>
    </div>
    <div class="homecontent"  :style="amplify==0?'width:60%':'width:100%'">
      <home  @getamplify="getamplify2"></home>
    </div>
    <div class="rightmodel right wid"  v-show="amplify==0">
      <Weighing class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        ></Weighing>
      <deviation class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        ></deviation>
      <Requisition class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        ></Requisition>
    </div>

  </div>
</template>

<script>
import Attendance from "@/components/connet/personnelcon/Attendance.vue";
// import Typeworkss from "@/components/connet/personnelcon/Typeworkss.vue";
import teamslistss from "@/components/connet/personnelcon/teamslist.vue";
import Weighing from "@/components/connet/material/Weighing.vue";
import deviation from "@/components/connet/material/deviation.vue";
import Requisition from "@/components/connet/material/Requisition.vue";


import home from "@/components/connet/material/content/home.vue";

import { onMounted, ref ,computed} from 'vue';

export default {
components:{
        Attendance,
        // Typeworkss,
        teamslistss,
        Weighing,
        deviation,
        Requisition,
        home,

        // Materialtype,
        // acceptance
    },
setup(){
    let themelist=ref([])

    let bgcolor=ref({})
    let amplify=ref(0)
    let materialtype=ref({
        src:require('@/assets/img/material/acceptance.png'),
        titles:'物料验收记录',
        type:'物料'
    })
    let teamtype=ref({
        src:require('@/assets/img/material/material.png'),
        titles:'供货商验收统计',
        type:'物料',
        ids:'suppliercharts'
    })
    
    let Typeworlist=ref({
        src:require('@/assets/img/material/Materialtype.png'),
        titles:'材料种类验收分析',
        type:'物料',
        ids:'materialcharts'
    })
    window.addEventListener('setItem', ()=> {
      // console.log('项目引导');
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
   })
   window.addEventListener('setthcolor', ()=> {
      // console.log('主题切换');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(()=>{
      // let divs =document.querySelector('.leftmodel')
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    
      // console.log('计算竖向',showname('weather')[0].name);

    })
    const getamplify2=(val)=>{
      // console.log('放大',val);
      
      amplify.value=val
    }
    return{materialtype,
        bgcolor,
        themelist,
        teamtype,
        amplify,
        Typeworlist,
        getamplify2
    }
}
}
</script>
<style lang="scss" scoped>
@mixin homeflex($deg) {
  height: 31.3%;
  width: 95%;
  opacity: 1;
  margin:7px 10px;
  background: linear-gradient($deg, #0096C7 7%, rgba(0,52,75,0.00) 97%);
}
@mixin leftdjx($left,$right,$top,$bottom,) {
  position: absolute;
  content: '';
  display: block;
  left: $left;
  top: $top;
  right: $right;
  bottom: $bottom;
  width: 10.06px;
  height: 10.84px;
  opacity: 1;
}

.Homebgco{
  position: relative;
  @include homeflex(90deg)
  }
.Homeright{
  position: relative;
  @include homeflex(-90deg);

}
</style>
<style lang="scss" scoped>
.model{
  width: 100%;
  height: 100%;
  display: flex;
  .wid{
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .homecontent{
    width: 60%;
    height: 100%;
    // position: relative;
  }
}
</style>