{"name": "smart", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@dataview/datav-vue3": "^0.0.0-test.1672506674342", "@jiaminghi/data-view": "^2.10.0", "animate.css": "^4.1.1", "axios": "^1.5.1", "core-js": "^3.8.3", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.9.1", "ezuikit-js": "^8.1.1", "fabric": "^5.4.2", "jquery": "^3.7.1", "node-polyfill-webpack-plugin": "^2.0.1", "nprogress": "^0.2.0", "qweather-icons": "^1.6.0", "swiper": "^11.1.14", "three": "^0.174.0", "v-click-outside": "^3.2.0", "vue": "^3.2.13", "vue-axios": "^3.5.2", "vue-lazyload": "^3.0.0", "vue-router": "^4.0.3", "vue-solar-lunar": "^1.0.3", "vue3-scale-box": "^0.1.9", "vuex": "^4.0.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "postcss-pxtorem": "^6.0.0", "sass": "^1.32.7", "sass-loader": "^12.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}