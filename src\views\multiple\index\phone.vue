<template>
  <div class="mobile-table-page">

    <el-table  :data="filteredData" v-loading="loading" style="width: 100%"   >
      <el-table-column  prop="WorkerName"  label="姓名"  width="50"/>
      <el-table-column  prop="IDCardNumber"  label="身份证号"  />
      <el-table-column  prop="TeamName"  label="班组"  />
      <el-table-column  prop="CorpName"  label="所属单位"  />
    </el-table>

  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick,watch } from 'vue'
import { gettable, setdatca, deldata } from "@/network/api/requestnet";
// ?code=330109202531313750&led=960302121004511&user=上海隧道741
export default {
  setup() {
    const searchText = ref('')
    const teamFilter = ref('')
    const loading = ref(false)
    const tableHeight = ref('auto')
    const workerList = ref([])
    let getform=ref({
        ProjectCode:'',
        // InUserName:'国丰集团727',
        led:'',
    })
    let filteredData =ref([])
    // 处理URL参数的统一函数
    const handleUrlParams = () => {
        try {
            // console.log('开始处理URL参数...');
            const urlParams = getUrlParams();
            // console.log('解析到的参数:', urlParams);

            // 更新表单数据
            if (urlParams && urlParams.code) {
                // console.log('设置ProjectCode:', urlParams.code);
                getform.value.ProjectCode = urlParams.code;
            }

            if (urlParams && urlParams.led) {
                // console.log('设置led:', urlParams.led);
                getform.value.led = urlParams.led;
            }

            // if (urlParams && urlParams.user) {
            //     // console.log('设置用户名:', urlParams.user);
            //     getform.value.InUserName = urlParams.user;
            // }

            // 重新获取数据
            // console.log('准备获取数据，当前表单:', getform.value);
            getdata();
        } catch (error) {
            // console.error('处理URL参数时出错:', error);
            // 出错时使用默认值
            getform.value.ProjectCode = '330109202531313750';
            getdata();
        }
    };

    onMounted(()=>{
        // console.log('组件挂载，初始化...');

        // 使用nextTick确保DOM完全渲染后再执行
        nextTick(() => {
            // console.log('DOM渲染完成，开始处理...');

            // 第一次加载时处理URL参数
            handleUrlParams();

            // 计算表格高度
            calculateTableHeight();
            window.addEventListener('resize', calculateTableHeight);

            // 监听路由变化
            window.addEventListener('popstate', () => {
                // console.log('监听到popstate事件');
                nextTick(() => {
                    handleUrlParams();
                });
            });

            window.addEventListener('hashchange', () => {
                // console.log('监听到hashchange事件');
                nextTick(() => {
                    handleUrlParams();
                });
            });
        });
    });

    // 监听URL变化
    watch(() => window.location.href, (newUrl, oldUrl) => {
        // console.log('URL变化:', newUrl, 'from:', oldUrl);
        if (newUrl !== oldUrl) {
            // console.log('URL确实发生了变化，处理参数...');
            // 使用nextTick确保DOM已更新
            nextTick(() => {
                handleUrlParams();
            });
        }
    }, { immediate: false }); // 改为false，避免初始化时重复调用
    
    const getdata=async()=>{
        loading.value=true
    const {data:res} = await gettable('GetJKWorkerByProjectCode', getform.value);
    // console.log('获取工人列表',res);
    filteredData.value=res.data
    loading.value=false
    }
    // 提取URL参数的方法
    const getUrlParams = () => {
      const url = window.location.href;
      // console.log('当前完整URL:', url);

      // 检查URL中是否包含查询参数
      if (!url.includes('?')) {
        // console.log('URL中没有查询参数');
        return { code: null, led: null,};
      }

      // 提取查询字符串部分
      const queryString = url.split('?')[1];
      // console.log('查询字符串:', queryString);

      // try {
        const params = new URLSearchParams(queryString);
        const result = {
          code: params.get('code'),
          led: params.get('led'),
          // user: params.get('user') ? decodeURIComponent(params.get('user')) : null
        };

        // console.log('解析结果:', result);
        return result;
      // } catch (error) {
      //   console.error('解析URL参数时出错:', error);
      //   return { code: null, led: null, user: null };
      // }
    };

    const calculateTableHeight = () => {
      try {
        const windowHeight = window.innerHeight;
        const searchSectionHeight = 120; // 搜索区域高度
        const padding = 40; // 上下边距
        const calculatedHeight = windowHeight - searchSectionHeight - padding;

        if (calculatedHeight > 0) {
          tableHeight.value = calculatedHeight + 'px';
          // console.log('表格高度已更新:', tableHeight.value);
        } else {
          tableHeight.value = '400px'; // 默认高度
          // console.log('使用默认表格高度:', tableHeight.value);
        }
      } catch (error) {
        // console.error('计算表格高度时出错:', error);
        tableHeight.value = '400px'; // 出错时使用默认高度
      }
    }

    // onMounted(() => {
    // //   loadData()
    //   calculateTableHeight()
    //   window.addEventListener('resize', calculateTableHeight)
    // })

    return {
    //   searchText,
    //   teamFilter,
    //   teamOptions,
      loading,
      filteredData,
      tableHeight,
    //   handleSearch
    }
  }
}
</script>

<style scoped>
.mobile-table-page {
  padding: 10px;
  height: 100vh;
  box-sizing: border-box;
  background: #f5f7fa;
}

.search-section {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

.search-section .el-input {
  flex: 2;
}

.search-section .el-select {
  flex: 1;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .search-section {
    flex-direction: column;
  }

  .el-table {
    font-size: 14px;
  }

  .el-table th {
    padding: 8px 0;
  }

  .el-table td {
    padding: 8px 0;
  }

  /* 确保在小屏幕上文字不会被截断 */
  .el-table .cell {
    white-space: normal;
    line-height: 1.4;
  }
}

.loading-text, .empty-text {
  text-align: center;
  padding: 20px;
  color: #909399;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #dcdfe6;
  border-radius: 3px;
}
</style>
