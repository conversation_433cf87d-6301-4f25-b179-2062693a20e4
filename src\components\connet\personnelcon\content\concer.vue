<template>
  <div class="concer">
    <!-- <i class="iconfont icon-fangda cursor" v-if="amplifyindex==0" @click="amplifyopen(1)"
     :style="{color:bgcolor.titlecolor,left:amplifyindex==0?'20%':'2%'}" title="放大"></i>
    <i class="iconfont icon-suoxiao cursor" v-else-if="amplifyindex==1" @click="amplifyopen(0)"
     :style="{color:bgcolor.titlecolor,left:amplifyindex==0?'20%':'2%'}" title="缩小"></i> -->
    <div class="concer-top">
        <el-button type="primary" v-for="(item,index) in topbtn" :key="index" @click="btns(item)">
            <template #icon>
                <i :class="[`iconfont ${item.value}` ]"></i>
            </template>
            <span>{{item.name}}</span>
        </el-button>
    </div>

    <img v-for="(item,index) in imglist" @mouseenter="mouseenter(item,index)" @mouseleave="mouseleave(item,index)"
    :key="index" class="imgpoting cursor" v-show="amplifyindex==0?item.left>'20'&&item.left<'80':item" :src="falge==index?item.src1:item.src" 
    :style="`top:${item.top}%;left:${item.left}%`" alt="" srcset="" @click="open(item)">
    
    <delog ref="delogs"></delog>
    <cadperon ref="cadperon"></cadperon>
    <el-dialog v-model="dialogTableVisible"  destroy-on-close
    class="personwqmit delogss" :width="showfalge==0?'30%':'60%'" title="设备信息">
    <selection ref="selection" @colses="closes" ></selection>
    <div class="personwqmit-body bodybottom">
        <div>
            <div class="personwqmit-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
                <div class="personwqmit-body-one">
                    <img src="@/assets/img/home/<USER>" alt="">
                    <p>项目进度照片</p>
                </div>
            </div>
            <div class="personwqmit-body-two">
                <div class="personwqmit-body-two-p" v-for="(item,index) in leftlable" :key="index" :style="{color:bgcolor.font}">
                    <span>{{item.name}}：{{forms[item.value]}}</span>
                </div>
            </div>
            <div class="personwqmit-body-three">
                <img :src="forms.ImageUrl" alt=""  class="cursor" style="height:180px;width:120px" @click="pic(forms.ImageUrl)">
                <div class="Similaritysum">
                    <Bieechart :ids="'Similarity'" :options1="option"></Bieechart>
                    <p class="Similaritysum-p"  :style="{color:bgcolor.font}">相似度</p>
                </div>
                <img :src="forms.ImageOne" class="cursor" alt="" style="height:180px;width:120px" @click="pic(forms.ImageOne)">
            </div>
            <div class="personwqmit-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
                <div class="personwqmit-body-one">
                    <img src="@/assets/img/home/<USER>" alt="">
                    <p>考勤记录</p>
            </div>
        </div>
        
        </div>
        <div>
            <div class="personwqmit-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
                <div class="personwqmit-body-one">
                    <img src="@/assets/img/home/<USER>" alt="">
                    <p>考勤通道信息</p>
                </div>

            </div>
            <div class="personwqmit-body-two">
                <div class="personwqmit-body-two-p" v-for="(item,index) in rightlabe" :key="index" :style="{color:bgcolor.font}">
                    <span>{{item.name}}</span>
                </div>
            </div> 

            <div class="attenttong">
                <img src="@/assets/img/home/<USER>" alt="" style="width:100%;height:260px">
            </div>
        </div>
        <div class="lastdiv">
            <el-table :data="tableDate"  :style="['width: 100%',`color:${bgcolor.font};
            --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
            :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
            empty-text="暂无数据" :loading="loading" max-height="230px"
            >
                <!-- <template #empty>
                    <el-empty  v-loading="loading"></el-empty>
                </template> -->
                <el-table-column width="90" prop="rowNum" label="序号" align="center"> </el-table-column>
                <el-table-column width="90" prop="WorkerName" label="员工姓名" align="center"> </el-table-column>
                <el-table-column prop="GName" label="所在工种" align="center"> </el-table-column>
                <el-table-column prop="TeamName" label="所在班组" align="center"> </el-table-column>
                <el-table-column prop="AttendType" label="考勤方式" align="center"> </el-table-column>
                <el-table-column prop="AttendTime" label="考勤打卡时间" align="center"> </el-table-column>
                <el-table-column prop="Direction" label="考勤方式" align="center"> </el-table-column>
                <el-table-column prop="ImageUrl" label="考勤照片" align="center">
                    <template #default="scope">
                        <img :src="scope.row.ImageUrl"  class="cursor" alt="" style="width:50px;height:70px" @click="pic(scope.row.ImageUrl)">
                    </template>
                </el-table-column>
          </el-table>
          <el-pagination
            v-model:current-page="getform.page"
            v-model:page-size="getform.count"
            :page-sizes="[10, 20, 50, 100]"
            :background="background"
            layout="total, sizes, prev, pager, next, jumper"
            :total="Total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        </div>
    </div>

    </el-dialog>
    <picimg ref="picimg"></picimg>
  </div>
</template>

<script>
import { computed, onMounted, reactive, ref,watch } from 'vue'
import Bieechart from "@/components/connet/Common/echartscom.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";
import { labelist } from "@/components/connet/personnelcon/content/labeles";
import delog from "@/components/connet/personnelcon/content/delog.vue";
import cadperon from "@/components/connet/personnelcon/content/cadperon.vue";
import selection from "@/components/connet/Common/selection.vue";

export default {
components:{
        Bieechart,
        picimg,
        delog,
        cadperon,
        selection
    },
setup(props,ctx){
    let imglist=ref([{
        src:require('@/assets/img/personnelcon/channel.png'),
        src1:require('@/assets/img/personnelcon/channel1.png'),
        name:'考勤通道',
        top:'30',
        left:'65'
    },
    ])
    let forms=ref({})
    let Total=ref(0)
    let leftlable=[{
        name:'员工姓名',
        value:'WorkerName'
    },{
        name:'员工性别',
        value:'Gender'
    },{
        name:'员工年龄',
        value:'Age'
    },{
        name:'类型',
        value:'WorkerType'
    },{
        name:'所在工种',
        value:'GName'
    },{
        name:'所在班组',
        value:'TeamName'
    },{
        name:'手机号码',
        value:'CellPhone'
    },{
        name:'最近打卡',
        value:'AttendTime'
    }
    ]
    let getform=ref({
        ProjectCode:store.getters.code,
        IconType:'考勤通道',
        EquipCode:'',
        page:1,
        count:10
    })
    let rightlabe=[{
        name:'通道名称',
        value:'GateName'
    },{
        name:'闸机类型',
        value:'GateType'
    },{
        name:'闸机数量',
        value:'GateNum'
    },{
        name:'监控类型',
        value:'MonitorType'
    },
    ]
    let falge=ref(-1)
    let picimg=ref(null)
    let bgcolor=ref({})
    let showfalge=ref(-1)//更多
    let dialogTableVisible=ref(false)
    let amplifyindex=ref(0)
    let option=ref({})
    let loading=ref(false)
    let tableDate=ref([])
    const background = ref(false)
    // 顶部
    let topbtn=ref(labelist('人员管理顶部'))
    let delogs=ref(null)
    let cadperon=ref(null)
    window.addEventListener('setthcolor', ()=> {
            // console.log('导航');
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--dialog-bg-color', bgcolor.value.delogcolor);
    
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // deloglists.value=counts.value[0]
        // bieechart()
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--dialog-bg-color', bgcolor.value.delogcolor);
    
    })
    const closes=()=>{
        // console.log('关闭');
        dialogTableVisible.value=false
    }
    const getlocation=async()=>{
        let loction=[]
        const {data:res}=await gettable('GetHomePageIconPosition',getform.value)
        if (res.code=="1000") {
            // loction=res.data
            // // loction
            // loction.forEach((item,index)=>{
            //     switch (item.IconType) {
            //         case '考勤通道':
            //             item.src=require('@/assets/img/home/<USER>/tower.png')
            //             item.src1=require('@/assets/img/home/<USER>/tower1.png')

            //             break;
            //     }
            //     // item.
            // });
            // imglist.value=loction
        }
    }
    const getdetil=async()=>{
        const {data:res}=await gettable('GetWorkerManagerInfo',getform.value)
        if (res.code=="1000") {
            forms.value=res.data
            tableDate.value=res.data.AttendRecordTable
            Total.value=res.Total
            bieechart(forms.value)
        }else{
             tableDate.value=[]
             Total.value=0
        }
    }
    const pic=(val)=>{
        picimg.value.piclist(val)
    }
    const bieechart=(val)=>{
        // console.log('获取图表',val);
        
        var echarts = require('echarts');
        if (val.Similarity.length>0) {
            
        option.value = {
            series: [
                {
                type: 'gauge',
                startAngle:210,
                endAngle: -30,
                center: ['50%', '50%'],
                radius: '90%',
                min: 0,
                max: 1,
                splitNumber: 20,
                axisLine: {
                    lineStyle: {
                    width: 0,
                    color: [
                        [val.Similarity[0].value,'#07B1CB'],
                        [1, '#ccc'],
                    ]
                    }
                },
                pointer: {
                    icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                    length: '12%',
                    width:0,
                    offsetCenter: [0, '-60%'],
                    itemStyle: {
                    color: 'auto'
                    }
                },
                axisTick: {
                    length: 12,
                    splitNumber:3,
                    lineStyle: {
                    color: 'auto',
                    width: 2,
                    cap:'round'
                    }
                },
                splitLine: {
                    length: 22,
                    distance: 3,
                    lineStyle: {
                    color: 'auto',
                    width: 3,
                    cap:'round'
                    }
                },
                axisLabel: {
                    color: '#07B1CB',
                    fontSize: 20,
                    show:false,
                    distance: -60,
                    rotate: 'tangential',
                },
                detail: {
                    fontSize: 30,
                    offsetCenter: [0, '-10%'],
                    valueAnimation: true,
                    formatter: function (value) {
                    return Math.round(value * 100) + '%';
                    },
                    color: 'inherit'
                },
                data: val.Similarity
                },
            ]
            };
        }

    }
    const mouseenter=(e,index)=>{
        falge.value=index
    }

    const mouseleave=(e,index)=>{
        falge.value=-1

    }
    const open=(item)=>{
        // console.log('打开',item);
        getdetil()
        dialogTableVisible.value=true
    }
    const btns=(val)=>{
        // console.log('详情',val);cadperon
        if (val.name!='人员定位') {
            delogs.value.showdelog(val)
        }else{
            cadperon.value.showdelog(val)

        }
    }
    const amplifyopen=(val)=>{
        amplifyindex.value=val
        // emit.
        ctx.emit("getamplify1", val);
    }
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
    const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        getdetil()
        }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        getform.value.page=val

        getdetil()

        }
    return{
        dialogTableVisible,
        amplifyindex,
        bgcolor,
        picimg,
        showfalge,
        imglist,
        falge,
        leftlable,
        option,
        rightlabe,
        loading,
        tableDate,
        getform,
        background,
        forms,
        Total,
        topbtn,
        delogs,
        cadperon,

        mouseenter,
        mouseleave,
        open,
        amplifyopen,
        bieechart,
        tableRowClassName,
        handleSizeChange,
        handleCurrentChange,
        getdetil,
        pic,
        btns,
        closes
    }
}
}
</script>
<style lang="scss">
.personwqmit{
    margin-top: 2%!important;
    // background: rgba(2, 193, 253, 0.24)!important;
    opacity: 1;
    box-sizing: border-box!important;
    .el-dialog__header{
        display: none!important;
    }
    .el-dialog__body{
        padding: 10px!important;
    }
    // .workerpopove{
    .el-table{
            --el-table-tr-bg-color:transparent!important;
            // border:1px solid transparent!important;
            // --el-table-border-color:transparent;
            --el-table-row-hover-bg-color:transparent;
            --el-table-bg-color:transparent;
        }
    .el-table .warning-row {
        //   --el-table-tr-bg-color: #000 !important;
        background: rgba(15, 43, 63, 0.6)!important;
        }

// }
    
}
</style>
<style lang="scss" scoped>

@mixin widths($width, $height) {
    width:$width;
    height:$height;
}
@mixin columns($columns) {
    display: grid;
    grid-template-columns: $columns;
    
}
.concer{
    // width: 100%;
    // height: 100%;
    @include widths(100%, 100%);
    &-top{
        @include columns(repeat(9,11.1%));
        margin-top: 10px;
        font-size: 14px;
        .iconfont{
            font-size: 18px!important;
        }
        .el-button{
            padding: 3px 3px!important;
            justify-content: space-evenly!important;
        }
    }
}
.iconfont{
    font-size: 45px!important;
    position: absolute;
    left: 2%;
}
.imgpoting{
    position: absolute;
    // width: 65px;
    // height: 75px;
    @include widths(65px, 75px);

}
.tower:hover{
    background-image: url('@/assets/img/home/<USER>/tower1.png');
    // width: 65px;
    // height: 75px;
    @include widths(65px, 75px);

}
.Similarity{
    @include widths(100%, 100%);
}
.Similaritysum{
    @include widths(100%, 100%);
    position: relative;
    &-p{
    position: absolute;
    bottom: 20%;
    left: 38%;
    font-size: 20px;
    font-weight: bold;
    }
}

.personwqmit{
    // .heaereq{
    //     position: relative;
    //     // width: 140px;
    //     // height: 30px;
    //     @include widths(140px, 30px);

    //     top: 42px;
    //     left: -12px;
    //     background: #000;
    //     clip-path: polygon(15% 0, 85% 0, 100% 100%, 100% 100%, 0 100%);
    // }
    // .heaereq-one{
    //     position: relative;
    //     top: -83px;
    //     height: 0px;
    //     .titlefont{
    //         padding: 2px;
    //         font-weight: bold;
    //         line-height: 26px;
    //         // color: #fff;
    //     }
    //     .toplist{
    //         display: flex;
    //         .iconone{
    //             position: absolute;
    //             font-size: 39px;
    //             color: #fff;
    //             top: 35px;
                
    //         }
    //         &-icon{
    //             left: 0;
    //         }
    //         &-icon1{
    //             right: 20px;
    //         }
    //     }
    // }
    &-body{
        // display: grid;
        // grid-template-columns: 50% 50% 100%;
        @include columns(50% 50% 100%);

        .lastdiv{
            // grid-row: 2 / 3;
        grid-column: 1 / 3;
        margin:10px 20px;

        }
        &-two{
        // display: grid;
        // grid-template-columns: 40% 60%;
        @include columns(40% 60%);

        margin:10px 20px;
        &-p{
            text-align: start;
            margin: 5px 0;
        }
    }
    &-three{
        // display: grid;
        // grid-template-columns:30% 40% 30%;
        @include columns(30% 40% 30%);

        margin:10px 20px;
        justify-items: center;
    }
    }
    
    .icontop{
        // width: 100px;
        // height: 2px;
        @include widths(100px, 2px);
        
        position: absolute;
        left: 20px;
    }
    .bor{
        // width: 38px;
        // height: 2px;
        @include widths(38px, 2px);
        
    }
    .icontop1{
        transform: rotate(-56deg);
        position:absolute;
        left: -8px;
        top: 16px;
    }
    .icontop2{
        transform: rotate(56deg);
        position:absolute;
        left: 111px;
        top: 16px;
    }
    &-header{
        margin:10px 20px;
        // width: 93%;
        // height: 50px;
        @include widths(93%, 50px);
        
        display: flex;
        justify-content: space-between;
        align-items: center;
        p{
            font-size: 18px;
            font-weight: bold;
        }
    }
    &-body-one{
        display: flex;
        align-items: center;
        
        }
.iconall{
    // width: 80px;
    // height: 80px;
    @include widths(80px, 80px);

    border-radius: 100%;
}
.iconall{
grid-row: 1 / 4;
display:flex;
justify-content: center;
align-items: center;
p{
    font-size: 35px;
    font-weight: bold;
    color: #fff;
}
}
.attenttong{
margin: 10px 20px;
// position: relative;
}
.play{
    // width: 260px;
    // height: 150px;
    @include widths(260px, 150px);

    border: 1px solid #ccc;
}

}
</style>