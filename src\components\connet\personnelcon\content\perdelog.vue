<template>
    <el-dialog v-model="dialogTableVisible"   destroy-on-close
      class="delogss" :width="widths.includes(title)?'50%':'70%'">
      <selection ref="selections" @colses="closes" :titles="title"></selection>
      <div v-if="falge=='0'" class="datedelog bodybottom bules" :style="getstyle">
          <template v-if="title=='在场人员变动情况'">
            <el-button  v-for="(item,index) in Twolayers" :key="index" size="small" @click="changes(index)" :type="indexs==index?'primary':'default'">{{item}}</el-button>
          </template>
          <template v-for="(item,index) in serch" :key="index">
              <div class="serch" v-if="item">
                  <span>{{item.name}}:</span>
                  <el-input v-if="item.type==1" v-model="getform[item.value]" :style="getwidth(item)"  placeholder="请输入关键字" size="small" clearable></el-input>
                  <el-date-picker v-if="item.type==3" v-model="getform[item.value]" type="date" size="small" placeholder="选择日期"
                   format="yyyy-MM-dd" value-format="yyyy-MM-dd"  :style="getwidth(item)"  />
                  <el-select v-else-if="item.type==2" popper-class="bules" size="small" :popper-append-to-body="true"
                   clearable :style="getwidth(item)" v-model="getform[item.value]"
                    class="m-2" placeholder="请选择" >
                          <el-option  v-for="(items,index) in item.list || []" :key="index"
                          :label="items.name" :value="items.value" />
                      </el-select>
              </div>
          </template>
          <el-button type="primary" v-if="btns.includes(title)" size="small" @click="search">搜索</el-button>
          <el-table :data="tableDate" class="cursor" :style="['width: 100%',`color:${bgcolor.font};
              --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
              :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}" max-height="550px"
              empty-text="暂无数据" @row-click="open"
              v-loading="loading"
              >
              <template #empty>
                  <el-empty></el-empty>
              </template>
              <el-table-column v-for="(item,index) in labelist1" :key="index" 
              :width="item.widths" :prop="item.value" :label="item.name" align="center"> 
              <!-- <template #default="scope">
                  <span v-if="item.type=='1'" :style="getcolor(item,scope.row)">{{scope.row[item.value]}}</span>
                  <div v-else-if="item.name=='仓库预警'" class="round" :style="getround(item,scope.row)"></div>
                  <span v-else>{{scope.row[item.value]}}</span>
              </template> -->
              </el-table-column>
              <!-- <el-table-column label="操作" align="center" v-if="opter">
                  <template #default="scope">
                      <el-button type="primary" text size="small" @click="open(scope.row)">查看</el-button>
                  </template>
              </el-table-column> -->
          </el-table>
          <el-pagination  v-model:current-page="getform.page" popper-class="bules" v-model:page-size="getform.count" 
          :page-sizes="[10, 20, 40, 50, 100]"  :background="false" layout="total, sizes, prev, pager, next, jumper"
          :total="total"  @size-change="handleSizeChange"  @current-change="handleCurrentChange"/>
      </div>
      <!-- <div v-else class="datedelog bodybottom bules lable" :style="getstyle">
          <h2>{{title}}</h2>
          <div v-for="(item,index) in labeform" class="text" :key="index" :style="getcloume(item)">
              <span v-if="item.name">{{item.name}}：</span>
              <span v-if="!item.img&&item.name">{{addform[item.value]}}</span>
              <img v-if="!item.img&&item.value1" :src="addform[item.value1]" class="cursor"
               @click="pic(addform[item.value1])" alt="" style="width: 150px;height: 100px;">
              <template v-if="item.img">
                  <img v-for="(its,i) in addform[item.value]" :key="i" :src="its" alt="" 
                  style="width: 150px;height: 100px;" class="cursor" @click="pic(its)">
              </template>
              <el-scrollbar height="300px" v-if="item.type==2">
                  <div v-for="(all,ii) in addform?.materialsInfo" :key="ii" class="lable">
                      <h2>材料{{ ii+1 }}</h2>
                      <div v-for="(table,index) in tablelist" :key="index" class="text"> 
                          <span>{{table.name}}：{{all[table.value]}}</span>
                      </div>
                  </div>
              </el-scrollbar>
          </div>
      </div> -->
      <picimg ref="picimgs"></picimg>
      </el-dialog>
  </template>
  
  <script setup>

  import selection from "@/components/connet/Common/selection.vue";
  import picimg from "@/components/connet/Common/picimg.vue";
  import { onMounted, ref, computed } from 'vue';
  import { labelist } from "@/components/connet/personnelcon/content/labeles.js";
  import { gettable,setdata,deldata} from "@/network/api/requestnet";
  import store from "@/store";
  import { updateFormListFields } from '@/utils/dataTransform'
  let dialogTableVisible=ref(false)
  let bgcolor=ref({})
  let title=ref('')
  let tableDate=ref([])
  let loading=ref(false)
  let labelist1=ref([])
  let serch=ref([])
  let total=ref(0)
  let titleist=[]
  let falge=ref(0)//0为供货商验收统计，1为详情
  let getform=ref({
      page:1,
      count:10,
      SupplierName:'',
      ProjectCode:store.getters.code,
      InUserName:store.getters.username,
      ReceiveTime:"",//时间
      SupplierName:"",//供货商  必传
      SearchStr:"",//关键字
      MaterialClass:"",///材料分类
      DJSource:'',
      EquipCode:'',
      IconType:'',
      Status:'',
      WorkTypeCode:''
  
  })
  let widths=['']
  let url=ref('')
  let history=ref([])
  let currentLevel=ref(0)
  // 添加物料验收记录标记
  let isMaterialRecord=ref(false)
  let labeform=ref([])
  let onelist=[]
  let twolist=[]
  let geturl=''
  let addform=ref({})
  let picimgs=ref(null)
  let tablelist=ref([])
  let opter=true
  let Twolayers=['考勤人员' ,'未考勤人员']
  let toptitle=ref('')
  let btns=[]
  let indexs=ref(0)
  let allow=['工种考勤分析','班组考勤统计'] // 允许的标题列表
  let tips=ref({}) // 用于存储提示信息

  window.addEventListener('setthcolor', ()=> {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
  })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getform.value.ProjectCode=store.getters.code
      getform.value.InUserName=store.getters.username
  })
  const showdelog=(val,titleText)=>{
      console.log('titleText',val,titleText);
      toptitle.value=titleText
      tips.value=val
      title.value=titleText
      titleist[0]=titleText
      falge.value=0
      opter=true
      for (const key in getform.value) {
        getform.value[key] = ''; // 清空所有表单字段
      }
      getform.value.InUserName=store.getters.username
      getform.value.ProjectCode=store.getters.code
      getform.value.page=1
      getform.value.count=10
      labelist1.value=labelist(titleText)
      // 初始化搜索配置 - 确保serch数组不为空
      serch.value = []
      // 初始化历史记录和导航层级
      history.value = []
      currentLevel.value = 0
      // 重置物料验收记录标记
      isMaterialRecord.value = false
      getform.value.Status='1'
      
      switch (titleText) {
          case '工种考勤分析':
              url.value='GetWorkerTypeRateTable'
              break;
          case '班组考勤统计':
              url.value='GetTeamTypeRateTable'
            //   getform.value.SupplierName=val.SupplierName
              break;
           case '建筑工人':
           case '管理人员':
              labelist1.value=labelist('在场人员统计')
              url.value='GetProjectWorkerTableByParam'
              break;
           case '考勤人数':
                title.value='在场人员变动情况'
                Twolayers=['考勤人员' ,'未考勤人员']
              labelist1.value=labelist('考勤人数')
              getform.value.Date=val.name
              getform.value.Status='1'
              getform.value.IsQianTai='前台'
              url.value='GetAttendWorkerTableByStatus'
              break;
            case '在场人数':
                title.value='在场人员变动情况'

              labelist1.value=labelist('在场人员统计')
              labelist1.value[2]={
                    name:'所属参建单位',
                    value:'CorpName',
                    widths:''
                }
              Twolayers=['在场人员','离场人员']
              getform.value.Date=val.name
              getform.value.Status='1'
              getform.value.IsQianTai='前台'
              url.value='GetProjectWorkerTableByParam'
              break;
            case '人员年龄':
                title.value=val.name

              labelist1.value=labelist('人员年龄')
            //   getform.value.Date=val.name
            //   getform.value.Status='1'
            //   getform.value.IsQianTai='前台'
              url.value='GetProjectWorkerTableByParam'
              break;

            //   在场人员变动情况

      }
      getdetable()
      dialogTableVisible.value=true
  }
  
  const search=()=>{
      // console.log('搜索',getform.value);
      getform.value.page=1
      getform.value.count=10
      getdetable()
  }
  const form={
                name:'所属参建单位',
                value:'CorpName',
                widths:''
            }
  // 获取材料下拉框
  const changes=async(val)=>{
    // console.log('数据',toptitle.value);
    
    indexs.value=val
    labelist1.value=labelist('考勤人数')
    if (toptitle.value == '在场人数') {
        labelist1.value=labelist('在场人员统计')
        labelist1.value[2]=form
        }
    if (val=='0') {
        getform.value.Status='1'
    }else{
        if (title.value === '在场人员变动情况'&&toptitle.value == '考勤人数') {
            // 修改第3列为"所属参建单位"
            labelist1.value[2]=form
            labelist1.value = labelist1.value.slice(0, 5)
            // 然后添加新的列
            let newColumns = [{
                    name:'连续未考勤天数',
                    value:'wkqts',
                    widths:''
                },{
                    name:'最近考勤日期',
                    value:'MaxDate',
                    widths:''
                }
            ]
            labelist1.value.push(...newColumns)
        }
        getform.value.Status='0'
    }
    getdetable()
  }
   const pic=(val)=>{
      let imgtype=['png','jpg','jpeg','gif','bmp']
      if (val) {
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1).toLowerCase()
          if (imgtype.includes(file)) {
              // console.log('获取',val);
              picimgs.value.piclist(val)
          }else{
              let type = val.substring(val.lastIndexOf('.') + 1).toLowerCase()
              
              if (type !== 'mp4') {
                  window.open('https://f.zqface.com/?fileurl='+val,'_slef')
              }
  
          }
      }
  }
  const getcloume=(val)=>{
      // 如果 val.name 在 onelist 数组中，合并三列
      if (onelist.includes(val.name)) {
          return 'grid-column: 1 /span 3';
      }
      else if (twolist.includes(val.name)) {
          return 'grid-column: 1 /span 2';
      }
      // 如果都不在，则不合并列
      return '';
  }
   const getdetable=async()=>{ 
      loading.value=true
      const {data:res}=await gettable(url.value,getform.value)
      // console.log('获取数据',res);
      loading.value=false
      tableDate.value=res.data
      total.value=res.Total || 0
   }
  //  获取详情
   const getdetil=async(val)=>{
      let GUID={
          GUID:val.GUID,
          ProjectCode:store.getters.code,
      }
      const {data:res}=await gettable(geturl,GUID)
      // console.log('获取数据',res);
      if (res.code=='1000') {
          addform.value = Array.isArray(res.data) ? res.data[0] : res.data
      }
   }
   const getstyle=()=>{
      return `border:2px solid ${bgcolor.value.titlecolor};
      background:rgba(${bgcolor.value.delogcolor},0.35)`
   }
   const getwidth=(val)=>{
      if (val.widths) {
          return `width: ${val.widths}px`
      }else{
          return 'width: 200px'
      }
   }
   const getround=(val,row)=>{
      if (row[val.value]=='0') {
          return 'background-color: #1B9E35'
      }else if(row[val.value]=='1'){
          return 'background-color: red'
      }
   }
   // 显示磅单详情的公共函数
   const showReceiptDetail = (row) => {
    //   falge.value = 1
    //   title.value = `磅单编号 ${row.ReceivingCode}`
    //   labeform.value = labelist('磅单详情')
      
    //   if (row.DJSource == '移动收料') {
    //       geturl = 'GetMoveReceiveDetail'
    //       labeform.value = labelist('移动收料详情')
    //       tablelist.value = labelist('移动收料材料')
    //   } else {
    //       geturl = 'GetReceiveDetail'
    //   }
      getdetil(row)
      return true // 返回true表示已处理，调用处可以直接return
   }
   
   const open=(row)=>{
    //   console.log('查看', title.value);
      opter=true
      toptitle.value=''
      getform.value.Status='1'
      // 简化逻辑：直接处理所有情况
      if (allow.includes(title.value)) {
          // 保存当前页面状态到历史记录
          if (currentLevel.value <= 0) {
              saveCurrentState();
              currentLevel.value++;
          }

          switch (title.value) {
              case '工种考勤分析':
                  labelist1.value = labelist('工种考勤详情');
                  falge.value=0
                  getform.value.WorkType=row.WorkType
                  getform.value.WorkTypeCode=row.WorkType
                  
                  url.value='GetProjectWorkerTableByParam'
                  title.value = row.GName;
                  break;
              case '班组考勤统计':
                  labelist1.value = labelist('工种考勤详情');
                  falge.value=0
                  getform.value.TeamSysNo=row.TeamSysNo
                  url.value='GetProjectWorkerTableByParam'
                  title.value = row.TeamName;
                  break;
          }
      }

      // 无条件调用getdetable，确保loading总是触发
    //   console.log('调用getdetable，loading应该会触发');
    if (currentLevel.value <= 1) {
      getdetable();
        
    }
    //   getdetable();
  }
   const getcolor=(val,row)=>{
      if (val.name=='偏差情况') {
          if (row.Deviation=='负偏差') {
              return 'color:  #01C2FF'
          }else if(row.Deviation=='超负差'){
              return 'color:  red'
          }else{
              return 'color:  #008000'
          }
      }
      return ''
   }
  
   // 保存当前页面状态到历史记录的函数
   const saveCurrentState = () => {
      history.value.push({
          title: title.value,
          labelist1: [...labelist1.value],
          serch: [...serch.value],
          url: url.value,
          getform: {...getform.value},
          tableDate: [...tableDate.value],
          total: total.value,
          level: currentLevel.value
      });
      // console.log('保存状态:', title.value, '层级:', currentLevel.value);
   }
  
  /**
   * 恢复页面状态的辅助函数
   * @param {Object} state - 要恢复的状态对象
   * @param {boolean} clearHistory - 是否清空历史记录
   * @param {number} newLevel - 要设置的新导航层级
   */
  const restoreState = (state, clearHistory = false, newLevel = null) => {
      // 恢复基础状态
      falge.value = 0;
      opter = true;
      
      // 恢复所有页面状态
      title.value = state.title;
      labelist1.value = state.labelist1;
      serch.value = state.serch;
      url.value = state.url;
      getform.value = state.getform;
      tableDate.value = state.tableDate;
      total.value = state.total;
      
      // 设置导航层级
      currentLevel.value = newLevel !== null ? newLevel : state.level;
      
      // 如果需要清空历史记录
      if (clearHistory) {
          history.value = [];
      }
      
      // 重新获取数据
      getdetable();
  };
  const closes = () => {
      // 特殊情况：物料验收记录的详情页直接返回第一层
      if (isMaterialRecord.value && falge.value === 1 && history.value.length > 0) {
          const firstLayerState = history.value.pop();
          restoreState(firstLayerState, true, 0);
          return;
      }
      
      // 常规情况：根据历史记录决定返回上一页或关闭对话框
      if (history.value.length > 0) {
          const prevState = history.value.pop();
          restoreState(prevState);
      } else {
          // 没有历史记录，关闭对话框
          dialogTableVisible.value = false;
      }
  };
  
  const tableRowClassName=({row,rowIndex,})=>{
          // console.log('获取当前行',row,rowIndex);
          if (rowIndex%2 != 0) {
              return 'warning-row'
          }
          return ''
      }
  const handleSizeChange = (val) => {
      console.log(`${val} 显示多少页`)
      getform.value.count=val
      getform.value.page=1
      getdetable()
      }
  const handleCurrentChange = (val) => {
      console.log(`选择第几: ${val}`)
      getform.value.page=val
      getdetable()
      }
  defineExpose({
    showdelog
  })
//   return {
//     dialogTableVisible,
//     bgcolor,
//     title,
//     tableDate,
//     loading,
//     labelist1,
//     serch,
//     total,
//     falge,
//     getform,
//     widths,
//     url,
//     history,
//     currentLevel,
//     isMaterialRecord,
//     labeform,
//     addform,
//     picimgs,
//     tablelist,
//     opter,
//     toptitle,
//     showdelog,
//     search,
//     getMaterial,
//     pic,
//     getcloume,
//     getdetable,
//     getdetil,
//     getstyle,
//     getwidth,
//     getround,
//     showReceiptDetail,
//     open,
//     getcolor,
//     saveCurrentState,
//     restoreState,
//     closes,
//     tableRowClassName,
//     handleSizeChange,
//     handleCurrentChange
//   }
// }
// }
  </script>
  <style lang="scss" scoped>
  .serch{
      color: #fff;
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
  }
  .bodybottom{
      text-align: left;
      h2{
          color: #fff;
          margin-bottom: 10px;
      }
  }
  .round{
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #01C2FF;
  }
  .lable{
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      h2{
          grid-column: 1 /span 3;
          color: #fff;
      }
  }
  .text{
      color: #01C2FF;
      margin: 20px 10px;
  }
  .cursor{
      margin: 5px;
  }
  .el-scrollbar{
      grid-column: 1 /span 3!important;
  
  }
  </style>