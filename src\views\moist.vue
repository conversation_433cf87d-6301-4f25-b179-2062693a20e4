<script>
let baseUrl;
let httpsurl;
let empower;
if (process.env.VUE_APP_ENVIRONMENT === "productionEnv") {
  // 生产包环境
  baseUrl = "https://ai.zqface.com/smakt/index.html#/login";
  httpsurl="https://ai.zqface.com"

} else if (process.env.VUE_APP_ENVIRONMENT === "testEnv") {
  // 测试包环境
  baseUrl = "https://ai.zqface.com/smakt/index.html#/login";
  httpsurl="https://ai.zqface.com"

} else if (process.env.VUE_APP_ENVIRONMENT === "developmentEnv") {
  // 开发包环境
  baseUrl = "/#/login";
  httpsurl="/aiot"
  

}
// window.location.href=httptz
export default {
  url: baseUrl,
  httpsurl:httpsurl
};
</script>
