<!-- components/UnityGame.vue -->
<template>
  <div class="webgl-content">
    <div id="unityContainer" :style="containerStyle"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式状态
const unityInstance = ref(null)
const containerStyle = ref({
  width: '1280px',
  height: '720px'
})

// 加载 Unity 脚本
const loadUnityScripts = () => {
  return new Promise((resolve) => {
    // 加载 UnityProgress.js
    const progressScript = document.createElement('script')
    progressScript.src = '/ai/TestWeb/TemplateData/UnityProgress.js'
    document.head.appendChild(progressScript)

    // 加载 UnityLoader.js
    const loaderScript = document.createElement('script')
    loaderScript.src = '/ai/TestWeb/Build/UnityLoader.js'
    loaderScript.onload = () => resolve()
    document.head.appendChild(loaderScript)
  })
}

// 发送消息到 Unity
const sendToUnity = (msg) => {
  if (msg === null) {
    msg = '打开测试标签'
  }
  if (unityInstance.value !== null) {
    unityInstance.value.SendMessage("UnityWeb", "ReceiveFromWeb", msg)
  }
}

// 接收来自 Unity 的消息
const receiveFromUnity = (msg) => {
  const initD = {
    detail: { "hazcheeseburger": true },
    bubbles: true,
    cancelable: true,
    composed: true
  }
  const evt = new CustomEvent(msg, initD)
  window.top.dispatchEvent(evt)
  
  // 测试
  console.log('从Unity收到消息:',evt, msg)
  sendToUnity("打开测试标签")
}

// 暴露方法给父组件使用
defineExpose({
  sendToUnity
})

onMounted(async () => {
  // 加载 Unity 脚本
  await loadUnityScripts()
  
  // 初始化 Unity
  unityInstance.value = UnityLoader.instantiate("unityContainer", "/ai/TestWeb/Build/TestWeb.json", {
    onProgress: UnityProgress
  })

  // 添加与 Unity 通信的方法到 window 对象
  window.ReceiveFromUnity = receiveFromUnity
})

onBeforeUnmount(() => {
  // 清理工作
  if (unityInstance.value) {
    unityInstance.value.Quit()
  }
})
</script>

<style scoped>
.webgl-content {
  /* 可以从原始的 style.css 中复制相关样式 */
}
</style>