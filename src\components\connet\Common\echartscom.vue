<template>
  <div :id="ids" class="qwtj" ></div>
</template>
<script>
import { nextTick, onMounted, ref,getCurrentInstance, watch, onBeforeUnmount } from 'vue';
export default {
props:['ids','options1'],
// emits: ["popsdelog"],
setup(props, context){
    // let refsid=ref('')
    let aa= ref(props.options1);
    let myChart=null
    // console.log('数',aa);
    watch(()=>props.options1, (newVal, oldVal) => {
      // console.log('触发',props.options1);
      
      if (newVal) {
        // console.log('数据',newVal);
        nextTick(()=>{
            getecharts()
        })
      }
      },
    {immediate: true,deep: true})

    
    const getecharts=(val)=>{

        var echarts = require('echarts');
      //  let myChart = echarts.init(document.getElementById(props.ids));

       myChart = echarts.getInstanceByDom(document.getElementById(props.ids));
        if (myChart == null) {
          myChart = echarts.init(document.getElementById(props.ids));
        }
        // options.value=val
        // 移除所有已存在的点击事件监听器，避免重复绑定
        myChart.off('click');

        myChart.on('click', function(params) {
        // console.log("数据",params);
          if (params.seriesType ==='line') {
            // console.log('点');
            context.emit('popsdelog',params)
          }
        });
        myChart.setOption(props.options1);
        window.addEventListener("resize", function() {
          // console.log('有值',myChart._dom);
          
          myChart?.resize();
        });
    }
    onBeforeUnmount(()=>{
      myChart.dispose()

    })
    return{
        // refsid,
        // myChart,
        getecharts
    }
}
}
</script>
<style lang="scss" scoped>
.qwtj{
  width: 100%;
  height: 100%;
}
</style>