<template>
    <swiper class="swiperdp" 
         :slides-per-view="slidesPerView"
         :loop="true" 
         :loopedSlides="8"
         :direction="'vertical'" 
         @swiper="onSwiper"
         :autoplay="{
           delay: 1500,
           disableOnInteraction: false
         }" 
         :modules="modules" 
         :speed="800">
            <swiper-slide v-for="(row, index) in list" :key="index" class="product-slide">
                <img :src="`data:image/jpeg;base64,${row.HeadImage}`" alt="" style="width: 80px;height: 100px;">
                <span v-for="(item,index) in labels" :key="index">{{item.name+(row[item.value]??'')}}</span>
            </swiper-slide>
    </swiper>
</template>

<script setup>
import { gettable, setdata, deldata } from "@/network/api/requestnet";
import { ref, computed, getCurrentInstance, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
let modules=[Autoplay, Pagination, Navigation]
import { useStore } from 'vuex'
const store = useStore()
let getform=ref({
  ProjectCode:store.getters.code,
})
let list=ref([])
let labels=[
    {
        name:'姓名:',
        value:'WorkerName'
    },
    {
        name:'班组:',
        value:'TeamName'
    },{
        name:'兑换商品:',
        value:'GoodsName'
    },{
        name:'核销积分:',
        value:'Score'
    },{
        name:'商品兑换时间:',
        value:'RedeemTime'
    },
]

const slidesPerView = ref(4);

onMounted(()=>{
  let code= window.location.href?.split('?code=')[1]

  getform.value.ProjectCode= code
  
  getdata()
  checkFullscreen()
  window.addEventListener('resize', checkFullscreen)
})
const showdelog=()=>{
  // console.log('获取');
  
  getdata()
  checkFullscreen()
  window.addEventListener('resize', checkFullscreen)
}
onBeforeUnmount(() => {
  window.removeEventListener('resize', checkFullscreen);
});

const checkFullscreen = () => {
  if (window.innerHeight > 920) {
    slidesPerView.value = 5;
  } else {
    slidesPerView.value = 4;
  }
};

const getdata=async()=>{
  const {data:res}=await gettable('GetJFGoodsExchangeDetail',getform.value)
  console.log('获取售货机商品积分',res);
  list.value=res.data
}
const onSwiper = (swiper) => {
    // console.log('初始化Swiper，启动自动滚动');
    
    // 确保自动播放启动
    setTimeout(() => {
        swiper.autoplay.start();
    }, 100);
    
    // 监听自动播放状态
    swiper.on('autoplayStart', () => {
        // console.log('自动播放已启动');
    });
    
    swiper.on('autoplayStop', () => {
        // console.log('自动播放已停止，尝试重新启动');
        swiper.autoplay.start();
    });
};
defineExpose({
    showdelog
})
</script>

<style lang="scss" scoped>
.swiperdp{
  width: 100%;
  --swiper-slides-per-view: v-bind(slidesPerView);
  height: calc(var(--swiper-slides-per-view) * 120px);
  span{
    font-size: 14px;
  }
  span:nth-child(4){
    grid-row: 2;
    grid-column: 2/span 2;
}
}
img{
    grid-row: 1/span 3;
    grid-column: 1;
}
.product-slide {
  height: 120px!important; /* 图片高度(150px) + 文本高度 + 内边距 */
    display: grid;
    grid-template-columns: 15% 40% 40%;
    justify-items: start;
  box-sizing: border-box;
  align-items: center;
  padding: 10px;
}
</style>