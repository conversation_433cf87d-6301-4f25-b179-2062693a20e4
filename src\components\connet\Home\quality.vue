<template>
  <!-- 质量 -->
  <div class="quality padding family" :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="topforms"></Chamfering>
    
    <div class="quality-two">
      <div class="quality-two-top">
        <div class="quality-two-top1">
          <span @click="counts('质量问题记录','')" class="cursor">总计：{{forms.ZLQuestionCount}}</span>
          <span  @click="counts('问题整改记录','已整改')" class="cursor">已整改：{{forms.RectificationNum}}</span>
          <span style="color:red"> 整改率：{{forms.RectificationRate}}%</span>
        </div>
        <div class="quality-two-top2">

        </div>
      </div>
      <div class="echatr">
        <qualityechart v-if="falge"  :ids="'qualityechart'" 
        :refs="'qualityechart'" :options1="options"  @popsdelog="popsdelog"></qualityechart>
      </div>
      <delog ref="delogtable"></delog>
    </div>
    <Chamfering :homeindex="homeindex" :horn="0"></Chamfering>
  </div>
</template>

<script>
import qualityechart from "@/components/connet/Common/echartscom.vue";
import { nextTick, onMounted, reactive, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import delog from "@/components/connet/safety/content/delog.vue";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
props:['homeindex'],
components:{
  qualityechart,
  delog,
  Chamfering
},
setup(){
   let bgcolor=ref({})
    let options=ref(null)

    let getform=ref({
      ProjectCode:store.getters.code,

    })
    let quliet=ref([])
    let delogtable=ref(null)
    let falge=ref(false)
    let forms=ref({})
    let topforms=ref({
      url:require('@/assets/img/home/<USER>'),
      name:'质量巡检'
    })
  window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    // getecharts1()
    gettableqe()
  })

  const gettableqe=async()=>{
    // falge.value=false
    const {data:res}=await gettable('QualityCurve',getform.value)
        // console.log('返回数据',res);
        // quliet.value=res.data
        if (res.code=="1000") {
          forms.value=res.data[0]
        quliet.value=res.data
          
        }else{
          quliet.value=[]
        }
        nextTick(()=>{
          falge.value=true
          getecharts1()

          // falge.value=true
        })
  }
  const getecharts1=()=>{
      let addpro=[]
      let addpro1=[]
      let addpro2=[]
      let addprotime=[]
      // console.log('获取数据',quliet.value[0]);
      
    if (quliet.value.length==3) {
    addpro= quliet.value[0].qs.map((item)=>{
      return item.CurveCount
      })
    addpro1= quliet.value[1].qs.map((item)=>{
      return item.CurveCount
      })
    addpro2= quliet.value[2].qs.map((item)=>{
      return item.CurveCount
      })
    addprotime=quliet.value[0].qs.map((item)=>{
      return item.time
      })
      // console.log('虎丘',);
      
    }
     options.value={
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        itemGap: 24,
        textStyle: {
						fontSize: '12px',
						color: '#A8D6FF',
					},
        data: ['整改问题', '新增问题', '待整改问题']
      },
      grid: {
        top:'20%',
        left: '3%',
        right: '4%',
        bottom: '8%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLine: {
						lineStyle: {
							color: 'rgba(3, 251, 255, 0.3)'
						}
            
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						interval: 0,
						// textStyle: {
						color:'#03FBFF',
						// },
						// 默认x轴字体大小
						fontSize: 12,
						// margin:文字到x轴的距离
						margin: 15
					},
        data: addprotime
      },
      yAxis: {
        type: 'value',
        axisTick: {
						show: false
					},
					axisLabel: {
						// textStyle: {
							color: '#03FBFF'
						// }
					},
					 splitLine :{    //网格线
              lineStyle:{
                  type:'dashed',
                  color:'rgba(3, 251, 255, 0.3)'   //设置网格线类型 dotted：虚线   solid:实线
              },
              show:true //隐藏或显示
          }
      },
      series: [
        {
          name: '整改问题',
          type: 'line',
          stack: 'Total',
          color:'#03FBFF',
          data: addpro1
        },
        {
          name: '新增问题',
          type: 'line',
          stack: '',
          color:'#C80202',
          data: addpro
        },
        {
          name: '待整改问题',
          type: 'line',
          stack: '',
          color:'#8A38F5',
          data: addpro2
        },
      ]
    }
  
  }
  const counts=(val,name)=>{
    delogtable.value.showdelog(val,0,name,'')
  }
  const popsdelog=(val)=>{
    // console.log('点击获取',val);
    
    delogtable.value.showdelog(val,0,'','')
  }

return{
  // heights,
  forms,
  falge,
  quliet,
  getform,
  bgcolor,
  options,
  delogtable,
  topforms,

  getecharts1,
  gettableqe,
  counts,
  popsdelog
}
}
}
</script>
<style lang="scss" scoped>
.quality{
  // font-family: 'HarmonyOS Sans SC';
  &-two{
    height: 78%;
    &-top1{
      font-size: 12px;
      color: #A8D6FF;
      margin: 5px;
      span{
        display: inline-block;
        width: 28%;
        margin: 5px;
      }
    }
  }
}
.echatr{
  width: 100%;
  height: 20vh;

}
</style>