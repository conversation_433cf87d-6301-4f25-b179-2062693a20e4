<template>
  <div :id="ids" class="echarts"></div>
</template>

<script setup>
import { onBeforeUnmount, ref,getCurrentInstance, onMounted, nextTick } from 'vue';
const $colorlist = getCurrentInstance().appContext.config.globalProperties.$colorlist

const props = defineProps({
  ids: {
    type: String,
    // default: 'chart1'
  },
  options: {
    type: Object,
    // default: () => ({})
  }
})
let myChart
const ids = ref(props.ids)
let keys=[]
let names=ref([])
let series=ref([])
let legends=ref([])
let typeline=[]
let lengname=[]
onMounted(()=>{

})
const showdelog=(val,value,type)=>{
    console.log('获取数据',value);
    let keys1=Object.keys(val?.data)
    // keys=keys1
    lengname=keys1.map((item)=>{return val?.data[item]?.name})
    // typeline=keys1.map((item)=>{return val?.data[item]?.type})
    switch (type) {
        case '售货机商品统计':
        typeline=[{
            type:'bar',
        }]
        lengname=[]    
            break;
    }
    // getserir(val,type)
    nextTick(()=>{
        getecahrts()
    })
}
// 饼状图
const getpie=(val,value)=>{
    console.log('获取数据',val,value);
    let echarts = require('echarts');
    // series.value=
}
const getserir=(val,value)=>{
        // console.log('获取数据',val,typeline);
        let echarts = require('echarts');
        series.value=[]
        names.value=[]
        let keys=Object.keys(val.data)
        names.value=val.data[keys[0]]?.map((item)=>{
            return item.name
        })
        series.value=keys.map((item,i)=>{
                // console.log('获取AQI数据',item);
                return {
                    name:lengname[item],
                    type: typeline[i].type,
                    data: val.data[item]?.map((item)=>{
                        return item.value
                    }),
                    itemStyle: {
                        borderRadius: [20, 20, 0, 0],
                        color:typeline[i].type=='bar'?new echarts.graphic.LinearGradient(
                            0, 0, 0, 1, // x1, y1, x2, y2 指定渐变的起点和终点位置
                            [
                                {offset: 0, color: typeline[i]?.color},   // 渐变起始颜色
                                {offset: 1, color: typeline[i]?.name}   // 渐变结束颜色
                            ]
                        ):$colorlist()[i]
                    },
                    
                }
            })
        legends.value=keys.map((item)=>{return lengname[item]})
        // console.log('获取颜色渐变',series.value);
    }
const getecahrts=()=>{
        let echarts = require('echarts');
        myChart = echarts.getInstanceByDom(document.getElementById(ids))
         if (myChart == null) {
            myChart = echarts.init(document.getElementById(ids));
         }

      let option = {
        title: {
            left:'2%',
            // text: porps.forms.toptwo,
            // subtext: units
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            right:'10%',
            data: legends.value
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: true,
            axisLine:{
                show:true,
            },
            data: names?.value
        },
        yAxis: yAxis1?.value,
        series: series?.value
        };

        myChart?.setOption(option)

        window.addEventListener("resize", function() {
          myChart?.resize();
        });
    }
defineExpose({
    showdelog
})
onBeforeUnmount(()=>{
  myChart?.dispose()
})

</script>
<style lang="scss" scoped>
.echarts{
    width: 100%;
    height: 100%;
}
</style>