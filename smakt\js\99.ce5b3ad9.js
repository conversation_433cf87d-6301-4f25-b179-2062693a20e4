"use strict";(self["webpackChunksmart"]=self["webpackChunksmart"]||[]).push([[99],{71099:function(e,t,a){a.r(t),a.d(t,{default:function(){return u}});var l=a(73396);const o={class:"mobile-table-page"};function n(e,t,a,n,d,r){const i=(0,l.up)("el-table-column"),c=(0,l.up)("el-table"),s=(0,l.Q2)("loading");return(0,l.wg)(),(0,l.iD)("div",o,[(0,l.wy)(((0,l.wg)(),(0,l.j4)(c,{data:n.filteredData,style:{width:"100%"}},{default:(0,l.w5)((()=>[(0,l.Wm)(i,{prop:"WorkerName",label:"姓名",width:"50"}),(0,l.Wm)(i,{prop:"IDCardNumber",label:"身份证号"}),(0,l.Wm)(i,{prop:"TeamName",label:"班组"}),(0,l.Wm)(i,{prop:"CorpName",label:"所属单位"})])),_:1},8,["data"])),[[s,n.loading]])])}a(46229),a(17330),a(62062);var d=a(44870),r=a(57597),i={setup(){(0,d.iH)(""),(0,d.iH)("");const e=(0,d.iH)(!1),t=(0,d.iH)("auto");(0,d.iH)([]);let a=(0,d.iH)({ProjectCode:"",led:""}),o=(0,d.iH)([]);const n=()=>{try{const e=c();e&&e.code&&(a.value.ProjectCode=e.code),e&&e.led&&(a.value.led=e.led),i()}catch(e){a.value.ProjectCode="330109202531313750",i()}};(0,l.bv)((()=>{(0,l.Y3)((()=>{n(),s(),window.addEventListener("resize",s),window.addEventListener("popstate",(()=>{(0,l.Y3)((()=>{n()}))})),window.addEventListener("hashchange",(()=>{(0,l.Y3)((()=>{n()}))}))}))})),(0,l.YP)((()=>window.location.href),((e,t)=>{e!==t&&(0,l.Y3)((()=>{n()}))}),{immediate:!1});const i=async()=>{e.value=!0;const{data:t}=await(0,r.rT)("GetJKWorkerByProjectCode",a.value);o.value=t.data,e.value=!1},c=()=>{const e=window.location.href;if(!e.includes("?"))return{code:null,led:null};const t=e.split("?")[1],a=new URLSearchParams(t),l={code:a.get("code"),led:a.get("led")};return l},s=()=>{try{const e=window.innerHeight,a=120,l=40,o=e-a-l;t.value=o>0?o+"px":"400px"}catch(e){t.value="400px"}};return{loading:e,filteredData:o,tableHeight:t}}},c=a(40089);const s=(0,c.Z)(i,[["render",n],["__scopeId","data-v-3c8d5644"]]);var u=s}}]);