<template>
  <!-- 出勤 -->
  <div class="Attendance padding"  :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="topforms"></Chamfering>

    <div class="Attendance-two">
      <div class="Attendance-two-top">
        <div class="Attendance-two-top1">
          <span class="Attendance-two-top1-span">总计：{{countlist.Person}}</span>
          <div class="Attendance-two-top1-lend ">
            <div class="Attendance-two-top1-lend mage">
              <div class="Attendance-two-top1-lend1 bgcolorcz" ></div>
              <span>管理人员</span>
            </div>
            <div class="Attendance-two-top1-lend mage">
              <div  class="Attendance-two-top1-lend1 bgcolorsj"></div>
              <span>建筑工人</span>
            </div>
          </div>
        </div>
 
      </div>
      <div class="echatr">
        <!-- <div id></div> -->
        <echartsAttendance v-if="falge" :ids="'echartsAttendance'" :options1="attendees" :refsid="'echartsAttendance'" style="width:33%;height:60%"></echartsAttendance>
        <echartsAttendance v-if="falge" :ids="'echartsmangen'" :options1="mangen" :refsid="'echartsmangen'" style="width:33%;height:60%"></echartsAttendance>
        <echartsAttendance v-if="falge" :ids="'echartsconstruction'" :options1="construction" :refsid="'echartsconstruction'" style="width:33%;height:60%"></echartsAttendance>
      </div>
    </div>
	 <Chamfering :homeindex="homeindex"></Chamfering>
  </div>
</template>

<script>
import echartsAttendance from "@/components/connet/Common/echartscom.vue";
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
props:['homeindex'],
components:{
echartsAttendance,
Chamfering
},
setup(){
  let attendees=ref({})
  let mangen=ref({})
  let construction=ref({})
  let bgcolor=ref({})
  let falge=ref(false)
  let countlist=ref([])
  let getform=ref({
      ProjectCode:store.getters.code,

    })
  let topforms=ref({
	url:require('@/assets/img/home/<USER>'),
    name:'出勤情况'
  })
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    // getAttendance()
	getsunm()
    // getmangen()
    // getconstruction()
  })
  const getsunm=async()=>{
    const {data:res}=await gettable('GetAttendanceStatus',getform.value)
	// if (res.code) {
		countlist.value=res.data
	// }
	nextTick(()=>{
		falge.value=true
		getAttendance()
		getmangen()
    getconstruction()

	})

  }
  const getAttendance=()=>{
    let colorList=["#407FFF",
					"#F29961",]
	let countvalue=[]
	if (countlist.value.ZCList.length>0) {
		countvalue=[
			{
				name:'建筑工人',
				value:countlist.value.ZCList[1].value
			},
			{
				name:'管理人员',
				value:countlist.value.ZCList[0].value
			},
		]
	}
    let option = {
        	title: [{
							text: countlist.value.Person,
							textStyle: {
								color: "#fff",
								fontSize: 16,
							},
							itemGap: 10,
							left: "center",
							top: "54%",
						},
						{
							text: "在场人数",
							textStyle: {
								color: "#fff",
								fontSize: 13,
								fontWeight: "normal",
							},
							itemGap: 10,
							left: "center",
							top: "40%",
						},
					],
					tooltip: {
						trigger: "item",
            			position: 'bottom',
						formatter(params, ticket, callback){
				// console.log('自定义模版',params, ticket, callback);
				
                    //自定义模板
                    let html = ` 
                       
                        <span style="display:inline-block;margin-right:4px;
						border-radius:10px;width:10px;height:10px;
						background-color:${params.color};"></span>
                        <span style="margin-right:2%;">${params.name}</span>
                        <span style="margin-right:10%;">${params.value}</span>
                        `
                // callback(ticket, html) //返回自定义内容
				// console.log('自定义内容',html);
				
				return html
            }
					},
					series: [{
						type: "pie",
						center: ["50%", "50%"],
						radius: ["60%", "90%"],
						clockwise: true,
						avoidLabelOverlap: true,
						hoverOffset: 100,
						itemStyle: {
							normal: {
								color: function(params) {
									return colorList[params.dataIndex];
								},
							},
						},
            emphasis: {
                scale: true,  // 开启鼠标移入时的缩放效果
                focus: 'series', // 鼠标移入时聚焦于整个系列
                blurScope: 'coordinateSystem',
            },
						label: {
							show: false,
						},
						labelLine: {},
						data: countvalue,
					}, ],
    };
    attendees.value=option
  }
  const getmangen=()=>{
	// console.log('获取1',countlist.value.KQList);
	let countvalue=[]
	
    let colorList=["#407FFF","#F29961",]

	if (countlist.value.KQList.length>0) {
		countvalue=[
			{
				name:'建筑工人',
				value:countlist.value.KQList[1].value
			},
			{
				name:'管理人员',
				value:countlist.value.KQList[0].value
			},
		]
	}
	// console.log('考勤饼状图',countvalue);
	
    let option = {
  		title: [{
							text:countlist.value.kqNum,
							textStyle: {
								color: "#fff",
								fontSize: 15,
							},
							itemGap: 10,
							left: "center",
							top: "54%",
						},
						{
							text: "考勤人数",
							textStyle: {
								color: "#fff",
								fontSize: 14,
								fontWeight: "normal",
							},
							itemGap: 10,
							left: "center",
							top: "40%",
						},
		],
		tooltip: {
			trigger: "item",
			formatter(params, ticket, callback){
				// console.log('自定义模版',params, ticket, callback);
				
                    //自定义模板
                    let html = ` 
                       
                        <span style="display:inline-block;margin-right:4px;
						border-radius:10px;width:10px;height:10px;
						background-color:${params.color};"></span>
                        <span style="margin-right:2%;">${params.name}</span>
                        <span style="margin-right:10%;">${params.value}</span>
                        `
                // callback(ticket, html) //返回自定义内容
				// console.log('自定义内容',html);
				
				return html
            }
		},
		series: [{
						// hoverAnimation: false,
						type: "pie",
						center: ["50%", "50%"],
						radius: ["60%", "90%"],
						clockwise: true,
						avoidLabelOverlap: true,
						hoverOffset: 15,
						itemStyle: {
							normal: {
								color: function(params) {
									return colorList[params.dataIndex];
								},
							},
						},
						label: {
							show: false,
						},
						labelLine: {},
						data:countvalue,
		}, ],
    };
    mangen.value=option
  }
  const getconstruction=()=>{
    let colorList=["#407FFF","#F29961",]
	let countvalue=[]
	

	if (countlist.value.RateList.length>0) {


		countvalue=[
			{
				name:'建筑工人',
				value:countlist.value.RateList[1].value
			},
			{
				name:'管理人员',
				value:countlist.value.RateList[0].value
			},
		]
	}
    let option = {
  			title: [{
				text: countlist.value.kqRate+'%',
				textStyle: {
					color: "#fff",
					fontSize: 15,
				},
				itemGap: 10,
				left: "center",
				top: "45%",
			},
			{
				text: "考勤率",
				textStyle: {
					color: "#fff",
					fontSize: 14,
					fontWeight: "normal",
				},
				itemGap: 10,
				left: "center",
				top: "32%",
			},
		],
		tooltip: {
			trigger: "item",
			formatter(params, ticket, callback){
				// console.log('自定义模版',params, ticket, callback);
				
                    //自定义模板
                    let html = ` 
                       
                        <span style="display:inline-block;margin-right:4px;
						border-radius:10px;width:10px;height:10px;
						background-color:${params.color};"></span>
                        <span style="margin-right:2%;">${params.name}</span>
                        <span style="margin-right:10%;">${params.value}%</span>
                        `
                // callback(ticket, html) //返回自定义内容
				// console.log('自定义内容',html);
				
				return html
            }
		},
		series: [{
			// hoverAnimation: false,
			type: "pie",
			center: ["50%", "50%"],
			radius: ["60%", "90%"],
			clockwise: true,
			avoidLabelOverlap: true,
			hoverOffset: 15,
			itemStyle: {
				normal: {
					color: function(params) {
						return colorList[params.dataIndex];
					},
				},
			},
			label: {
				show: false,
			},
			labelLine: {},
			data: countvalue,
					}, ],
    };
    construction.value=option
  }
	return{
		falge,
		countlist,
		getform,
		bgcolor,
	attendees,
	topforms,
	mangen,
	construction,
	getAttendance,
	getmangen,
	getconstruction,
	getsunm
	}
}
}
</script>

<style lang="scss" scoped>
.Attendance{
&-two{
  height: 80%;
  &-top1{
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      font-size: 12px;
      color: #A8D6FF;
      margin: 10px;
      &-span{
        display: inline-block;
        width: 28%;
      }
      &-lend{
        display: flex;
        align-items: center;
        margin: 0 7px;
        // width: 28%;
      }
      .mage{
        width: 50%;
      }
      &-lend1{
          width: 18px;
          height: 10px;
          margin: 0 3px;
          // background: #000;
          border-radius: 20%;
      }
  }
  .bgcolorcz{
        background: #F29961;

    }
    .bgcolorsj{
          background: #4582ff;

    }
}
}
.echatr{
  display: flex;
align-items: center;
  width: 100%;
  height: 85%;
}
// #echartsBox{
//  width: 100%;
//  height: 100%; 
// }
</style>