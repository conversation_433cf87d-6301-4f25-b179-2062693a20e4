<template>
<!-- 天气 -->
  <div class="weater padding" :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="forms" @opens="opentable()"></Chamfering>
    
    <div class="weater-two">
      <div>
        <p>{{nowweater.name}}</p>
        <img :src="`https://ai-zqface-com-wjgl.oss-cn-hangzhou.aliyuncs.com/fillist/Realsystem/icons/${nowweater.icon}.png`" 
        alt="" style="width: 70px;height: 70px;">
        <p>{{nowweater.temp}}°C</p>
        <p>{{nowweater.text}}</p>
      </div>
      <div v-for="(item,index) in weater" :key="index">
        <p>{{item.name}}</p>
         <img :src="`https://ai-zqface-com-wjgl.oss-cn-hangzhou.aliyuncs.com/fillist/Realsystem/icons/${item.iconDay}.png`" 
         alt="" style="width:70px;height: 70px;">
        <p>{{item.tempMin}}°C~{{item.tempMax}}°C</p>
        <p>{{item.textDay}}</p>
      </div>
    </div>
    <delog ref="delogs"></delog>
    <Chamfering :homeindex="homeindex" :horn="0" ></Chamfering>
  </div>
</template>

<script>
import { onMounted, ref ,getCurrentInstance} from 'vue'
import { formatDateTime } from "@/utils/date";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/Home/content/delog.vue";
export default {
props:['homeindex'],
components:{
  Chamfering,
  delog
},
setup(props){
  let bgcolor=ref({})
  const $http = getCurrentInstance().appContext.config.globalProperties.$http
  let weater=ref([])
  let nowweater=ref({})
  let forms=ref({
    url:require('@/assets/img/home/<USER>'),
    name:'天气状况',
    text:'更多天气',
    lefs:'lefs',
    order:'2'
  })
  let delogs=ref(null)
  window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getweater()
      // console.log('获取天气',props.homeindex);
      forms.value.lefs=props.homeindex>4?'lefs':'rigs'
      forms.value.order=props.homeindex>4?'1':'2'
   })
   const opentable=()=>{
    delogs.value.showdelog(0,'天气状况')
   }
   const getweater=async()=>{
    let date=new Date()
    // let tody=date.getDate()
    let datashow=['今天','明天','后天']
    // console.log('获取日期',date.getDate());
			const {data:res1} = await $http.get('https://devapi.qweather.com/v7/weather/now?key=e0b66f1dcbbe4a76aea59aa878a59a29&location=101210101')
      // console.log('获取日期',res1);
      if (res1.code=="200") {
        nowweater.value=res1.now
        nowweater.value.name=formatDateTime(date,'HH:mm')
      }
      const {data:res}= await $http.get(' https://devapi.qweather.com/v7/weather/3d?location=101210101&key=e0b66f1dcbbe4a76aea59aa878a59a29')
      // console.log('获取三天',res);
      if (res.code=="200") {
        weater.value=res.daily
        // console.log('获取日期',getDay(1),getDay(2),getDay(0));
        weater.value.map((item,index)=>{
          if (item.fxDate==getDay(index)) {
            item.name=datashow[index]
          }
        })
      }
   }
   const  getDay=(day)=>{
        var today = new Date()
        var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day
        // Date.setTime(时间戳)：设置当前日期的时间
        today.setTime(targetday_milliseconds)
        // console.log('today=', today) // today= Sun Mar 05 2023 16:14:56 GMT+0800 (中国标准时间)
        var tYear = today.getFullYear() // 年
        var tMonth = today.getMonth() // 月
        var tDate = today.getDate() // 日
        tMonth = doHandleMonth(tMonth + 1)
        tDate = doHandleMonth(tDate)
        // console.log('返回年月日=', tYear + '-' + tMonth + '-' + tDate)
        return tYear + '-' + tMonth + '-' + tDate
    }
    const doHandleMonth=(month)=>{
      var m = month
        if (month.toString().length == 1) {
          m = '0' + month
        }
        return m
    }
  return{
    bgcolor,
    weater,
    nowweater,
    forms,
    delogs,

    getweater,
    getDay,
    doHandleMonth,
    opentable

  }
}
}
</script>

<style lang="scss" scoped>
.weater{
.iconweater{
  font-size: 50px;
  display: inline-block;
  width: 50px;
  height: 50px;
  margin: 15px;
}
&-two{
  height: 85%;
  display: grid;
  grid-template-columns: repeat(4,25%);
  padding: 10px;
  font-size: 14px;
  p{
    margin: 15px 10px;
  }
}
}
</style>