<template>
  <div class="headertop">
        <div class="toplist">
           <div class="heaereq" :class="{'themed-bg': bgcolor.delogcolor}">
               <div class="icontop1 bor themed-border"></div>
               <div class="icontop themed-border"></div>
               <div class="icontop2 bor themed-border"></div>
                <p v-if="titles">{{titles}}</p>
           </div>
        </div>
        <div class="closedelog cursor" v-if="!falge" :class="{'themed-close': bgcolor.hovercor}" @click="handleClose()">
            <el-icon class="closeicon"><CloseBold /></el-icon>
        </div>
    </div>
</template>

<script>
import { onMounted, ref, onUnmounted } from 'vue'
export default {
props:['falge','titles'],
setup(props,emits){
    let bgcolor=ref({})
    
    const handleThemeChange = () => {
        bgcolor.value = JSON.parse(sessionStorage.getItem('themecolor'))
        
        // 动态设置CSS变量
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--delog-color', bgcolor.value.delogcolor);
        document.documentElement.style.setProperty('--hover-color', bgcolor.value.hovercor);
        document.documentElement.style.setProperty('--font-color', bgcolor.value.font);
    }
    
    window.addEventListener('setthcolor', handleThemeChange)
    
    const handleClose=()=>{
        emits.emit('colses',false)
    }
    
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        handleThemeChange()
    })
    
    onUnmounted(() => {
        window.removeEventListener('setthcolor', handleThemeChange)
    })
    
    return{
        bgcolor,
        handleClose
    }
}
}
</script>
<style lang="scss" scoped>
:root {
    --title-color: #fff;
    --delog-color: 0, 48, 70;
    --hover-color: #0346b0;
    --font-color: #fff;
}

.themed-border {
    border: 1px solid var(--title-color) !important;
}

.themed-bg {
    background: rgba(var(--delog-color), 0.35);
}

.themed-close {
    background: radial-gradient(50% 50% at 50% 50%, rgba(3, 251, 255, 0.17) 0%, var(--hover-color) 100%);
    left: 99%;
}

.headertop{
    height: 30px;
    position: relative;
    p{
        color: #fff;
        text-align: center;
        line-height: 30px;
        font-weight: bold;
    }
    .heaereq{
    position: absolute;
    top: 0!important;
    left: 0!important;
}
}
.closedelog{
    position: absolute;
    top: 0;
}
</style>