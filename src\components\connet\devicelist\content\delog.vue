<template>
  <el-dialog  v-model="dialogTableVisible"  destroy-on-close
    class="delogss" width="70%" :title="titles" append-to-body :before-close="closes" 
    >
    <selection ref="selection" @colses="closes" :titles="titles" ></selection>
    <div v-if="falge=='0'" class="bodybottom" :style="`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`">
        <div  v-for="(item,index) in serchs" :key="index" class="serchs bules">
          <span>{{item.name}}:</span>
          <el-date-picker v-if="item.type==3" class="bules" size="small" v-model="getform[item.value]" type="date" placeholder="选择日期"
           format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 200px;" />
          <el-select v-else-if="item.type==2" clearable popper-class="bules" 
          :popper-append-to-body="true" style="width: 200px;" size="small" v-model="getform[item.value]"
           class="m-2"  placeholder="" >
            <el-option v-for="(ite,index) in item.list" :key="index" :label="ite.name" :value="ite.value"  />
          </el-select>
          <el-input v-if="item.type==1" v-model="getform[item.value]" style="width: 200px"  placeholder="请输入关键字" size="small" clearable></el-input>
        </div>
        <el-button type="primary" size="small" @click="search">搜索</el-button>

        <el-table  :data="tableData" border class="bs-table cursor" max-height="500px" :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
        empty-text="暂无数据" :loading="loading" @row-click="rows"  :row-class-name="tableRowClassName"
        :style="[`width: 100%;color:${bgcolor.font}; --el-table-border-color:${bgcolor.titlecolor}`]">
            <template #empty>
                  <el-empty  v-loading="loading"></el-empty>
            </template>
          <el-table-column v-for="(item,index) in lables" :key="index" 
              :label="item.name" align="center" :prop="item.value" :width="item.widths">
              <template #default="scope">
                <el-button type="primary" size="small" v-if="item.name=='维保内容'" @click="showdelog(scope.row,'设备维保详情')">查看</el-button>
                <span v-else>{{ scope.row[item.value] }}</span>
              </template>
          </el-table-column>
        </el-table>
        <el-pagination v-model:current-page="getform.page" v-model:page-size="getform.count" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          :total="Number(Totles) " @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
    <div v-else class="datedelog bodybottom bules lable" :style="getstyle">
        <div v-for="(item,index) in labeform" class="text" :key="index" :style="getcloume(item)">
            <span v-if="item.name">{{item.name}}：</span>
            <span v-if="!item.img&&item.name">{{addform[item.value]}}</span>
            <el-button type="primary" link v-if="item.name=='位置定位'" @click="showmap(addform,addform[item.value])">查看</el-button>
            <img v-if="item.value1" :src="addform[item.value1]" class="cursor"
             @click="pic(addform[item.value1])" alt="" style="width: 150px;height: 100px;">
      </div>
    </div>
    <picimg ref="picimg"></picimg>
    <maploaction ref="maploactions"></maploaction>
  </el-dialog>
</template>

<script>
import { onMounted, ref,getCurrentInstance, nextTick } from 'vue'
import selection from "@/components/connet/Common/selection.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";
import { labelist } from "@/components/connet/devicelist/content/lables.js";
import maploaction from "@/components/connet/Common/maploaction.vue";
export default {
components:{
  selection,
  picimg,
  maploaction
  // cadperon
  },
setup(){
  const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist

    let dialogTableVisible=ref(false)
    let bgcolor=ref({})
    let titles=ref('')
    let loading=ref(false)
    let tableData=ref([])
    let lables=ref([])
    let getform=ref({
      ProjectCode:'',
      page:1,
      count:10,
      InUserName:"",
      CreateTime:'',
      OperateDate:'',
      EquipCode:'',
      MaintainType:'',
      EquipType:'',
      type:''
    })
    let Totles=ref(0)
    let url=ref('')
    let picimg=ref(null)
    let geturl=ref('')
    let serchs=ref([])
    let labeform=ref([])
    let falge=ref(0)
    let onelist=['位置定位','维保结果','备注']
    let twolist=[]
    let addform=ref({})
    let maploactions=ref(null)
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    })
    const showdelog=(val,name,CorpName)=>{
      // console.log('显示',val,name);
      falge.value=0
      for (const key in getform.value) {
        getform.value[key]=''
      }
      getform.value.ProjectCode=store.getters.code
      getform.value.InUserName=store.getters.username
      titles.value=name
      lables.value=[]
      lables.value=[...$labelist(name)]
      serchs.value=[...$labelist('更多维保搜索')]
      getform.value.page=1
      getform.value.count=10
      // modal.value=false
      url.value=''
      switch (name) {
        case '设备维保记录':
          gettawer()
          url.value='GetMaintenTable'
          break;
        case '环境告警':
          url.value='GetNewYCWarningTable'
          
          break;
        case '告警类型统计分析':
        case '设备告警统计分析':
          lables.value=[...labelist('告警记录列表')]
          serchs.value=[...labelist('告警记录列表查询')]
          // url.value='GetEquipHisWarningAnalysis'
          url.value='GetEquipWarnRecordTable'
          
          break;
        case '设备维保详情':
          falge.value=1
          getform.value.GUID=val.GUID
          labeform.value=[...labelist('设备维保详情')]
          geturl.value='GetMaintainDetail'
          getdetilform()
          break;
      }
      gettabledata()
      dialogTableVisible.value=true
    }
    // 获取设备名称下拉框
    const geteqname=async()=>{
        const {data:res}=await gettable('GetEquipMonitorAddr',getform.value)
        getlist(res,'设备名称','EquipName','EquipCode')

        // console.log('获取',getlist(res,'设备名称','EquipName','EquipCode'));
    }
    const showmap=(val,addres)=>{
        // console.log('查看地图',val);
        maploactions.value.showdelog(val,addres)
    }
    const getstyle=()=>{
    return `border:2px solid ${bgcolor.value.titlecolor};
    background:rgba(${bgcolor.value.delogcolor},0.35)`
 }
    const gettawer=async()=>{
        const {data:res}=await gettable('DropDeviceType',getform.value)
        getlist(res,'设备类型','TypeName','DicType')
        // console.log('获取',getlist(res,'设备类型','TypeName','DicType'));
    }
    const options=(val,value)=>{
      
      if (val?.name=='设备类型') {
        getform.value.type=value.value
        geteqname()
      }
      gettabledata()
      
    }
    const getlist=(res,val,name,value)=>{
      if (res.code=="1000") {
        serchs.value.forEach((item,i)=>{
        if (item.name==val) {
          item.list=res.data.map((item,index)=>{
          return{
              name:item[name],
              value:item[value],
          }
          })
        }
        })
      }else{
        serchs.value.forEach((item,i)=>{
            if (item.name==val) {
                item.list=[]
            }
        })
      }
      return serchs.value
    }
    const preview=(val)=>{
        let imgtype=['jpg','png','Jpeg']
      if (val) {
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1)
          if (imgtype.includes(file)) {
              picimg.value.piclist(val)
          }else{
          window.open('https://f.zqface.com/?fileurl='+val,'_slef')
          }
            }
	  }
    const search=()=>{
        // console.log('搜索',getform.value);
        gettabledata()
    }
    const closes=()=>{

      dialogTableVisible.value=false
    }
    // 获取详情
    const getdetilform=async()=>{
        const {data:res}=await gettable(geturl.value,getform.value)
          if (res.code=="1000") {
            // console.log('是否是组数',res.data);
            addform.value=Array.isArray(res.data)?res.data[0]:res.data
          }
    }
    // 获取列表
    const gettabledata=async()=>{
        tableData.value=[]
        loading.value=true
        const {data:res}=await gettable(url.value,getform.value)
        loading.value=false
        // if (titles.value=='告警类型统计分析'||titles.value=='设备告警统计分析') {
        //   tableData.value=res.data.WarnTable
        //   Totles.value=res.data.Total

        // }else{
          tableData.value=res.data
          Totles.value=res.Total

        // }
        // Totles.value=res.Total
        
    }
    const getstylable=(val)=>{

        let styles=widths.includes(val)?'grid-column: 1/span 2':''
        return styles
    }
    const getcloume=(val)=>{
        // 如果 val.name 在 onelist 数组中，合并三列
        if (onelist.includes(val.name)) {
            return 'grid-column: 1 /span 3';
        }
        // 如果 val.name 在 twolist 数组中，合并两列
        else if (twolist.includes(val.name)) {
            return 'grid-column: 1 /span 2';
        }
        // 如果都不在，则不合并列
        return '';
    }
    const rows=(val)=>{

    }

    const tableRowClassName=({row,rowIndex,})=>{

      if (rowIndex%2 != 0) {
          return 'warning-row'
      }
        return ''
    }
    const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        gettabledata()
        }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        getform.value.page=val
        gettabledata()
        }
    
    return{
        dialogTableVisible,
        picimg,
        titles,
        bgcolor,
        loading,
        tableData,
        lables,
        getform,
        Totles,
        serchs,
        url,
        falge,
        onelist,
        twolist,
        labeform,
        addform,
        maploactions,

        closes,
        showdelog,
        tableRowClassName,
        rows,
        gettabledata,
        preview,
        handleSizeChange,
        handleCurrentChange,
        getstylable,
        getdetilform,
        geteqname,
        gettawer,
        getlist,
        options,
        search,
        getstyle,
        getcloume,
        showmap
        

    }
}
}
</script>
<style lang="scss">
.delogss{
    .el-table .warning-rows {
        .cell{
        color: red!important;

        }
    }
    .el-table .uline{
        .cell{
            color:#E88B0D!important;
        }
    }
}
</style>
<style lang="scss">
// .bs-table{
//   margin-bottom: 20px;
// }
.counts{
  color: red;
  margin-bottom: 10px;
}
.serchs{
  display: inline-block;
  margin: 10px;
  color: #fff;
}
.zscounts{
  color: #000;
  display: grid;
  grid-template-columns: repeat(2,50%);
  div{
    margin: 10px;
  }
}
.text{
  margin: 10px;
}
.lable{
  display: grid;
  grid-template-columns: repeat(3,33.3%);
  color: #fff;
}
.flex-img{
    color: #fff;
    display: grid;
    grid-template-columns: repeat(5,20%);
    
}
.flex-time{
    margin: 10px;
}
.upload{
    display: grid;
    grid-template-columns: 10% 10%;
    .el-icon{
      grid-column: 1;
      grid-row: 1/span 2 ;
      font-size: 50px!important;
    }
}
#maps{
  width: 100%;
  height: 60vh;
}
</style>