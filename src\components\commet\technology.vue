<template>
<!-- 技术生产 -->
  <div class="model" >
    <div class="leftmodel left wid" >
        <Projectschedule  class="Homebgco" 
        :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" ></Projectschedule>
        <organization  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :teamtype="materialtype"></organization>
        <organization  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :teamtype="teamtype"></organization>
    </div>
    <div class="homecontent"  >
      <home></home>
    </div>
    <div class="rightmodel right wid" >
      <Largevolume class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        :bigcew="bigcew"
        ></Largevolume>
      <Largevolume class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        :bigcew="bigcew1"
        ></Largevolume>

      <ConstructionLog class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        ></ConstructionLog>
    </div>

  </div>
</template>

<script>

import Projectschedule from "@/components/connet/technology/Projectschedule.vue";
import organization from "@/components/connet/technology/organization.vue";
import Largevolume from "@/components/connet/technology/Largevolume.vue";
import ConstructionLog from "@/components/connet/technology/ConstructionLog.vue";

import home from "@/components/connet/technology/content/home.vue";

import { onMounted, ref ,computed} from 'vue';

export default {
components:{

        Projectschedule,
        Largevolume,
        organization,
        ConstructionLog,
        home,

    },
setup(){
    let themelist=ref([])

    let bgcolor=ref({})
    let amplify=ref(0)
    let materialtype=ref({
        src:require('@/assets/img/technology/organization.png'),
        titles:'施工方案',
        type:'organization'
    })
    let teamtype=ref({
        src:require('@/assets/img/technology/disclosure.png'),
        titles:'技术交底',
        type:'disclosure'
    })

    let bigcew=ref({
        src:require('@/assets/img/technology/Largevolume.png'),
        titles:'大体积混泥土测温',
        type:'disclosure'
    })
    let bigcew1=ref({
        src:require('@/assets/img/technology/MeetingMinutes.png'),
        titles:'会议纪要',
        type:'MeetingMinutes'
    })

    
    // let Typeworlist=ref({
    //     src:require('@/assets/img/material/Materialtype.png'),
    //     titles:'材料种类验收分析',
    //     type:'物料'
    // })
    window.addEventListener('setItem', ()=> {
      // console.log('项目引导');
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
   })
   window.addEventListener('setthcolor', ()=> {
      // console.log('主题切换');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(()=>{
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    

    })
    const getamplify2=(val)=>{
      // console.log('放大',val);
      
      amplify.value=val
    }
    return{
        materialtype,
        bgcolor,
        themelist,
        teamtype,
        amplify,
        bigcew,
        bigcew1,
        // Typeworlist,
        getamplify2
    }
}
}
</script>
<style lang="scss" scoped>
@mixin homeflex($deg) {
  height: 31.3%;
  width: 95%;
  opacity: 1;
  margin:7px 10px;
  background: linear-gradient($deg, #0096C7 7%, rgba(0,52,75,0.00) 97%);
}
@mixin leftdjx($left,$right,$top,$bottom,) {
  position: absolute;
  content: '';
  display: block;
  left: $left;
  top: $top;
  right: $right;
  bottom: $bottom;
  width: 10.06px;
  height: 10.84px;
  opacity: 1;
}

.Homebgco{
  position: relative;
  @include homeflex(90deg)
  }
.Homeright{
  position: relative;
  @include homeflex(-90deg);

}
</style>
<style lang="scss" scoped>
.model{
  width: 100%;
  height: 100%;
  display: flex;
  .wid{
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .homecontent{
    width: 60%;
    height: 100%;
    // position: relative;
  }
}
</style>