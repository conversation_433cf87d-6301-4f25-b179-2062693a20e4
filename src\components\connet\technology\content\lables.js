export function labelist(params) {
    // 表单
    let lablist=[]
    let formcout=[
        {
         name:'施工方案列表',
         value:[{
            name:'序号',
            value:'rowNum',
            widths:'60',
            },{
            name:'报审日期',
            value:'CreateTime',
            widths:'',
            },{
            name:'方案名称',
            value:'ProgrammeName',
            widths:'',
            },{
            name:'方案类型',
            value:'ProgrammeType',
            widths:'',
            },{
            name:'编制人',
            value:'Editor',
            widths:'',
            },{
            name:'方案状态',
            value:'ProgrammeState',
            widths:'',
            },{
            name:'二维码',
            value:'QRCode',
            widths:'',
            img:'true'
            }
         ]
        },{
        name:'施工方案列表查询',
        value:[{
              name:'日期',
              value:'CreateTime',
              type:3,
              widths:'150',
              },{
             name:'关键字搜索',
             value:'ProgrammeName',
             type:1,
             widths:'150',
             }
        ]},{
        name:'施工方案详情',
        value:[{
            name:'基本信息',
            value:'',
            type:'0',
            widths:'',
            },{
            name:'方案状态',
            value:'ProgrammeState',
            type:'0',
            widths:'',
            },{
            name:'创建人',
            value:'Creator',
            type:'0',
            widths:'',
            },{
            name:'创建时间',
            value:'CreateTime',
            type:'0',
            widths:'',
            },{
            name:'方案类型',
            value:'ProgrammeType',
            type:'0',
            widths:'',
            },{
            name:'方案名称',
            value:'ProgrammeName',
            type:'0',
            widths:'',
            },{
            name:'编制人',
            value:'Editor',
            type:'0',
            widths:'',
            },{
            name:'编制完成时间',
            value:'EditorTime',
            type:'0',
            widths:'',
            },{
            name:'报审时间',
            value:'SubmissionTime',
            type:'0',
            widths:'',
            },{
            name:'备注',
            value:'Remake',
            type:'0',
            widths:'',
            },{
            name:'方案附件列表',
            value:'ProgrammeFile',
            type:'1',
            widths:'',
            },{
            name:'审批信息',
            value:'',
            type:'0',
            widths:'',
            },{
            name:'施工单位审批',
            value:'ConstructionUnit',
            type:'0',
            widths:'',
            },{
            name:'审批时间',
            value:'ConstructionTime1',
            type:'0',
            widths:'',
            },{
            name:'审批人',
            value:'ConstructionPerson1',
            type:'0',
            widths:'',
            },{
            name:'',
            value:'',
            type:'0',
            widths:'',
            },{
            name:'审批时间',
            value:'ConstructionTime2',
            type:'0',
            widths:'',
            },{
            name:'审批人',
            value:'ConstructionPerson2',
            type:'0',
            widths:'',
            },{
            name:'监理单位审批',
            value:'ControlUnit',
            type:'0',
            widths:'',
            },{
            name:'审批时间',
            value:'ControlTime1',
            type:'0',
            widths:'',
            },{
            name:'审批人',
            value:'ControlPerson1',
            type:'0',
            widths:'',
            },{
            name:'',
            value:'',
            type:'0',
            widths:'',
            },{
            name:'审批时间',
            value:'ControlTime2',
            type:'0',
            widths:'',
            },{
            name:'审批人',
            value:'ControlPerson2',
            type:'0',
            widths:'',
            },{
            name:'交底附件列表',
            value:'DisclosureFile',
            type:'1',
            widths:'',
            },
        ]},{
        name:'技术交底列表',
        value:[{
           name:'序号',
           value:'rowNum',
           widths:'60',
           },{
           name:'交底日期',
           value:'CreateTime',
           widths:'',
           },{
           name:'交底名称',
           value:'DisclosureName',
           widths:'',
           },{
           name:'交底类型',
           value:'DisclosureType',
           widths:'',
           },{
           name:'交底人',
           value:'DisclosurePerson',
           widths:'',
           },{
           name:'交底状态',
           value:'DisclosureState',
           widths:'',
           },{
           name:'关联实施方案',
           value:'ProgrammeName',
           value1:'FileUrl',
           widths:'',
           }
        ]},{
        name:'技术交底详情',
        value:[{
                name:'基本信息',
                value:'',
                type:'0',
                widths:'',
                },{
                name:'方案状态',
                value:'ProgrammeState',
                type:'0',
                widths:'',
                },{
                name:'创建人',
                value:'DisclosurePerson',
                type:'0',
                widths:'',
                },{
                name:'创建时间',
                value:'CreateTime',
                type:'0',
                widths:'',
                },{
                name:'交底类型',
                value:'DisclosureType',
                type:'0',
                widths:'',
                },{
                name:'交底部位',
                value:'DisclosurePosition',
                type:'0',
                widths:'',
                },{
                name:'关联施工方案',
                value:'DisclosureType',
                type:'0',
                widths:'',
                },{
                name:'交底人',
                value:'DisclosurePerson',
                type:'0',
                widths:'',
                },{
                name:'交底时间',
                value:'DisclosureTime',
                type:'0',
                widths:'',
                },{
                name:'备注',
                value:'Remake',
                type:'0',
                widths:'',
                },{
                name:'附件列表',
                value:'FileList',
                type:'1',
                widths:'',
                }
        ]},{
        name:'施工日志列表',
        value:[{
           name:'序号',
           value:'rowNum',
           widths:'60',
           },{
           name:'日期',
           value:'ConstructionDate',
           widths:'120',
           },{
           name:'记录人',
           value:'Recorder',
           widths:'80',
           },{
           name:'记录内容',
           value:'RecordContent',
           widths:'',
           },{
           name:'尚未解决问题',
           value:'Problems',
           widths:'110',
           },{
           name:'是否闭环',
           value:'ClosedLoop',
           widths:'100',
           }
        ]},{
        name:'会议纪要列表',
        value:[{
           name:'序号',
           value:'rowNum',
           widths:'60',
           },{
           name:'日期',
           value:'MeetingTime',
           widths:'120',
           },{
           name:'会议主题',
           value:'MeetingName',
           widths:'80',
           },{
           name:'会议地点',
           value:'MeetingAddr',
           widths:'',
           },{
           name:'与会人员',
           value:'Participants',
           widths:'110',
           },{
           name:'文件',
           value:'MeetingFileName',
           value1:'MeetingFile',
           widths:'100',
           }
        ]},{
        name:'施工日志详情',
        value:[{
            name:'日志日期',
            value:'ConstructionDate',
            type:'0',
        },{
            name:'星期',
            value:'Week',
            type:'0',
        },{
            name:'气象',
            value:'Climate',
            type:'0',
        },{
            name:'风力风向',
            value:'WindDirectionPower',
            type:'0',
        },{
            name:'气温',
            value:'AirTemperature',
            type:'0',
        },{
            name:'记录内容',
            value:'RecordContent',
            type:'0',
        },{
            name:'照片',
            value:'ConstructionPhoto',
            type:'0',
            img:true
        },{
            name:'尚待解决问题',
            value:'Problems',
            type:'0',
        },{
            name:'是否闭环',
            value:'ClosedLoop',
            type:'0',
        },{
            name:'记录人',
            value:'Recorder',
            type:'0',
        }
        ]}
        
    ]


    formcout.forEach((item,index)=>{
        if (item.name==params) {
            lablist=item.value
            
        }
    })
    
    return lablist
}