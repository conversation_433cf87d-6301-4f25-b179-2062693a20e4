<template>
  <el-dialog  v-model="dialogTableVisible"  destroy-on-close
    class="delogss" width="70%" :title="titles" append-to-body :before-close="closes" 
    >
    <selection ref="selection" @colses="closes" :titles="titles" ></selection>
    <div class="bodybottom">
      <foundations v-if="titles=='深基坑监测'" ref="foundations"></foundations>
      <Highsupportmold v-else-if="titles=='高支模监测'" ref="Highsupportmold"></Highsupportmold>
      <Standard v-else-if="titles=='标养室监测'" ref="Standard"></Standard>
      <Greenconstruction v-else-if="titles=='绿色工地'" ref="Greenconstruction"></Greenconstruction>
    </div>
    <picimg ref="picimg"></picimg>
  </el-dialog>
</template> 

<script>
import { onMounted, ref,getCurrentInstance, nextTick } from 'vue'
import selection from "@/components/connet/Common/selection.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";
import foundations from "./foundations.vue";
import Highsupportmold from "./Highsupportmold.vue";
import Standard from "./Standard.vue";
import Greenconstruction from "./Greenconstruction.vue";
export default {
components:{
  selection,
  picimg,
  foundations,
  Highsupportmold,
  Standard,
  Greenconstruction
  // cadperon
  },
setup(){
//   const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist

    let dialogTableVisible=ref(false)
    let bgcolor=ref({})
    let titles=ref('')
    let loading=ref(false)
    let tableData=ref([])
    let lables=ref([])
    let getform=ref({
      ProjectCode:'',
      page:1,
      count:10,
      InUserName:"",
    })
    // let delogs=ref(null)
    let picimg=ref(null)
    let Foundation=ref(null)
    let Highsupportmold=ref(null)
    let Standard=ref(null)
    // let geturl=''
    // let serchs=ref([])
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--dialog-bg-color', bgcolor.value.delogcolor);
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--dialog-bg-color', bgcolor.value.delogcolor);
    })
    const showdelog=(val,name,CorpName)=>{
      // console.log('获取',val,name,CorpName);
      titles.value=val.name
      dialogTableVisible.value=true
    }

    const preview=(val)=>{
        let imgtype=['jpg','png','Jpeg']
      if (val) {
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1)
          if (imgtype.includes(file)) {
              picimg.value.piclist(val)
          }else{
          window.open('https://f.zqface.com/?fileurl='+val,'_slef')
          }
            }
	  }

    const closes=()=>{

        dialogTableVisible.value=false
    }


    const tableRowClassName=({row,rowIndex,})=>{

      if (rowIndex%2 != 0) {
          return 'warning-row'
      }
        return ''
    }

    return{
        dialogTableVisible,
        picimg,
        titles,
        bgcolor,
        loading,
        tableData,
        lables,
        getform,
        Highsupportmold,
        Standard,
        Foundation,

        closes,
        showdelog,
        tableRowClassName,
        // rows,
        // gettabledata,
        preview,
        // handleSizeChange,
        // handleCurrentChange,
        // getstylable,
        // getdetilform,
        // geteqname,
        // gettawer,
        // getlist,
        // options,
        

    }
}
}
</script>
<style lang="scss">
.delogss{
    // .el-table .warning-rows {
    //     .cell{
    //     color: red!important;

    //     }
    // }
    // .el-table .uline{
    //     .cell{
    //         color:#E88B0D!important;
    //     }
    // }
}
</style>
<style lang="scss">
// .bs-table{
//   margin-bottom: 20px;
// }
// .counts{
//   color: red;
//   margin-bottom: 10px;
// }
// // .serchs{
// //   display: inline-block;
// //   margin: 10px;
// //   color: #fff;
// // }
// .zscounts{
//   color: #000;
//   display: grid;
//   grid-template-columns: repeat(2,50%);
//   div{
//     margin: 10px;
//   }
// }
// .flex-img{
//     color: #fff;
//     display: grid;
//     grid-template-columns: repeat(5,20%);
    
// }
// .flex-time{
//     margin: 10px;
// }
// .upload{
//     display: grid;
//     grid-template-columns: 10% 10%;
//     .el-icon{
//       grid-column: 1;
//       grid-row: 1/span 2 ;
//       font-size: 50px!important;
//     }
// }
// #maps{
//   width: 100%;
//   height: 60vh;
// }
</style>