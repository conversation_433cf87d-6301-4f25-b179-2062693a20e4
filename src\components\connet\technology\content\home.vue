<template>
  <div class="technology-home">
    <div class="technology-home-btn">
        <div v-for="(item,index) in btnlist" :key="index" :class="['technology-btn cursor',{'changindex':falge==index}]" 
        :style="[falge==index?`background:${bgcolor.changcolor};color:#FFF`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
         rgba(7, 93, 184, 0.6) 100%);color:#FFF`]" @click="change(item,index)"
        >{{item}}</div>
    </div>
    <div v-if="falge==0">
        <div class='model' id="domId"></div>
    </div>
    <Production v-else-if="falge==1"></Production>
    <Projectprogress v-else-if="falge==2"></Projectprogress>
  </div>
</template>

<script>
import { ref ,onMounted} from 'vue'
import Production from "@/components/connet/technology/content/Production.vue";
import Projectprogress from "@/components/connet/technology/content/Projectprogress.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
export default {
    components:{
        Production,
        Projectprogress
    },
    setup(){
        let btnlist=['BIM模型','生产进度','工程进展']
        let falge=ref(0)
        let bgcolor=ref({})
        let getform=ref({
            ProjectCode:store.getters.code,
            })
        window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        })
        onMounted(()=>{
            bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        })
        const change=(val,index)=>{
            falge.value=index
            if (index==0) {
                showBIM('10000758992762')
            }
        }
        const showBIM=async(val)=>{

			let takoen = {}
			takoen.fileid = val
			let viewToken = ""
		//  const  {data:res }= await this.$http.post('/aiot/Api.ashx?PostType=get&Type=GetTokenInfo', takoen)
		//  console.log('返回',res);10000729796458
            const {data:res}=await gettable('GetTokenInfo',takoen)
            // console.log('获取token',res);
            
			if (res.code) {
				viewToken = res.token
			}
		// var viewToken = 'bc844e4a43114593999989d2e4b70792';
        // 声明Viewer及App
        var viewer3D;
        var app;
        var viewAdded = false;
        var fileid = '10000729796458';
        // 初始化显示组件
        var options = new BimfaceSDKLoaderConfig();
        options.viewToken = viewToken;
        BimfaceSDKLoader.load(options, successCallback, failureCallback);
        function successCallback(viewMetaData) {
            if (viewMetaData.viewType == "3DView") {
                // ======== 判断是否为3D模型 ========
                // 获取DOM元素
                var dom4Show = document.getElementById('domId');
                var webAppConfig = new Glodon.Bimface.Application.WebApplication3DConfig();
                webAppConfig.domElement = dom4Show;
                // 创建WebApplication
                app = new Glodon.Bimface.Application.WebApplication3D(webAppConfig);
                // 添加待显示的模型
                app.addView(viewToken);
                // 从WebApplication获取viewer3D对象
                viewer3D = app.getViewer();
                // 监听添加view完成的事件
                viewer3D.addEventListener(Glodon.Bimface.Viewer.Viewer3DEvent.ViewAdded, function () {
                    viewAdded = true;
                    //自适应屏幕大小
                    window.onresize = function() {
                        viewer3D.resize(document.documentElement.clientWidth, document.documentElement.clientHeight - 40);
                    }
                    // 渲染3D模型
                    viewer3D.render();
                });
            }
        }
        function failureCallback(error) {
            console.log(error);
        } 
        // 打开邵逸夫医院
        function Stairs(id) {
            window.location.href = "BIMPage.aspx?fileid="+id;
        } 
        // 基于世界坐标中的某个点，将模型进行整体缩放
        function scaleModel() {
            if (!viewAdded || !isModelAdded) {
                return;
            }
            viewer3D.setModelScale(modelId, { x: 0, y: 0, z: 0 }, 1.2);
            viewer3D.render();
        } 
        // 按钮文字
        function setButtonText(btnId, text) {
            var dom = document.getElementById(btnId);
            if (dom != null && dom.nodeName == "BUTTON") {
                dom.innerText = text;
            }
        }
			}
        return{
            bgcolor,
            btnlist,
            falge,
            getform,
            change,
            showBIM
        }
    }
}
</script>
<style lang="scss" scoped>
.technology{
    &-home{
        height: 100%;
        // display: grid;
        // grid-template-rows: 5% 95%;
    &-btn{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    }

    &-btn{
        position: relative;
        font-weight: bold;
        margin: 8px;
        padding: 10px;
    }
}
.changindex::before{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        left: -2px;
        top: -2px; 
        opacity: 1;
        border-top: 2px solid #E0A538;
        border-left: 2px solid #E0A538;
    }
.changindex::after{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        right: -2px;
        bottom: -2px; 
        opacity: 1;
        border-bottom: 2px solid #E0A538;
        border-right: 2px solid #E0A538;
    }
</style>