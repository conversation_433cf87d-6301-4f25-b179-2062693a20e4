<template>
  <div class="map">
    <div class="back-button" @click="goBack">
      ← 返回
    </div>
    <div id="baiduMap" class="map-container"></div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'
import ycIcon from '@/assets/img/all/yc.png'
import ycIcon1 from '@/assets/img/all/yc1.png'


// 定义可以向父组件发送的事件
const emit = defineEmits([
    'back',           // 返回事件
    'close',          // 关闭事件
    'locationChange', // 位置改变事件
    'markerClick',    // 标记点击事件
    'mapReady'        // 地图准备就绪事件
])

// cosnt map = ref(null)
let map = ref(null)
let form=ref({
    lng:'',
    lat:'',
})
let marker = ref(null)
let infoWindow = ref(null) // 地图信息窗口

// 供应商数据 - 示例数据，您可以根据实际需求修改
let supplierData = ref({})
const showdelog=(item,its)=>{
    // console.log('定位项目:', item, its);
    form.value.lng = item.longitude
    form.value.lat = item.latitude
    supplierData.value = item
    initMap()
    // maploactions.value.showdelog(item,its)
}
const initMap=()=> {
    // 确保地图容器已经渲染
    nextTick(() => {
        // 创建地图实例
        map.value = new BMapGL.Map("baiduMap");
        // 创建点坐标
        const point = new BMapGL.Point(form.value.lng, form.value.lat);
        // 初始化地图，设置中心点坐标和地图级别
        map.value.centerAndZoom(point, 17);
        // 开启鼠标滚轮缩放
        map.value.enableScrollWheelZoom(true);

        // 添加地图点击事件监听器
        map.value.addEventListener('click', () => {
            // 点击地图空白处时关闭信息窗口
            if (infoWindow.value) {
                infoWindow.value.close();
            }
        });

        addMarker(point);

    });
}

// 获取自定义图标URL
const getCustomIconUrl = () => {
    // console.log('使用图标:', form.value);
    if (supplierData.value.status == '1') {
        // 如果有特定条件，可以返回不同的图标URL
        return ycIcon; // 替换为其他图标URL
        
    }else{
        return ycIcon1
    }
    return 
}

// 获取悬停时的图标URL
const getHoverIconUrl = () => {
    // 悬停时使用相同图标，可以后续替换为不同的悬停图标
    if (supplierData.value.status == '1') {
        // 如果有特定条件，可以返回不同的图标URL
        return ycIcon; // 替换为其他图标URL
        
    }else{
        return ycIcon1
    }
    return
}

// 添加标记和信息窗口
const addMarker=(point)=> {
    if (marker.value) {
        map.value.removeOverlay(marker.value);
        marker.value = null;
    }

    // 关闭之前的信息窗口
    if (infoWindow.value) {
        infoWindow.value.close();
        infoWindow.value = null;
    }

    // 创建自定义图标
    const customIcon = new BMapGL.Icon(
        getCustomIconUrl(), // 图标URL
        new BMapGL.Size(20, 35), // 图标尺寸
        {
            anchor: new BMapGL.Size(11, 32), // 图标的定位锚点
            imageOffset: new BMapGL.Size(0, 0) // 图像偏移量
        }
    );

    // 创建带自定义图标的标记
    marker.value = new BMapGL.Marker(point, { icon: customIcon });
    map.value.addOverlay(marker.value);

    // 创建信息窗口内容
    const infoContent = createInfoWindowContent();

    // 创建信息窗口
    infoWindow.value = new BMapGL.InfoWindow(infoContent, {
        width: 500,
        height: 217,
        title: ""
    });

    // console.log('信息窗口已创建:', infoWindow.value);

    // 添加标记点击事件监听器
    marker.value.addEventListener('click', (e) => {
        // console.log('标记被点击，位置:', point, '供应商数据:', supplierData.value);

        // 阻止事件冒泡
        e.stopPropagation && e.stopPropagation();

        // 先关闭已存在的信息窗口
        if (infoWindow.value) {
            infoWindow.value.close();
        }

        // 延迟一下再打开，确保关闭操作完成
        setTimeout(() => {
            // 手动点击时打开信息窗口
            map.value.openInfoWindow(infoWindow.value, point);
        }, 100);

        // 向父组件传递标记点击事件
        emit('markerClick', {
            position: { lng: point.lng, lat: point.lat },
            supplierData: supplierData.value,
            timestamp: Date.now(),
            trigger: 'manual_click' // 标识为手动点击
        });
    });

    // 添加标记悬停效果
    marker.value.addEventListener('mouseover', () => {
        // 鼠标悬停时改变光标样式
        marker.value.getIcon().setImageUrl(getHoverIconUrl());
    });

    marker.value.addEventListener('mouseout', () => {
        // 鼠标离开时恢复原始图标
        marker.value.getIcon().setImageUrl(getCustomIconUrl());
    });

    // 自动打开信息窗口
    setTimeout(() => {
        map.value.openInfoWindow(infoWindow.value, point);

        // 向父组件传递标记点击事件
        emit('markerClick', {
            position: { lng: point.lng, lat: point.lat },
            supplierData: supplierData.value,
            timestamp: Date.now(),
            trigger: 'auto_open' // 标识为自动打开
        });
    }, 500); // 延迟0.5秒显示
}

// 创建信息窗口内容
const createInfoWindowContent = () => {
    // 确保数据存在，提供默认值
    const data = supplierData.value || {};
    const projectName = data.projectName || '未知项目';
    const status = data.status || '0';
    const operationCompanyName = data.operationCompanyName || '未知运维商';
    const devicePoleName = data.devicePoleName || '未知类型';
    const districtName = data.districtName || '未知区域';
    const mnCode = data.mnCode || '未知编码';

    // console.log('创建信息窗口内容，数据:', data);

    return `
        <div style="padding: 15px; font-family: Arial, sans-serif;">
            <div style="margin-bottom: 8px;">
                <strong style="color: rgba(0,0,0,.85); font-size: 16px;line-height:1.5;">${projectName}</strong>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 5px;">
                <span style="color: #ffffff; padding: 2px 8px;
                    background: ${status === '1' ? '#28A33E' : '#000'};
                    font-size: 12px; border-radius: 3px;">${status === '1' ? '在线' : '离线'}</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="color: rgba(0,0,0,.85);font-size: 14px;">运维商：${operationCompanyName}</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="color: rgba(0,0,0,.85);font-size: 14px;">工地类型：${devicePoleName}</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="color: rgba(0,0,0,.85);font-size: 14px;">所属区域：${districtName}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="color: rgba(0,0,0,.85);font-size: 14px;">MN编码：${mnCode}</span>
            </div>
        </div>
    `;
}

// 返回按钮点击事件
const goBack = () => {
    // 方式1: 不传参数，只触发事件
    emit('back');
    nextTick(()=>{
        map.value.clearMap() // 清除地图上的标记
    })
}

// 页面加载时初始化基础地图
onMounted(() => {
    // 初始化一个默认的地图视图
    nextTick(() => {
        if (!map.value) {
            map.value = new BMapGL.Map("baiduMap");
            // 设置默认中心点（杭州市中心）
            const defaultPoint = new BMapGL.Point(120.153576, 30.287459);
            map.value.centerAndZoom(defaultPoint, 11);
            map.value.enableScrollWheelZoom(true);

            // 向父组件传递地图准备就绪事件
            emit('mapReady', {
                mapInstance: map.value,
                defaultLocation: { lng: 120.153576, lat: 30.287459 },
                message: '地图初始化完成'
            });
        }
    });
});

defineExpose({
    showdelog
})
</script>
<style scoped>
.map{
    width: 100%;
    height: 100%;
    position: relative;
    background: #f0f0f0; /* 添加背景色，便于调试 */
}

.map-container {
    width: 100%;
    height: 100%;
}

#baiduMap {
    width: 100%;
    height: 100%;
}

/* 返回按钮样式 */
.back-button {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 99999 !important; /* 提高层级，使用!important确保优先级 */
    background: #ffffff !important;
    border: 2px solid #409eff !important; /* 加粗边框便于调试 */
    border-radius: 8px;
    padding: 12px 18px; /* 增大内边距 */
    display: flex !important;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important; /* 加深阴影 */
    transition: all 0.3s ease;
    font-size: 16px; /* 增大字体 */
    font-weight: bold; /* 加粗字体 */
    color: #409eff !important;
    min-width: 80px; /* 设置最小宽度 */
    min-height: 40px; /* 设置最小高度 */
}

.back-button:hover {
    background: #f5f7fa;
    border-color: #409eff;
    color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.back-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.back-button svg {
    transition: transform 0.3s ease;
}

.back-button:hover svg {
    transform: translateX(-2px);
}

.back-button span {
    user-select: none;
}
</style>