<template>
  <!-- 磅房信息 -->
  <div class="ConstructionLog padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms" @opens="opentable()"></Chamfering>

    <div class="ConstructionLog-two">
      <div  class="ConstructionLog-two-one">
        <p v-for="(item,index) in ConstructionLoglable" :key="index">{{item.name}}:{{item.value}}</p>
      </div>
      <div class="pickers">
        <el-date-picker
        v-model="getform.Date"
        type="date"
        ref="daterange"
        @panel-change="panelchange"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        popper-class="dateranges bules"
        @blur="chooseDate"
            >
            <template #default="cell">
                <div class="cell" :class="{ current: cell.isCurrent }" @click="handleChange(cell)">
                  <span class="text" :style="`${iscolor(cell)}`">{{ gettext(cell) }}</span>
                  <span v-if="isHoliday(cell)" class="holiday" />
                </div>
            </template>
        </el-date-picker>

      </div>
      <delogshow ref="delogshow"></delogshow>
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    <delog ref="delog"></delog>
  </div>
</template>

<script>
import { nextTick, onMounted, ref,getCurrentInstance } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import { formatDateTime } from "@/utils/date";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

import delogshow from "@/components/connet/technology/ecahrtline/delog.vue";
import delog from "@/components/connet/technology/content/delog.vue";
export default {
props:['bigcew', 'form'],
components:{
  delogshow,
  Chamfering,
  delog
},
setup(props){
// const $formatDateTime = getCurrentInstance().appContext.config.globalProperties.$formatDateTime

    // let falge=ref(false)
    let bgcolor=ref({})
    let value=ref('2023-11-29')
//   let countlist=ref([])
    let getform=ref({
      ProjectCode:store.getters.code,
      Date:'',
      ConstructionDate:''
    })
    let daterange=ref(null)
    let ConstructionLoglable=ref([
        {
            name:'日志总数',
            value:'Total'
        },{
            name:'本月日志',
            value:'MonthNum'
        }
    ])
    // const value = ref('2021-10-29')
    const holidays = ref([])
    let valuelsit=ref([])
    let windowHeight=ref(window.innerHeight)
    let delogshow=ref(null)
    let topforms=ref({
      url:require('@/assets/img/technology/ConstructionLog.png'),
      name:"施工日志",
      text:'更多记录',
      lefs:'lefs'
    })
    let delog=ref(null)
    let url=ref('')
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
   window.addEventListener('resize', function(event) {
    var windowHeights = window.innerHeight;
    // console.log('窗口高度为：' + windowHeights);
    let getpicker=document.querySelector('.dateranges')
    if (getpicker) {
    if (windowHeights>windowHeight.value) {
      // console.log('获取dom',getpicker);
      getpicker.classList.add("changeafter");

    }else{
      getpicker.classList.remove('changeafter');

    }
    }

  });
  onMounted(()=>{
    //  value.value=dayjs.format('YYYY-MM-DD')
    // getform.value.Date=''
    bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    // getform.value.Date=formatDateTime(new Date(),'yyyy-MM-dd')

    // 调试信息
    // console.log('ConstructionLog props:', props)
    // console.log('props.form:', props.form)

    // 安全检查 props.form 是否存在
    if (props.form && props.form.titles === '安全日志') {
      // console.log('设置为安全日志模式')
      topforms.value.name='安全日志'
      // url.value='GetSecurityLogTableInfo'
    }else{
    // getform.value.Date=formatDateTime(new Date(),'yyyy-MM-dd')

    }
    getpale()
    // console.log('窗口',windowHeight.value);
  })
  
  const iscolor= ({ dayjs }) => {
    // console.log('dayjs',dayjs);
    if (!dayjs) {
        // console.warn('handleChange: dayjs 对象为空');
        return;
      }
      let todat=dayjs.format('YYYY-MM-DD')
      let colors
      // console.log('获取',holidays.value);
      holidays.value?.map((its)=>{
          if (its.Date==todat) {
            // console.log('获取状态',its.status);
            switch (its.status) {
              case 2:
              colors= 'color:#0E9CFF'
                break;
              case 3:
              colors= 'color:red'
                break;
            }
          }
        })
        return colors
    // return holidays.includes(dayjs.format('YYYY-MM-DD'))
    }
  const isHoliday = ({ dayjs }) => {
    if (!dayjs) {
        // console.warn('handleChange: dayjs 对象为空');
        return;
      }
    let todat=dayjs.format('YYYY-MM-DD')
    let colorshow
      holidays.value?.map((its)=>{
          if (its.Date==todat) {
            switch (its.status) {
              case 2:
              // colors= 'color:#0E9CFF'
            colorshow= false
                break;
              case 3:
              // colors= 'color:red'
            colorshow= true

                break;
            }
          }
        })
        return colorshow
    }
  const gettext=(val)=>{
    // 判断是否有renderText字段，如果有则取这个值，没有取text值
    if (val && typeof val === 'object') {
      return val.renderText !== undefined ? val.renderText : val.text || '';
    }
    return '';
  }
  const opentable=()=>{
    // console.log('打开');
    delog.value.showdelog(null,'施工日志')
  }
    const panelchange=(date,val)=>{
      // console.log('选择',date,val);GetSecurityLogTableInfo
      getform.value.Date=formatDateTime(date,'yyyy-MM-dd')
      getpale()
    }
    const chooseDate=(e)=>{
    }
    const getpale=async()=>{
        const {data:res}=await gettable('GetConstructionLogByDate',getform.value)
          // console.log('获取',res);
          if (res.code=="1000") {
            holidays.value=res.data.MonthList
            ConstructionLoglable.value[0].value=res.data.Total
            ConstructionLoglable.value[1].value=res.data.MonthNum
          }
    }
    
    const handleChange =({dayjs})=>{
        // console.log('点击',dayjs.format('YYYY-MM-DD'))
        if (!dayjs) {
        return;
      }
      let todat=dayjs.format('YYYY-MM-DD')
      holidays.value?.forEach((item,index)=>{
        if (item.Date==todat) {
          switch (item.status) {
              case 2:
            // colorshow= false
            delogshow.value.showdelog(0,item)
                break;
              case 3:
                // console.log('点击',item);
            delogshow.value.showdelog(1,item)

                break;
            }
        }
      })
        // daterange.value.focus()
        // event.preventDefault()
    }

 

	return{
    delogshow,
    daterange,
    getform,
    bgcolor,
    value,
    ConstructionLoglable,
    holidays,
    valuelsit,
    windowHeight,
    topforms,
    delog,

    opentable,
    // falge,
    isHoliday,
    iscolor,
    chooseDate,
    handleChange,
    panelchange,
    getpale,
    gettext
    // getdetil
	}
}
}
</script>
<style lang="scss">
.dateranges{
    display: block!important;
    box-shadow: none!important;
    inset:auto auto 20px 1574px!important;
    background: rgba(15, 43, 63, 0.2)!important;

    .el-date-table td{
        height: 20px!important;
    }
    .el-picker-panel{
        line-height: 11px!important;
        background: rgba(15, 43, 63, 0.2)!important;
    }
    .el-picker-panel__body{
        background: transparent!important;
    }
    .el-picker-panel__content{
         margin: 5px 15px!important;
    }
    .el-date-picker__header{
        margin: 0px 12px!important;

    }
    .el-popper__arrow{
        display: none!important;
    }
}
.changeafter{
    inset:auto auto 35px 1574px!important;

  .el-picker-panel__content{
         margin: 15px!important;
    }
  .el-date-picker__header{
        margin:12px!important;

    }
  .el-date-table td{
        height: 23px!important;
    }
}
</style>
<style lang="scss" scoped>
.ConstructionLog{
&-two{
  height: 86%;
  padding: 10px;
//   display: grid;
  font-size: 14px;
//   grid-template-columns: 60% 40%;
//   align-items: center;
&-one{
  display: grid;
  grid-template-columns: 50% 50%;
  align-items: center;


}
// auto auto 230px 1569px
.pickers{
    .el-input{
    display: none!important;

    }
    display: none!important;
}
    p{
        text-align: start;
    }
}
&-three{
    height: 85%;
    p{
        font-size: 14px;
        text-align: start;
        padding: 10px;
    }
    .scrollbar-demo-item{
        border: 1px solid #0E9CFF;
    }
}
}

.cell {
  padding: 3px 0;
  box-sizing: border-box;
}
.cell .text {
  width: 30px;
  display: block;
  margin: 0 auto;
  line-height: 0px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 50%;
}
.cell.current .text {
  color: #626aef;
}
.cell .holiday {
  position: absolute;
  width: 3px;
  height: 3px;
  background: var(--el-color-danger);
  border-radius: 50%;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
}

</style>