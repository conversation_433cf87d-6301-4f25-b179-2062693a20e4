<template>
  <div class="deep-selector-example">
    <h2 class="title">Vue 3.0 深度选择器示例</h2>
    
    <div class="section">
      <h3>基本用法</h3>
      <div class="example-box">
        <el-input v-model="input1" placeholder="基本深度选择器"></el-input>
      </div>
    </div>
    
    <div class="section">
      <h3>嵌套用法</h3>
      <div class="nested-example">
        <el-input v-model="input2" placeholder="嵌套深度选择器"></el-input>
        <el-button type="primary">提交</el-button>
      </div>
    </div>
    
    <div class="section">
      <h3>选择器组合用法</h3>
      <div class="combined-selectors">
        <el-select v-model="select" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
    </div>
    
    <div class="section">
      <h3>多层嵌套应用</h3>
      <div class="multi-layer">
        <el-form :model="form" label-width="120px">
          <el-form-item label="表单示例">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 数据定义
const input1 = ref('');
const input2 = ref('');
const select = ref('');
const options = [
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' }
];
const form = reactive({
  name: ''
});
</script>

<style lang="scss" scoped>
// 主容器样式
.deep-selector-example {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 20px;
  background-color: #f5f7fa;
  
  .title {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
  }
  
  .section {
    margin-bottom: 30px;
    
    h3 {
      font-size: 18px;
      margin-bottom: 10px;
      padding-left: 10px;
      border-left: 4px solid #01C2FF;
    }
  }
  
  // 第一个例子：基本的深度选择器用法
  .example-box {
    // 在Vue 3中使用:deep()语法
    :deep(.el-input__wrapper) {
      background-color: rgba(15, 43, 63, 0.6);
      box-shadow: 0 0 0 1px #03FBFF;
    }
    
    :deep(.el-input__inner) {
      color: #fff;
    }
  }
  
  // 第二个例子：嵌套用法
  .nested-example {
    padding: 15px;
    background: #f0f9ff;
    border-radius: 4px;
    
    // 嵌套在父选择器中使用深度选择器
    :deep(.el-input__wrapper) {
      background-color: rgba(1, 194, 255, 0.1);
    }
    
    :deep(.el-button) {
      background: linear-gradient(90deg, #0E4DAC 0%, #072A82 52%, #0E4CAC 100%);
      border-color: #03FBFF;
      
      &:hover, &:focus {
        background: linear-gradient(90deg, #4c7bc2 0%, #0e43ca 52%, #4c84d6 100%);
        color: aqua;
      }
    }
  }
  
  // 第三个例子：选择器组合
  .combined-selectors {
    // 组合选择器使用深度选择器
    :deep(.el-select__wrapper) {
      background-color: rgba(15, 43, 63, 0.6);
    }
    
    :deep(.el-select-dropdown__item) {
      &.hover, &:hover {
        background-color: #01C2FF;
      }
    }
  }
  
  // 第四个例子：多层嵌套
  .multi-layer {
    background: #f9f0ff;
    padding: 15px;
    border-radius: 4px;
    
    // 多层嵌套时的深度选择器用法
    :deep(.el-form-item__label) {
      color: #01C2FF;
    }
    
    :deep(.el-input__wrapper) {
      background-color: rgba(15, 43, 63, 0.6);
    }
    
    :deep(.el-input__inner) {
      color: #fff;
    }
  }
}
</style>

<style>
/* 全局样式示例 - 不需要使用深度选择器 */
.global-class {
  color: #01C2FF;
}
</style> 