<template>
        <div class="bodyimg">
            <div class="bgimg">
                <!-- <img class="animate__animated animate__zoomIn" id="imgs" src="@/assets/img/001.jpg" alt=""> -->
                <img  id="imgs" v-if="!imgsrc" src="@/assets/img/001.jpg" alt="">
                <img  v-else :src="imgsrc" alt="">
                <!-- <canvas class="animate__animated animate__zoomIn" id="canvas" :width="widths" :height="heights" ref="canvas" ></canvas> -->
                <div class="bgimg-left"></div>
                <div class="bgimg-right"></div>
                <div class="bgimg-top"></div>
                <div class="bgimg-bottom"></div>
            </div>
            <el-container class="bodycont">
                <el-header>
                    <img class="bgimg" src="@/assets/img/home/<USER>" alt="" srcset="">
                    <div class="header">
                        <div class="left">
                            <img class="logo" src="@/assets/img/home/<USER>" alt="">
                            <span>{{username}}智慧工地平台</span>
                        </div>
                        <marquee class="project" :texts="PROJECTNAME" ></marquee>
                        <div class="right">
                            <span class="right-span">{{headertime}}</span>
                            <!-- <div>
                                <el-popover placement="bottom" :width="200" trigger="click">
                                        <span>当前主题：</span>
                                    <div class="themecolor">
                                        <div class="theme-one cursor" v-for="(item,index) in colorlist"
                                        :key="index" :style="`background:${item.bgcolor}; ${falge==index?'border:2px solid #66C023':''}`" @click="setcolor(item,index)"></div>
                                    </div>
                                    <template #reference>
                                    <i title="切换主题" class="iconfont icon-zhuti- cursor"></i>
                                    </template>
                                </el-popover>
                            </div> -->
                            <i v-for="(item,index) in iconlist" :key="index" :title="item.name"
                             :class="['iconfont icon-'+item.icon,'cursor']" @click="setout(index)"></i>
                        </div>
                    </div>
                    <order ref="orders"></order>
                </el-header>
                <!-- <navmenu></navmenu>
                <el-scrollbar height="800px">
                    <router-view />
                </el-scrollbar> -->
                <router-view />

                <navmenu></navmenu>
                 
            </el-container>
        </div>
        
</template>

<script>
import { onMounted, ref,onBeforeUnmount ,getCurrentInstance, nextTick} from 'vue';
import navmenu from "@/views/nav/nav.vue";
import {useStore} from 'vuex'
import order from "@/components/connet/Home/delog/order.vue";
import { datatime } from "@/network/formdata";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import {fabric} from 'fabric'
import { ElMessage, ElMessageBox } from 'element-plus'
import marquee from "@/components/connet/Common/marquee.vue";
// import  navmenu from "@/views/nav1.vue"
let context
export default {
    components:{
        navmenu,
        order,
        marquee
    },
    setup(){
        const store = useStore();
        let orders=ref(null)
        let falge=ref(0)
        let color1=ref('#409EFF')
        let bgcolor=ref({})
        let pics=ref(require('@/assets/img/001.jpg'))
        let PROJECTNAME=ref(store.getters.date[0].PROJECTNAME)
        let heights=ref(window.innerHeight)
        let widths=ref(window.innerWidth)
        let colorlist=ref([{
            bgcolor:'#0096C7',//背景颜色
            chamfer:'#03FBFF',//倒角颜色
            titlecolor:'#01C2FF',//标题背景
            font:'#03FBFF',//字体颜色
            navcolor:'0, 48, 70',//导航背景
            hovercor:'#03FBFF',//鼠标移入
            delogcolor:'2, 193, 253',//弹窗背景
            changcolor:'#01C2FF'//弹窗选择背景
            },
            {
            bgcolor:'#0D0D36',
            chamfer:'#fff',
            titlecolor:'#17215C',
            font:'#fff',
            navcolor:'23, 33, 92',
            hovercor:'#2148DD',
            delogcolor:'77,88,188',
            changcolor:'#4D95FA'//弹窗选择背景

                 
            }])
        // const sseion = inject('$sseion')
        // console.log('打印',getCurrentInstance().appContext.config.globalProperties.$sseion);
        let time=ref(null)
        let username=ref('')
        
        let headertime=ref()
        let getform=ref({
            ProjectCode:store.getters.code,
        })
        let imgsrc=ref('')
        let iconlist=[{
            name:'预警',
            icon:'yujing'},
            {
            name:'设置',
            icon:'shezhi'},
            {
            name:'退出',
            icon:'tuichu'
            }
            ]
        const $sseion = getCurrentInstance().appContext.config.globalProperties.$sseion
        onMounted(()=>{
            // var canvas = document.getElementById("canvas")
            // context = canvas.getContext("2d");

            // var myimg = new Image();
            // var img = new Image();
            // let img = document.getElementById('imgs');
            // img.src ='https://ai-zqface-com-wjgl.oss-cn-hangzhou.aliyuncs.com/pcfilestest/admin/1685610744876494097.png';
            // img.src =pics.value
            // img.src ='/assets/img/001.jpg';
            // // console.log('获取图片',myimg,context);
            
            // // myimg.onload = function(){

            // // context.drawImage(myimg,0,0,512,384);
            // console.log('获取图片',img);

            // // }
            // img.addEventListener('load', ()=> {
            //     context.drawImage(img, 0, 0,900,600);
            // });
            
                // new devPixelRatio().init();
            let name=store.getters.username
            var reg = /[\u4e00-\u9fa5]/g;
            if (name.match(reg)) {
            username.value = name.match(reg).join("");
            
            }else{
            username.value =""
            }
            // 获取窗口宽度
            time.value=setInterval(()=>{
               headertime.value=datatime().newDate
            })
            getimg()
            // nextTick(()=>{
                
            //     initCanvas(this)
            // })
        })
        window.addEventListener('setthcolor', ()=> {
            //   console.log('第一个模块');
            bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
            colorlist.value.forEach((item,index)=>{
                if (bgcolor.value.bgcolor==item.bgcolor) {
                 falge.value=index
                    
                }
            })
        })
        onBeforeUnmount(()=>{
            // console.log('销毁');
            clearInterval(time.value)
        })
        const setout=(index)=>{
            
            if (index==1) {
                // let theme =[
                //     {
                //     name:'Imageprogress',
                //     index:'2'
                //     },
                //     {
                //     name:'Equipment',
                //     index:'1'
                //     },
                //     {
                //     name:'produce',
                //     index:'3'
                //     },
                //     {
                //     name:'project',
                //     index:'4'
                //     },
                //     {
                //     name:'quality',
                //     index:'5'
                //     },
                //     {
                //     name:'secure',
                //     index:'6'
                //     },
                // ]
                // console.log('打印');
                nextTick(()=>{
                    // console.log('获取子弹窗',orders.value);
                    
                orders.value.showdelog()

                })
                // window.sessionStorage.setItem("data", JSON.stringify(res.data));
            // $sseion('theme',JSON.stringify(theme))
            }else if (index==2) {
                store.dispatch('removealls')
            }
        }
        const setcolor=(val,index)=>{
            ElMessageBox.confirm(
                '该操作将进行主题切换是否进行?',
                '提示',
                {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
                }
            )
                .then(() => {
                 $sseion('themecolor',JSON.stringify(val))
                 falge.value=index

                ElMessage({
                    type: 'success',
                    message: '成功切换',
                })
                })
                .catch(() => {
                ElMessage({
                    type: 'info',
                    message: '已取消切换',
                })
                })
        }
        const getimg=async()=>{
            const {data:res}=await gettable('GetIndexBGImageURL',getform.value)
            if (res.code=="1000") {
                imgsrc.value=res.data
            }
        }
      const initCanvas=(that)=>{
      // 1. 实例化canvas 画布
      var canvas = new fabric.Canvas('canvas', {
        perPixelTargetFind: true, //这一句说明选中的时候以图形的实际大小来选择而不是以边框来选择
        hasBorders: false,
      })
    //   console.log('canvas中',fabric.Image);
      
      // 2. 设置背景图片做为底图
      fabric.Image.fromURL(require('@/assets/img/001.jpg'), function (img) {
        // console.log('canvas画图',img);
        
        //保证背景图1:1铺满容器 
        canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
          scaleX: canvas.width / img.width,
          scaleY: canvas.height / img.height,
        })
      })
      //获取坐标点位置
 
      fabric.Image.fromURL(require('@/assets/img/home/<USER>'), function (oImg) {
        oImg.scale(1).set({
          left: 10,
          top: 10,
          name: 'point',
          hasBorders: false, //不显示选中时的边
          hasControls: false, //不显示选中时的9个方块
          lockMovementX: true,
          lockMovementY: true,
        })
        canvas.add(oImg)
      })
    //   fabric.Image.fromURL(require('@/assets/img/home/<USER>'), function (oImg) {
    //     oImg.scale(1).set({
    //       left: 760,
    //       top: 350,
    //       name: 'point2',
    //       hasBorders: false, //不显示选中时的边
    //       hasControls: false, //不显示选中时的9个方块
    //       lockMovementX: true,
    //       lockMovementY: true,
    //     })
    //     canvas.add(oImg)
    //   })
      //鼠标按下事件
      canvas.on('mouse:down', function (e) {     
        this.panning = true
      })
      //鼠标抬起事件
      canvas.on('mouse:up', function (e) {
        this.panning = false
        canvas.selectable = false
        if(e.target&&e.target.name){     
          that.showDialog(e.target.name)
        }
         
        // alert(e.target.name)
      })
      // 移动画布事件
      canvas.on('mouse:move', function (e) {
        if (this.panning && e && e.e) {
          var delta = new fabric.Point(e.e.movementX, e.e.movementY)
          canvas.relativePan(delta)
        }
      })
      // 鼠标滚动画布放大缩小
      canvas.on('mouse:wheel', function (e) {
        var zoom = (e.e.deltaY > 0 ? -0.1 : 0.1) + canvas.getZoom()
        zoom = Math.max(0.8, zoom)
        //最小为原来的1/10
        zoom = Math.min(3, zoom)
        //最大是原来的3倍
        var zoomPoint = new fabric.Point(e.e.pageX, e.e.pageY)
        canvas.zoomToPoint(zoomPoint, zoom)
      })
    }
        
        // onB
        return{
            bgcolor,
            heights,
            widths,
            pics,
            username,
            PROJECTNAME,
            falge,
            color1,
            initCanvas,
            orders,
            time,
            headertime,
            getform,
            iconlist,
            setout,
            imgsrc,
            colorlist,
            setcolor,
            getimg
        }
    }

}
</script>
<style lang="scss">
    .themecolor{
        display: flex!important;
    }
    .themepopover{
        .mrthem{

        }
    }
    .canvas-container{
    position: fixed!important;
}
</style>
<style lang="scss" scoped>
.bodyimg{
    position: relative;
}
.el-header{
    .bgimg{
        width: 100%;
        height: 100%;
    }
    .header{
        position: fixed;
        width: 100%;
        // height: 9%;
        color: #fff;
        display: grid;
        grid-template-columns: repeat(3,33.3%);
        // display: flex;
        // align-items: center;
        // justify-content: space-between;
        top: 0;
        .left{
            margin:2px 5px;
            display: flex;
            align-items: center;
            font-weight: bold;
            // width: 30%;
            font-family:YouSheBiaoTiHei!important;
            font-size: 20px;
            // font-weight: normal;
            line-height: normal;
            text-align: center;
            letter-spacing: 0.3em;
            color: #FFFFFF;
        }
        .right{
            margin: 10px 10px 0 10px;
            display: flex;
            // height: 9%;
            // width: 24%;
            align-items: center;
            justify-content: flex-end;
            &-span{
                display: inline-block;
                width: 50%;
                // margin-right: 10%;
            }
            .iconfont{
                font-size: 25px!important;
                margin:0 10px;
            }
            
            .icon-zhuti-{
                font-size: 25px!important;
            }
            
        }
        .logo{
            // width: 7%;
            // height: 5%;
            width: 46px;
            height: 36px;
            margin-right: 10px;

        }
        .project{
            font-size: 30px;
            padding-top: 10px;
            font-weight: bold;
        }
    }
}
:deep(.theme-one){
    width: 40px;
    height: 40px;
    margin: 5px;
    // display: flex;
    
}

.el-header{
    padding: 0px!important;
}
@mixin fiex($deg,$wids,$heig,$lef) {
     position: fixed;
        height: $heig;
        width: $wids;
        opacity: 1;
        // background: linear-gradient($deg, #012a64 -12%, rgba(0,52,75,0.00) 97%);
        background: linear-gradient($deg, #012a64 $lef, rgba(0,52,75,0.00) 97%);
       
}
.bgimg{
    width: 100%;
    height: 100%;
    img{
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 100%;
    }
    &-left{
        @include fiex(90deg,35%,100%,30%);
    }
    &-right{
        @include fiex(90deg,35%,100%,30%);
        transform: rotate(-180deg);
        right: 0;
    }
    &-top{
        top: 0;
        @include fiex(180deg,100%,35%,25%);
    }
    &-bottom{
        bottom: 0;
        @include fiex(0deg,100%,20%,-12%);
    }
}
.bodycont{
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
}

</style>