<template>
  <div :class="getform.Type=='餐具消毒记录表'?'':'tablelists'" >
    <el-table
        :data="tableData" style="width: 100%" border
        :header-cell-style="{background:'#FAFAFA',color:'#000000'}" ref="multipleTable"
        empty-text="暂无数据" max-height="600px" :span-method="objectSpanMethod"
        >
        <template #empty>
            <el-empty  v-loading="loading"></el-empty>
        </template>
        <el-table-column v-for="(item,index) in tablelist" :key="index"  :label="item.name" 
        align="center" :prop="item.value" :width="item.widths">
            <template #default="scope">
                <span v-if="encryption.includes(item.name)">{{scope.row[item.value].replace(/(\w{4})\w*(\w{4})/,'$1********$2')}}</span>
                <span v-else-if="answer.includes(item.name)">{{scope.row[item.value]=='是'?'√':'×'}}</span>
                <div v-else-if="getform.Type=='培训台账'&&item.name=='主要培训内容摘要'" class="expandable-cell"
                :class="{ 'expanded': expandedRows[scope.$index] }"
                @click="toggleExpand(scope.$index)">
                    <span>{{scope.row[item.value]}}</span>
                    <el-button class="open" v-if="!expandedRows[scope.$index]" link type="primary">... 展开</el-button>
                </div>
                <span v-else>{{scope.row[item.value]}}</span>
            </template>
        </el-table-column>
        <el-table-column  v-if="getform.Type=='餐具消毒记录表'" label="操作"  align="center"  key="002">
            <template #header >
                <div>
                    <el-date-picker v-model="getform.Date" type="month" class="left"
                    format="YYYY-MM" @change="changedate" value-format="YYYY-MM" placeholder="选择日期" style="width:150px" >
                    </el-date-picker>
                    <span class="titlespan">{{times.replace('-','年')}}月份消毒记录</span>
                </div>
            </template>
            <el-table-column v-for="(xds,i) in headerlsit" :key="i" width="48" :label="(i+1).toString()"  align="center" prop="status">
                <template  #default="scope">
                    <span>{{scope.row.DetailList[i].status}}</span>
                </template>
            </el-table-column>
        </el-table-column>
        <el-table-column v-if="opter.includes(titles)" :label="optile" align="center" prop="" width="120">
            <template #default="scope">
                <el-button link type="primary" size="small" @click="look(scope.row,btns)">{{btns.btn}}</el-button>
            </template>
        </el-table-column>

    </el-table>
    <el-pagination v-if="getform.Type!='餐具消毒记录表'" class="right pagination" popper-class="pagination"
     v-model:current-page="getform.page" :page-size="getform.count" 
    :page-sizes="[5, 10, 20, 30]"  layout="total, sizes, prev, pager, next, jumper" :total="Total" 
     @size-change="handleSizeChange" @current-change="handleCurrentChange"
    />
    <el-dialog append-to-body title="人员信息" class="information"  v-model="dialogVisible" width="50%"
        :destroy-on-close="false">
        <span v-for="(item,index) in lables" :key="index">{{item.name+(forms[item.value]??'')}}</span>
        <div class="imgs">
            <img :src="`data:image/jpeg;base64,${forms.HeadImage}`" @click="preview(`data:image/jpeg;base64,${forms.HeadImage}`)" class="cursor" alt="" style="width:150px;height:200px">
            <img :src="image" alt="" class="cursor" @click="preview(image)" style="width:150px;height:200px">
        </div>
        <picimg ref="picimgs"></picimg>
    </el-dialog>
    <picimg ref="picimgs"></picimg>

  </div>
</template>

<script setup>
import { ref ,getCurrentInstance, watch} from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";

  const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist
  const $http = getCurrentInstance().appContext.config.globalProperties.$http
  const $formatDateTime = getCurrentInstance().appContext.config.globalProperties.$formatDateTime

    let threinput=['保护用地措施']
    let tableData=ref([])
    let tablelist=ref([])
    let loading=ref(false)
    let titles=''
    let getform=ref({
        ProjectCode:store.getters.code,
        InUserName:store.getters.username,
        TypeValue:'',
        Type:'',
        UsingArea:'',
        Volume:'',
        StageType:'',
        SumVolume:'',
        SearchType: "New",
        IDCardNumber:'',
        Date:'',
        page:1,
        count:10

    })
    let url=''
    let Total=ref(0)
    let opter=['施工现场人员实名制登记表','食堂从业人员健康证明登记表','特种作业人员登记表',
    '职业病防治体检登记表','培训台账'
    ]
    let dialogVisible=ref(false)
    let geturl=''
    let forms=ref({})
    // 弹窗
    let lables=[]
    let image=ref('')
    let picimgs=ref(null)
    let btns={
        btn:'查看',
        value:''
    }
    let source=ref(null)
    let encryption=['身份证号码']
    let answer=['生活区','办公区','生产区','厕所','排水沟','食堂']
    let times=ref('')
    let headerlsit=ref([])
    let optile='操作'
    const expandedRows = ref({});
     const showdelog=(val)=>{
        // console.log('展示表格',val);
        optile='操作'
        titles=val.Type
        getform.value.TypeValue=val.TypeValue
        getform.value.Type=val.TypeValue
        btns.btn=''
        btns.value=''
        if (source.value) {
            source.value.cancel("取消")
        }
        tablelist.value=$labelist(val.Type)
        // console.log('获取表头',tablelist.value);
        switch (val.Type) {
            case '保护用地措施':
                url='GetProtectionLandTable'
                break;
            case '施工现场人员实名制登记表':
                url='GetProjectWorkerTable'
                geturl='GetProjectWorkerDetail'
                lables=$labelist('施工现场人员实名制登记表查看')
                btns.btn='查看'
                break;
            case '食堂从业人员健康证明登记表':
                url='GetCanteenHealthTable'
                btns.btn='查看健康证'
                btns.value='HealthCopy'
                break;
            case '特种作业人员登记表':
                url='GetRLZGCertificateTable'
                btns.btn='查看证件照片'
                btns.value='CertificatePhoto'
                break;
            case '职业病防治体检登记表':
                url='GetOccupationDiseaseTable'
                btns.btn='查看'
                btns.value='ExamReport'
                break;
            case '施工现场卫生保洁责任表':
                url='GetRLCleanResponse'
                break;
            case '洒水记录表':
                url='GetRLPurlingRecord'
                break;
            case '餐具消毒记录表':
                getform.value.Date=$formatDateTime(new Date(),'yyyy-MM')
                times.value=getform.value.Date
                url='GetDisinfecteRecordTable'
                break;
            case '施工现场消毒记录表':
                url='GetSterilizationRecordTable'
                break;
            case '培训计划':
                url='GetRLTrainPlanTable'
                break;
            case '培训台账':
                url='GetRLTrainLedger'
                optile='培训照片'
                btns.btn='查看照片'
                btns.value='TrainImages'
                break;
        }
        const CancelToken = $http.CancelToken;
    	source.value = CancelToken.source();
        gettablelist()
     }
    const gettablelist=async()=>{
        // console.log('获取表格',getform.value);
        tableData.value=[]
        // Total.value=0
        loading.value=true
        const {data:res}=await gettable(url,getform.value,source.value)
        // console.log('获取列表数据',res);
        loading.value=false
        tableData.value=res.data
        Total.value=res.Total
        if (getform.value.Type=='餐具消毒记录表') {
            headerlsit.value=res.data[0].DetailList
        }
    }
    const changedate=(val)=>{
        // this.times=val
        // this.gettable(this.url)
        gettablelist()
    }
    const look=async(val,value)=>{
        // console.log('点击详情',value);
        if (!value.value) {
            getform.value.IDCardNumber=val.IDCardNumber
            dialogVisible.value=true
            const {data:res}=await gettable(geturl,getform.value)
            // console.log('获取详情',res);
            forms.value=res.data[0]
            image.value=forms.value.Images[0]?.image
        }else{
            preview(val[value.value])
        }

    }
    const preview=(val)=>{
        let imgtype=['jpg','png','Jpeg','jpeg','JPG']
            if (val) {
                let lastIndex= val.lastIndexOf('.')
                let file=val.substring(lastIndex+1)
                let first=val.includes('data:')  
                if (imgtype.includes(file)||first) {
                    picimgs.value.piclist(val)
                }else{
                window.open('https://f.zqface.com/?fileurl='+val,'_slef')
                }
            }
    }
    const toggleExpand = (index) => {
        expandedRows.value  = {
            ...expandedRows.value, 
            [index]: !expandedRows.value[index] 
        };
    }
    const objectSpanMethod=({ row, column, rowIndex, columnIndex })=>{
            // console.log('合并单元格',row, column, rowIndex, columnIndex);
            if (threinput.includes(getform.value.Type)) {
                
                if (columnIndex === 0) {
                    if (rowIndex % 2 === 0) {
                        return {
                        rowspan: 2,
                        colspan: 1
                        };
                    } else {
                        return {
                        rowspan: 0,
                        colspan: 0
                        };
                    }
                    }
            }
            if (getform.value.Type=='餐具消毒记录表') {
                
                if (rowIndex== tableData.value.length-2||rowIndex== tableData.value.length-1) {
                // console.log('合并');
                    if (columnIndex === 0) {
                        return [0, 0];
                    } else if (columnIndex === 1) {
                        return [1, 2];
                    }
                    }
            }

    }
    const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        gettablelist()
        }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${ val}`)
        getform.value.page=val
        gettablelist()
        }
    defineExpose({
        showdelog
    })
</script>
<style lang="scss">

.tablelists{
    .el-table .el-table__cell{
        padding: 20px 0px!important;
    }
    .el-pagination{
    --el-pagination-text-color:#000!important;
    --el-pagination-button-color:#000!important;
    }
    .el-pagination__total{
        color: #000!important;
    }
    .el-pagination__jump{
        color: #000!important;
    }
}
.information{
    .el-dialog__body{
        display: grid;
        grid-template-columns: repeat(2,50%);
        span{
            display: inline-block;
            margin: 10px;
        }
    }
}
.pagination{
    .el-select-dropdown__item.is-hovering{
        background-color:#4CBCEF!important;
    }
}

</style>
<style lang="scss" scoped>
.tablelists{
    width: 100%;
    height: 100%;
}
.imgs{
    grid-column: 2;
    grid-row: 1/span 5;
    img{
        margin: 10px;
    }
}
.expandable-cell {
  position: relative;
  cursor: pointer;
  line-height: 1.5;
  transition: all 0.3s ease;
  /* 折叠状态样式 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  .open{
    position: absolute;
    float: right;
    // clear: both;
    // margin-left: 20px;
    font-size: 15px;
    top: 22px;
    right: 0;
    // width: 10px;
    background: #fff;
  }
  .open:hover{
    background: #fff;

  }

}
 
.expanded {
  /* 展开状态样式 */
  -webkit-line-clamp: unset;
  white-space: pre-wrap;
  word-break: break-word;
  background-color: var(--el-color-primary-light-9);
  
}
</style>