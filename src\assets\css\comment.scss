// 定义全局变量
$primary-color: #01C2FF;
$border-color: #03FBFF;
$bg-color: rgba(15, 43, 63, 0.6);
$text-color: #fff;

:root {
  --title-color: #fff;
  --delog-color: 0, 48, 70;
  --hover-color: #0346b0;
  --font-color: #fff;
  --dialog-bg-color: 2, 193, 253;
}
// 通用样式
%flex-center {
  display: flex;
  align-items: center;
}

%border-style {
  opacity: 1;
  border-color: $border-color;
}

// 图标容器样式
.lefticon, .righticon {
  @extend %flex-center;
  width: 100%;
  height: 15%;
  opacity: 1;
  padding: 0 4%;
  background: linear-gradient(90deg, 
    $primary-color 0%, 
    rgba($primary-color, 0) 97%
  );
}

.lefticon {
  padding-left: 4%;
  clip-path: polygon(0 0px, -61px 100%, 8px 30px, 4% 100%, 100% 100%, 100% 0, 0 0);
}

.righticon {
  padding-right: 4%;
  justify-content: flex-end;
  background: linear-gradient(90deg, 
    rgba($primary-color, 0) 0%, 
    $primary-color 97%
  );
  clip-path: polygon(0 0,0 100%,100% 100%,171% -135%,97% 26%,13% -214px);
}

// 文本样式
.padding {
  padding: 9px;
  margin: 1px;
}

.padding-text-span {
  opacity: 1;
  font-family: '思源黑体';
  font-size: 18px;
  font-weight: 700;
  line-height: 28px;
  letter-spacing: 0;
}

// 边框装饰样式
%corner-border {
  width: 10.06px;
  height: 10.84px;
  position: absolute;
  @extend %border-style;
}

.leftbefore {
  @extend %corner-border;
  left: 0;
  top: 0;
  border-top: 2px solid $border-color;
  border-left: 2px solid $border-color;
}

.leftafter {
  @extend %corner-border;
  left: 0;
  bottom: 0;
  border-bottom: 2px solid $border-color;
  border-left: 2px solid $border-color;
}

.rightbefore {
  @extend %corner-border;
  right: 0;
  top: 0;
  border-top: 2px solid $border-color;
  border-right: 2px solid $border-color;
}

.rightafter {
  @extend %corner-border;
  right: 0;
  bottom: 0;
  border-bottom: 2px solid $border-color;
  border-right: 2px solid $border-color;
}

// Element Plus 组件样式覆盖
.el-table,
.el-table__expanded-cell {
  background-color: transparent !important;
}

.el-table {
  --el-table-tr-bg-color: transparent !important;
  --el-table-bg-color: transparent;
  
  .warning-row {
    background: $bg-color !important;
  }
}
// 占位符样式
input {
  &:-moz-placeholder,
  &:-ms-input-placeholder,
  &::-webkit-input-placeholder {
    color: $text-color !important;
  }
}

// 日期选择器样式
.el-date-picker {
  &__header-label,
  &__icon-btn {
    color: $text-color !important;
  }
  
  .el-date-table th {
    color: $text-color !important;
  }
}

// 下拉选择器样式
.el-select-dropdown {
  &__item {
    color: $text-color !important;
    
    &.hover,
    &:hover {
      color: $text-color !important;
      background-color: $primary-color !important;
    }
  }
}

// 对话框样式
.delogss {
  background: transparent !important;
  
  .el-dialog {
    &__header {
      display: none !important;
    }
    
    &__body {
      padding: 0 !important;
      background: transparent !important;
    }
  }
  
  .headertop {
    background: transparent !important;
  }
  
  .el-table .warning-row {
    background: $bg-color !important;
  }
}

// 其他工具类
.widths {
  width: 100%;
  height: 100%;
}

.iconscre {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  margin-right: 10px;
}

.p {
  margin: 5px;
}

.el-pagination{
    margin-top: 10px;
}
 .el-table,
  .el-table__expanded-cell {
  background-color: transparent !important;
}
.el-table{
	--el-table-tr-bg-color:transparent!important;
	--el-table-bg-color:transparent;
}
.el-table .warning-row {
background: rgba(15, 43, 63, 0.6)!important;
}
.el-pagination{
--el-pagination-bg-color:transparent !important;
--el-pagination-text-color:#fff!important;
--el-pagination-button-color:#fff!important;
--el-pagination-button-disabled-bg-color:transparent !important;
}
.el-pagination__total{
    color: #fff!important;
}

.el-pagination__jump{
    color: #fff!important;

}
.el-input--small .el-input__inner{
  color: #fff!important;
}
.heaereq{
	position: relative;
	width: 140px;
	height: 30px;
	top: 0;
	// left: -12px;
	background: #000;
	clip-path: polygon(15% 0, 85% 0, 100% 100%, 100% 100%, 0 100%);
}
.heaereq-one{
	// position: relative;
	top: 0;
	height: 30px;
	.titlefont{
		padding: 2px;
		font-weight: bold;
		line-height: 26px;
	}
	.toplist{
		display: flex;
		.iconone{
			position: absolute;
			font-size: 39px;
			color: #fff;
			top: 35px;
			
		}
		&-icon{
			left: 0;
		}
		&-icon1{
			right: 20px;
		}
	}
  .close{
    width: 50px;
    height: 50px;
    position: relative;
    top: -30px;
    left: 97%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(45deg);
    border-radius: 4px;
    .closeicon{
        font-size: 40px!important;
        transform: rotate(-45deg);
        color: #fff;
    }   
}
}

.icontop{
	width: 100px;
	height: 2px;
	position: absolute;
	left: 20px;
}
.bor{
	width: 38px;
	height: 2px;
}
.icontop1{
	transform: rotate(-56deg);
	position:absolute;
	left: -9px;
	top: 16px;
}
.icontop2{
	transform: rotate(56deg);
	position:absolute;
	left: 111px;
	top: 16px;
}

input:-moz-placeholder {   
    color: #fff!important;   
}   
  
input:-ms-input-placeholder {   
    color: #fff!important;   
}   
input::-webkit-input-placeholder {   
    color: #fff!important;   
}
.is-pure{
	color: #fff!important;
	background: rgba(15, 43, 63, 1)!important;
	border: 1px solid #46587c!important;
}
.el-picker-panel{
	color: #fff!important;
	background: rgba(15, 43, 63, 1)!important;
}
.delogtilte-one{
	position: relative;
	top: -83px;
	height: 0px;
	.titlefont{
		padding: 2px;
		font-weight: bold;
		line-height: 26px;
	}
	.toplist{
		display: flex;
		.iconone{
			position: absolute;
			font-size: 39px;
			color: #fff;
			top: 35px;
			
		}
		&-icon{
			left: 0;
		}
		&-icon1{
			right: 20px;
		}
	}
}

.closedelog{
	width: 50px;
	height: 50px;
	position: relative;
	top: 19px;
	left: 97%;
	display: flex;
	align-items: center;
	justify-content: center;
	transform: rotate(45deg);
	border-radius: 4px;
	.closeicon{
		font-size: 40px!important;
		transform: rotate(-45deg);
		color: #fff;
	}   
}

.datedelog{
	padding: 10px;
	
}
.datedelog-p{
	font-size: 18px;
	font-weight: bold;
}
.datedelog-header{
		margin: 20px 0px;
		width: 100%;
		height: 50px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
.datedelog-body-one{
		display: flex;
		align-items: center;
		
	}
.monitor-two-btn{
	position: relative;
    padding: 10px;
	margin: 5px;
	border: 2px solid #03558F;
}

.opens{
	font-size: 90px!important;
	position: absolute!important;
	top: 40%;
}
.rightopen{
	right: 0;
}
.leftopen{
	left: 0;
}
.family{
	font-family: 'HarmonyOS Sans SC';
	
}
.bodybottom {
	border: 2px solid var(--title-color);
	background: rgba(var(--dialog-bg-color), 0.35);
  padding: 20px;
}

// 可视化大屏
.large-bg{
    height: 100% !important;
    width: 100% !important;
    background-image: url('@/assets/img/all/bgimg.jpg') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    position: relative;
    overflow: hidden;
}

// 默认样式
.bules{
  .el-select__wrapper{
    background-color: rgba(15, 43, 63, 0.6)!important;
    color: #fff!important;
  }
  .el-input__wrapper{
    background-color: rgba(15, 43, 63, 0.6)!important;
    color: #fff!important;
  }
  .el-select__input-wrapper{
    background-color: rgba(15, 43, 63, 0.6)!important;
    color: #fff!important;
  }
  .el-select-dropdown__item.is-hovering{
  background-color: $primary-color !important;
}
.el-select__placeholder{
  color: #fff!important;
}
.el-table{
  --el-table-row-hover-bg-color:rgba(1, 194, 255, 0.6);

}
.el-table .warning-row {
  //   --el-table-tr-bg-color: #000 !important;
  background: rgba(15, 43, 63, 0.6)!important;
}
.el-date-picker{
--el-datepicker-icon-color:#fff!important;
}
// --el-datepicker-icon-color:#fff!important;
}