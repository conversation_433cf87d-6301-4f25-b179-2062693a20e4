import request from "@/network/request";
import axios from 'axios'
// 获取列表
export function gettable(url,data,source) {
    // console.log('传值',data,source);
    if (data) {
    return request({
      url: `/Api.ashx?PostType=get&Type=`+url,
      method:"post",
      data,
      cancelToken: source?.token
      // params:data
    })
  }
  }
// 添加数据
export function setdata(url,data) {
    // console.log('传值',data);
    if (data) {
    return request({
      url: '/Api.ashx?PostType=set&Type='+url,
      method:"post",
      data
      // params:data
    })
  }
  }
// 添加数据
export function deldata(url,data) {
    // console.log('传值',data);
    if (data) {
    return request({
      url: '/Api.ashx?PostType=del&Type='+url,
      method:"post",
      data
      // params:data
    })
  }
  }