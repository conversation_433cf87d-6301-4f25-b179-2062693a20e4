<template>
  <el-dialog :title="titles" class="diy-dialog diy--ts-dialog ai" v-model="dialogVisible"	width="70%" append-to-body >
	<el-table  :data="tableData" border style="width: 100%" class="bs-table cursor" max-height="500px"
  :header-cell-style="{background:'#FAFAFA',color:'#000000'}"
     empty-text="暂无数据">
       <template #empty>
            <el-empty  v-loading="loading"></el-empty>
        </template>
		    <el-table-column v-for="(item,index) in lables" :key="index" 
        :label="item.name" align="center" :prop="item.value" :width="item.widths">
        </el-table-column>
	</el-table>

    <el-pagination  @size-change="handleSizeChange" @current-change="handleCurrentChange"
		 	v-model:current-page="getform.page" :page-size="getform.count" :page-sizes="[5, 10, 20, 30]"
		 	layout="total, sizes, prev, pager, next, jumper" :total="Total">
	</el-pagination>

  </el-dialog>
</template>

<script>
export default {
    data(){
        return{
            dialogVisible:false,
            getform:{
                Dates: "",
                ID: "",
                InUserName: "",
                IsWhole: 0,
                MeasurementPoints: "",
                ProjectCode: "",
                Types: 1,
                page:1,
                count:10
            },
            tables:[
                {
                name:'序号',
                value:'rowNum',
                widths:'50'
                },{
                name:'测点编号',
                value:'MeasurementPoints',
                widths:''
                },{
                name:'时间',
                value:'Dates',
                widths:'100'
                },{
                name:'深度(m)',
                value:'Depth',
                widths:''
                },{
                name:'上次累计变量(mm)',
                value:'LastDisplaceTotal',
                widths:''
                },{
                name:'本次累计变量(mm)',
                value:'DisplaceTotal',
                widths:''
                },{
                name:'变化量(mm)',
                value:'Displace',
                widths:''
                },{
                name:'变化速率(mm/d)',
                value:'DisplaceRate',
                widths:''
                },
            ],
            tableData:[],
            titles:'',
            Total:0,
            lables:[],
            loading:false

        }
    },
    methods:{
        showdelog(val,labels){
            // console.log('显示弹窗',val);
            this.getform.ID=val.MonitorID
            this.getform.MeasurementPoints=val.MeasurementPoints
            this.getform.ProjectCode=JSON.parse(window.sessionStorage.getItem("code"))
            this.getform.InUserName=JSON.parse(window.sessionStorage.getItem("username"))
            this.tableData=[]
            let tablelab=[]
            switch (val.MonitorItems) {
                case '深层水平位移':
                      // console.log('获取0');
                      tablelab=JSON.parse(JSON.stringify(this.tables))
                      this.lables=tablelab

                        break;
                    case '围护墙(边坡)顶部水平位移':
                      // console.log('获取0');
                      tablelab=JSON.parse(JSON.stringify(this.tables))
                        tablelab.splice(3,1)
                        tablelab.splice(4,0,{
                            name:'本次变量(mm)',
                            value:'Displace',
                            widths:''
                        })
                    tablelab.splice(6,1)

                      this.lables=tablelab
                      
                        break;
                    case '支撑轴力':
                    case '锚杆轴力':
                        tablelab=[
                            {
                            name:'序号',
                            value:'rowNum',
                            widths:'50'
                            },{
                            name:'组号',
                            value:'MeasurementPoints',
                            widths:''
                            },{
                            name:'时间',
                            value:'Dates',
                            widths:'100'
                            },
                            {
                            name:'本次测值(kN)',
                            value:'ThisAxiaForce',
                            widths:''
                            },{
                            name:'上次测值(kN)',
                            value:'LastAxiaForce',
                            widths:''
                            },{
                            name:'本次变量(kN)',
                            value:'Displace',
                            widths:''
                            },{
                            name:'累计变量(kN)',
                            value:'DisplaceTotal',
                            widths:''
                            },
                        ]
                     this.lables=tablelab
                        break;
                    case '孔隙水压力':
                    case '围护墙侧向土压力':
                    case '围护墙内力':
                    case '立柱内力':
                        tablelab=JSON.parse(JSON.stringify(this.tables))
                        
                        this.lables=tablelab
                        break;
                    case '围护墙(边坡)顶部竖向位移':
                    case '立柱竖向位移':
                    case '坑底降起':
                    case '地下水位':
                    case '分层竖向位移':
                    case '地表竖向位移':
                    case '周围建筑物沉降':
                    case '管线沉降':
                    case '道路沉降':
                        tablelab=JSON.parse(JSON.stringify(this.tables))
                        tablelab.splice(3,1)
                        tablelab.splice(5,1)

                        let adds=[
                            {
                            name:'初始高程（m）',
                            widths:'',
                            value:'FirstHeight',
                            },{
                            name:'上次高程（m）',
                            widths:'',
                            value:'LastHeight',
                            },{
                            name:'本次高程（m）',
                            widths:'',
                            value:'ThisHeight',
                            }
                        ]
                        tablelab.splice(3,0,...adds)
                        tablelab.splice(7,0,{
                            name:'本次变量(mm)',
                            widths:'',
                            value:'Displace',
                            })
                        this.lables=tablelab
                        break;
                    case '测斜':
                      // console.log('测斜');
                      
                        tablelab=JSON.parse(JSON.stringify(this.tables))
                        let GUID={
                            name:'测斜孔号',
                            widt:'ReceiptNo',
                            porps:'ReceiptNo',
                        }
                        let sectsl1=[
                        {
                          name:'初始角度（°）',
                          widt:'',
                          porps:'',
                        },{
                          name:'上次角度（°）',
                          widt:'',
                          porps:'',
                        },{
                          name:'本次角度（°）',
                          widt:'',
                          porps:'',
                        },{
                          name:'上次累计变量（°）',
                          widt:'',
                          porps:'',
                        },{
                          name:'本次变量（°）',
                          widt:'',
                          porps:'',
                        },{
                          name:'本次累计变量（°）',
                          widt:'',
                          porps:'',
                        },{
                          name:'变化速率（°/d）',
                          widt:'',
                          porps:'',
                        }
                      ]
                        tablelab[0]=GUID
                        tablelab.splice(4,7,...sectsl1)

                        this.lables=tablelab
                        break;
                    // default:
            }
        this.titles=val.MonitorItems+val.MeasurementPoints+'历史数据'
        this.gettable()
        this.dialogVisible=true
        },
        async gettable(){
            this.loading=true
            this.tableData=[]
            const {data: res} = await this.$http.post(`/aiot/Api.ashx?PostType=get&Type=GetMonitorRecordTable`, this.getform)
            this.loading=false
            this.tableData=res.data
            this.Total=res.Total
        },
        handleSizeChange(newSize) {
            console.log("一页显示", newSize)
            this.getform.count = newSize;

            this.gettable()
        },
        handleCurrentChange(newSize) {
            console.log("一页显示", newSize)
            this.getform.page = newSize;

            this.gettable()
        },
    }
}
</script>
<style lang="scss">
.diy-dialog{
.el-pagination{
  --el-pagination-bg-color:transparent !important;
  --el-pagination-text-color:#000!important;
  --el-pagination-button-color:#000!important;
  --el-pagination-button-disabled-bg-color:transparent !important;
}
.el-pagination__total{
    color: #000!important;
}

.el-pagination__jump{
    color: #000!important;

}
}
</style>
<style lang="scss" scoped>
// .el-pagination{
//   color: #000!important;
// }
</style>