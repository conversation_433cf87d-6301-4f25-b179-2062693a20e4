<template>
  <!-- 出勤 -->
  <div class="organization padding"  :style="{color:bgcolor.font}">
    <div :class="['organization-top','lefticon']"
	:style="{background:'linear-gradient(90deg, '+bgcolor.titlecolor+' 0%, rgba(1, 194, 255, 0) 97%)'}"
	>     
      <img :src="teamtype.src"  >
      <span class="padding-text-span" >{{teamtype.titles}}</span>
      <el-button  :class="`rigs`" link  @click="getopen(teamtype.titles)" :style="`order:2`"> 更多记录</el-button>
      
    </div>
    <div class="organization-two">
      <div class="echatr">
        <ecahrtline  :ids="teamtype.type" :show="1" :options="addteions" style="z-index: 1"></ecahrtline>
        <img :class="teamtype.type+'12'" src="@/assets/img/technology/bgimg.png" alt="">
      </div>
      <!-- <div class="organization-three" v-if="falgeswiper"> -->
        <!-- <swiper class="swiperline"  :slides-per-view="2"  :direction="'vertical'"
         :autoplay="{ delay: 2000, disableOnInteraction: false }" loop :modules="modules">
            <swiper-slide v-for="(item, index) in gridData" :key="index" >
              <p  class="scrollbar-demo-item cursor" :style="[(index%2)!=0?'background: rgba(15, 43, 63, 0.6)':''
              ,teamtype.type!='weathersechart'?getcolor(item):'']"
              >{{ item.name }}</p>
            </swiper-slide>
        </swiper> -->
        <ScrollTable
            ref="scrollTableRef" class="scroll-table" :style="`height: ${scrollheight}px;`"
            :config="configs" @resize="handleResize">
            <template #cell="{ row, columnIndex }">
                <div class="scroll-table-cell" :style="teamtype.type!='weathersechart'?getcolor(row):''">
                  {{ row.name }}
                </div>
            </template>
        </ScrollTable>
    </div>
    <Chamfering :homeindex="'1'" :horn="0"></Chamfering>
    <delog ref="delogRef"></delog>

  </div>
</template>

<script>
import { computed, nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import ecahrtline from "@/components/connet/technology/ecahrtline/ecahrtline.vue";
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import ScrollTable from '@/components/connet/Common/ScrollTable.vue'
import delog from '@/components/connet/technology/content/delog.vue'

export default {
props:['homeindex','teamtype'],
components:{
  // echartsAttendance
  ecahrtline,
  Swiper,
  SwiperSlide,
  Chamfering,
  ScrollTable,
  delog
  // ecahrtss
  // echartsss
},
setup(props){

  let bgcolor=ref({})
  let falge=ref(0)
  let getform=ref({
      ProjectCode:store.getters.code,

    })
  let gridData=ref([ ])
  let windowHeight=ref(window.innerHeight)
  let addteions=ref([])
  let heights=ref('80px')
  let domid=[
    {
      name:'施工方案',
      value:'.organization12'
    },
    {
      name:'技术交底',
      value:'.disclosure12'
    },{
      name:'极端天气应急通知',
      value:'.weathersechart12'
    },
  ]
  let weaters=ref({})
  let colorlist=ref(['#f77b66','#3edce0','#f94e76','#018ef1'])
  let falgeswiper=ref(true)
  let scrollheight=ref('90')
  let configs=ref({
    // header:['获取来源','获取占比','积分数量'],
      data: [],
      oddRowBGC: 'transparent',
      evenRowBGC: 'rgba(15, 43, 63, 0.6)',
      waitTime: 3000,
      rowHeight: 35,
      rowNum: 3,
      hoverPause: true,
      index: false,
      list:['DisclosureName']
  })
  let delogRef=ref(null)

  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  window.addEventListener('resize', function(event) {
    var windowHeights = window.innerHeight;
    // console.log('窗口高度为：' + windowHeights);
    domid.forEach((item,index)=>{
      if (item.name==props.teamtype.titles) {
        getdoms(item.value,windowHeights)
      }
    })
    
  });
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    if (props.teamtype.titles=='极端天气应急通知') {
      getnosty()
    }else if (props.teamtype.titles=='施工方案') {
      geteharts()
    } else if (props.teamtype.titles=='技术交底') {
      gettechnology()
    }
  })
  const getcolor=(val)=>{
    return `border:1px solid ${val.value=='完成交底'?'#018ef1':(val.value=='待审批'?'#3edce0':(val.value=='待交底'?'#f94e76':'#f77b66'))}`
  }
  const getopen=(val)=>{
    console.log('获取更多记录',val);
    switch (val) {
      case '施工方案':
      delogRef.value.showdelog(val,'施工方案')
        
        break;
      case '技术交底':
      delogRef.value.showdelog(val,'技术交底')
        
        break;
    }
  }
  // 施工方案
  const geteharts=async()=>{
    const {data:res}=await gettable('GetSpecialProgramAnalysis',getform.value)
    // console.log('获取生产技术',res);
    if (res.code=="1000") {
      gridData.value=res.data.ProgramNameList.map((item,index)=>{
        return{
            name:item.ProgrammeName,
            value:item.ProgrammeState
          }
      })
      configs.value={
        ...configs.value,
        data:gridData.value
      }
      // addteions.value=
    addteions.value=  res.data.ProgramList.map((item,index)=>{
        // console.log('获取技术管理',item);
        return {
          name:item.name,
          value:item.value,
          itemStyle: {
                color: colorlist.value[index],
            },
        }
      })
      // console.log('技术管理',addteions.value);
    }
  }
  // 技术交底
  const gettechnology=async()=>{
    const {data:res}=await gettable('GetTechnicalDisclosureAnalysis',getform.value)
    if (res.code=="1000") {
        gridData.value=res.data.DisclosureNameList.map((item,index)=>{
          return{
            name:item.DisclosureName,
            value:item.DisclosureState
          }
        })
        configs.value={
          ...configs.value,
          data:gridData.value
        }
        addteions.value= res.data.DisclosureList.map((item,index)=>{
            return {
              name:item.name,
              value:item.value,
              itemStyle: {
                    color: colorlist.value[index],
                },
            }
          })
        }
  }
  // 极端天气
  const getnosty=async()=>{
    falgeswiper.value=false
    const {data:res}=await gettable('GetWarningWeatherNotice',getform.value)
    // console.log('获取极端天气',res);
    
      if (res.code=="1000") {
        weaters.value=res.data
        
        gridData.value=res.data.WarningList.map((item,index)=>{
          return{
            name:item.WarningInfo
          }
        })
        configs.value={
          ...configs.value,
          data:gridData.value
        }
        addteions.value= res.data.EcharstList.map((item,index)=>{
            // console.log('获取技术管理',item);
            return {
              name:item.name,
              value:item.value,
              itemStyle: {
                    color: colorlist.value[index],
                },
            }
          })
          falgeswiper.value=true
          // console.log('技术管理',addteions.value);GetWarningWeatherNotice
          
        }
  }
  const getdoms=(val,windowHeights)=>{
    let getpickerorgs=document.querySelector(val)
    // console.log('获取dom',getpickerorgs);
    if (getpickerorgs) {
      
    if (windowHeights>windowHeight.value) {
      getpickerorgs.classList.add("btnimgs");
      heights.value='90px'
    }else{
      getpickerorgs.classList.remove("btnimgs");
      heights.value='80px'
      
    }
    }
  }
  const handleResize = () => {
    scrollheight.value = window.innerHeight * 0.6;
  };
  const getsunm=async()=>{
    const {data:res}=await gettable('GetSpecialProgramAnalysis',getform.value)
    // console.log('获取考勤',res);
    // Attendslist.value=res.data.KQList
    // addteion.value=Attendslist.value
    // if (addteion.value.length>0) {
    //   addteion.value[0].itemStyle= {
    //             // opacity: 0.5,
    //             color: '#00b7f7' }
    //   addteion.value[1].itemStyle= {
    //             // opacity: 0.5,
    //             color: '#74f3b5' }
    // }
    
  }

	return{
		falge,
    heights,
		domid,
		getform,
		bgcolor,
    colorlist,
    weaters,
    modules: [Autoplay, Pagination, Navigation],
    falgeswiper,
    // btnlist,
    addteions,
    gridData,
    windowHeight,
    scrollheight,
    configs,
    delogRef,

    getdoms,
    geteharts,
    gettechnology,
    getcolor,
    getnosty,
    handleResize,
    getopen,
  // tableRowClassName
	}
}
}
</script>
<style lang="scss" scoped>
:deep(.dv-scroll-board .rows .ceil){
  padding: 0!important;
}
.organization{
&-two{
  height: 83%;
  .lables{
    display: flex;
    flex-wrap: wrap;
  }
}
.scroll-table{
  text-align: left;
  color:#03FBFF;
  font-size: 14px;
  &-cell{
    padding: 0 10px!important;
    font-size: 12px;
  }
}
.swiperline{
  height: 100%;
}
.tbable{
    background: rgba(15, 43, 63, 0.6)!important;

}
&-three{
    height: 80px;
    margin-top: 10px;
    // font-size: 10px;
    .scrollbar-demo-item{
        // font-size: 10px;
        padding: 5px 3px;
        height: 100%;
        text-align: start;
        font-size: 12px;
        font-weight: normal;
        line-height: 14px;
        letter-spacing: 0.5px;
    }
}
}
#organization{
    z-index: 1!important;
}
@mixin ecahts() {
  position: absolute;
    left: 23%;
    width: 53%;
    height: 87%;
    top: 15%;
}

.echatr{

  position: relative;
  width: 100%;
  height: 60%;

  .organization12{
  @include ecahts()
  }
  .disclosure12{
  @include ecahts()
  }
  .weathersechart12{
  @include ecahts()

  }
}
.rigs{
  margin-left: auto;
  font-weight: bold;
  color: #fff;
}
.btnimgs{
    position: absolute!important;
    left: 20%!important;
    width: 60%!important;
    height: 87%!important;
    top: 30%!important;
  }
</style>