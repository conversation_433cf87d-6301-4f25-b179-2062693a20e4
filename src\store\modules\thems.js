// import router from "@/router/index";
const theme = {
  state: {
    themecolor:JSON.parse(window.sessionStorage.getItem("themecolor")),
  },
  mutations: {
    setthemecolor: (state, color) => {
        // console.log('获取路由');
        state.themecolor=color
        sessionStorage.setItem("themecolor", JSON.stringify(color))

      },
    
  },
  actions: {
    getthemecolor:({commit}, color)=>{

      commit('setthemecolor', color)

    },
  }
}

export default theme
