<template>
  <el-dialog class="delogss"  v-model="dialogVisible"  title="" :width="dialogVisible1=='1'?'50%':'70%'" :before-close="handleClose" >
    <selection ref="selections" @colses="closes" :titles="titles"></selection>
    <div v-if="dialogVisible1=='0'" class="bodybottom" :style="getstyle()">
        <el-table :data="tableDate"  :style="[`width: 100%;color:${bgcolor.font};
             --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
             :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
             empty-text="暂无数据" max-height="500px"
             >
                 <template #empty>
                     <el-empty  v-loading="loading"></el-empty>
                 </template>
                 <el-table-column v-for="(item,index) in lables" :key="index" :width="item.widths"
                  :prop="item.value" :label="item.name" align="center"> </el-table-column>
                 <el-table-column prop="SnapImage" label="操作" align="center">
                     <template #default="scope">
                        <el-button type="primary" link @click="getdetil(scope.row)">查看</el-button>
                     </template>
                 </el-table-column>
         </el-table>
         <el-pagination
             v-model:current-page="getform.page"
             v-model:page-size="getform.count"
             :page-sizes="[10, 20, 50, 100]"
             layout="total, sizes, prev, pager, next, jumper"
             :total="Totles"
             @size-change="handleSizeChange"
             @current-change="handleCurrentChange"
         />
    </div>
    <div v-else :style="getstyle()" class="bodybottom">
        <el-steps :active="stepindex" align-center finish-status="success">
           <el-step  :title="item.title" v-for="(item,index) in steps" :key="index">
              <template #description >
                <div class="row1">
                  <span>{{item.description}}</span>
                  <span>{{item.time}}</span>
                </div>
              </template>
           </el-step>
         </el-steps>
        <div class="quilet">
            <div v-for="(item,index) in formlabes" :key="index" :style="getwidths(item.name)" class="lefs">
                <span :class="[item.type==0?'lefttips':'','text']" 
                :style="widths.includes(item.name)?'margin-left:5%':''">{{item.name}}{{addfrom[item.value]}}</span>
                <img v-if="item.type==1" class="cursor" :src="addfrom[item.value1]" alt="" @click="pic(addfrom[item.value1])" style="width:80px;height:80px">
            </div>
        </div>
    </div>
    <!-- <el-dialog class="" append-to-body v-model="dialogVisible1"  width="50%"  >
        <el-steps :active="stepindex" align-center finish-status="success">
           <el-step  :title="item.title" v-for="(item,index) in steps" :key="index">
              <template #description >
                <div class="row1">
                  <span>{{item.description}}</span>
                  <span>{{item.time}}</span>
                </div>
              </template>
           </el-step>
         </el-steps>
        <div class="quilet">
            <div v-for="(item,index) in formlabes" :key="index" :style="getwidths(item.name)">
                <span :class="[item.type==0?'lefttips':'','text']" 
                :style="widths.includes(item.name)?'margin-left:5%':''">{{item.name}}{{addfrom[item.value]}}</span>
                <img v-if="item.type==1" class="cursor" :src="addfrom[item.value1]" alt="" @click="pic(addfrom[item.value1])" style="width:80px;height:80px">
            </div>
        </div>
        <picimg ref="picimg"></picimg>
    </el-dialog> -->
    <picimg ref="picimg"></picimg>

  </el-dialog>
</template>

<script>
import { onMounted, ref } from 'vue'
import store from "@/store";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import picimg from "@/components/connet/Common/picimg.vue";
import {labelist} from "@/components/connet/Common/lables";
import selection from "@/components/connet/Common/selection.vue";
export default {
components:{
    picimg,
    selection
    },
setup(){
    let dialogVisible=ref(false)
    let bgcolor=ref({})
    let titles=ref('')
    let loading=ref(false)
    let Totles=ref(0)
    let tableDate=ref([])
    let getform=ref({
            ProjectCode:store.getters.code,
            InUserName:store.getters.username,
            Months: "",//月份  2024-10
            ReformStatic: "",//状态 传空是查全部；已整改是查合格；剩下就是其它状态的
            QualityPatType: "",//问题，安全类型
            TYPE: 0,
            page:1,
            count:10
        })
    let lables=ref([])
    let tablelabe=ref([])
    let dialogVisible1=ref(0)
    let formlabes=ref([])
    let addfrom=ref({})
    let picimg=ref(null)
    let stepindex=ref(1)
    let steps=ref([])
    let url=ref('GetQualityDetailInfo')
    let widths=['质检信息','所属项目：','问题类别：','问题描述：',
    '问题照片：','整改情况','整改描述：','整改照片：','复查结果',
    
    ]
    let history=ref([])
    let currentLevel=ref(0)
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        })

    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    })

    const showdelog=(val,type,values,name)=>{
        // console.log('记录',val.name,type,values);
        
        let sername=val?.seriesName
        formlabes.value=labelist('质量巡检详情')
        // titles.value=name?name:val.name+val.seriesName+'记录'
        titles.value=val.name?val.name+val.seriesName+'记录':val
        // lables.value=tablelabe.value
        lables.value=labelist('质量巡检')
        getform.value.ReformStatic=values
        getform.value.QualityPatType=name
        getform.value.Months=val?.name??''
        getform.value.TYPE=type
        history.value = []
        currentLevel.value = 0
        let tables1= JSON.parse(JSON.stringify(labelist('质量巡检')))
        url.value='GetQualityDetailInfo'
        if (type==1) {
        url.value='GetSafetyDetailInfo'
        let paly=['隐患类别：','隐患地点：']
            tables1.map((item,index)=>{
                if (item.name.substring(0, 2)=='问题') {
                    item.name = '隐患'+item.name.slice(2, item.name.length)
                }
            })
            formlabes.value.map((item,index)=>{

                if (item.name.substring(0, 2)=='问题') {
                    item.name = '隐患'+item.name.slice(2, item.name.length)
                }
                // console.log('获取',item.name);
                
                if(paly.includes(item.name)){
                    item.value=item.value1
                }

            })
            widths.map((item,index)=>{
                if (item.substring(0, 2)=='问题') {
                    item = '隐患'+item.slice(2, item.length)
                }
            })
            
        }
        switch (sername) {
            case '整改问题':
            case '整改隐患':
            lables.value= getretable(tables1)
                break;
            case '新增问题':
            case '新增隐患':
            let arr1=[
                {
                name:'状态修改时间',
                value:'OperateDate',
                widths:'',
                },{
                name:'状态修改人',
                value:'Operator',
                widths:'',
                }
            ]
            tables1.splice(7,2,...arr1)
            lables.value=tables1

                break;
            case '待整改问题':
            case '待整改隐患':

            let arr2=[
                {
                name:'状态修改时间',
                value:'OperateDate',
                widths:'',
                index:1
                },{
                name:'状态修改人',
                value:'Operator',
                widths:'',
                index:2
                }
            ]
            tables1.splice(7,2,...arr2)
            
            tables1.sort((a, b) => a.index - b.index)
            // console.log('获取',tables3);
            lables.value=tables1
            
                break;
        }
        gettables()
        dialogVisible.value=true
    }
    const gettables=async()=>{
        const {data:res}=await gettable('GetZLSafeCheckRecordTable',getform.value)
        // console.log('获取',res);
        if (res.code=="1000") {
            tableDate.value=res.data
            Totles.value=res.Total
        }
    }
    // 每列数据显示
    const getretable=(table)=>{
        let arr=[
                {
                name:'复查状态',
                value:'ReformStatic',
                widths:''
                },{
                name:'复查时间',
                value:'OperateDate',
                widths:''
                },{
                name:'复查人',
                value:'Operator',
                widths:''
                },
            ]
            let qast=[
                {
                name:'整改时间',
                value:'OperateDate',
                widths:''
                },{
                name:'整改人',
                value:'Operator',
                widths:''
                },
            ]
            table.splice(6,3,...arr)
            table.splice(1,2,...qast)
        return table
    }
    const closes=()=>{
        // dialogVisible.value=false
        // 常规情况：根据历史记录决定返回上一页或关闭对话框
      if (history.value.length > 0) {
          const prevState = history.value.pop();
          restoreState(prevState);
      } else {
          // 没有历史记录，关闭对话框
          dialogVisible.value = false;
      }
    }
    // const look=(val)=>{
    //     // console.log('查看',val);
    //     saveCurrentState();
    //     currentLevel.value++;
    //     dialogVisible1.value=1
    // }
    const pic=(val)=>{
        picimg.value.piclist(val)
    }
    const getstyle=()=>{
      return `border:2px solid ${bgcolor.value.titlecolor};
      background:rgba(${bgcolor.value.delogcolor},0.35)`
   }
    const getdetil=async(val)=>{
        saveCurrentState();
        currentLevel.value++;
        dialogVisible1.value=1
        let GUID={
            GUID:val.GUID
        }
        const {data:res}=await gettable(url.value,GUID)
        // console.log('获取',res);
        if (res.code=="1000") {
            addfrom.value=res.data[0]
            steps.value = res.data[0].steps

              steps.value.forEach((item,index)=>{
              item.id = index+1
              if (item.Status==1) {
              stepindex.value = steps.value[index].id
                
              }
            })
        }
        // dialogVisible1.value=true

    }
    const getwidths=(val)=>{

        return widths.includes(val)?`grid-column: 1/span 2;`:''
    }
    const handleClose=()=>{
        dialogVisible.value=false
    }
     // 保存当前页面状态到历史记录的函数
   const saveCurrentState = () => {
      history.value.push({
          titles: titles.value,
          lables: [...lables.value],
          getform: {...getform.value},
          tableDate: [...tableDate.value],
          Totles: Totles.value,
          level: currentLevel.value
      });
      // console.log('保存状态:', title.value, '层级:', currentLevel.value);
   }
   const restoreState = (state, clearHistory = false, newLevel = null) => {
      // 恢复基础状态
      dialogVisible1.value = 0;
      
      // 恢复所有页面状态
      titles.value = state.titles;
      lables.value = state.lables;
      getform.value = state.getform;
      tableDate.value = state.tableDate;
      Totles.value = state.Totles;
      // 设置导航层级
      currentLevel.value = newLevel !== null ? newLevel : state.level;
      // 如果需要清空历史记录
      if (clearHistory) {
          history.value = [];
      }
      
      // 重新获取数据
      gettables();
  };
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
        }
    
    const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        gettables()
        }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        getform.value.page=val
        gettables()
        }
    return{
        dialogVisible,
        titles,
        tablelabe,
        bgcolor,
        loading,
        Totles,
        getform,
        tableDate,
        lables,
        dialogVisible1,
        formlabes,
        addfrom,
        widths,
        picimg,
        stepindex,
        steps,
        url,


        handleClose,
        showdelog,
        handleSizeChange,
        handleCurrentChange,
        tableRowClassName,
        gettables,
        // look,
        getdetil,
        getwidths,
        pic,
        getretable,
        closes,
        getstyle

    }
}
}
</script>
<style lang="scss">
// .delogss{
//     background: transparent!important;

//     .el-dialog__header{
//         display: none!important;
//     }
//     .el-dialog__body{
//         padding: 0!important;
//         background: transparent!important;

//     }
//     .headertop{
//     background: transparent!important;

//     }
//     .el-table .warning-row {
//         background: rgba(15, 43, 63, 0.6)!important;
//     }
// }
</style>
<style lang="scss" scoped>
.headertop{
    height: 30px;
    // padding: 10px;
    position: relative;
    p{
        color: #fff;
        position: absolute;
        top: 8px;
        left: 15px;
        // font-size: 18px;
        font-weight: bold;
    }
}
.lefttips{
    font-size: 16px;
    font-weight: bold;
    margin-left: 2%!important;
}
.lefs{
    text-align: left;
}
.detils{
    // color: aqua;
    grid-row: 1;
    grid-column: 1/span 2;
}
.quilet{
    display: grid;
    grid-template-columns: repeat(2,50%);
    color: #fff;
}
.text{
    display: inline-block;
    margin: 10px;
    margin-left: 10%;
}
.heaereq{
    position: absolute;
    top: 0!important;
    left: 0!important;
}
// .detils{
//     text-align: left;
// }
.toplist{
    // position: absolute;
}
.closedelog{
    position: absolute;
    top: 0;
}
.bodybottom{
    padding: 10px;
}
</style>