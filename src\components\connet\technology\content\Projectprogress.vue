<template>
  <div class="Projectprogress" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]" >
    <el-scrollbar :height="scroles" ref="myScrollbar">
        <div class="myScrollbar" ref="conScrollbar">
            <div class="Projectprogress-count" v-for="(ite,i) in progresslist" :key="i">
            <div class="Projectprogress-count-round" :style="[i==0?'justify-content: flex-end':'']">
                <div class="line" v-show="i!=0" :style="getcolor(ite)"></div>
                <div class="round" :style="getbgcolor(ite)"></div>
                <div class="line" v-if="i!=progresslist.length-1"  :style="getcolor(ite)"></div>
            </div>
            <div class="Projectprogress-count-img" :style="getcolor(ite)">
                <img v-lazy="ite.PlanPhoto" class="cursor" :key="i" alt="" srcset="" style="width:100%;height:100%" @click="pic(ite.PlanPhoto)">
                <div class="pointleft" :style="getcolor(ite)"></div>
                <div v-for="(item,index) in ange" :key="index" :class="item"  :style="getcolor(ite)"></div>
            </div>
            <div class="Projectprogress-count-content">
                    <p :style="`color:${bgcolor.font}`">{{ite.ActualEndDate}}</p>
                    <p :style="`color:${bgcolor.font}`" >{{ite.PlanName}}</p>
                    <p>负责人：{{ite.ConstructionWorker}}&nbsp 质检员：{{ite.QualityWorker}} &nbsp 安全员：{{ite.SecurityWorker}}</p>
                    <img v-for="(imgs,a) in imglsit" :key="a" v-show="ite.CompletionState==imgs.name" :src="imgs.src" alt="">
            </div>
            <div class="state"></div>
        </div>
        </div>
        
    </el-scrollbar>
    <picimg ref="picimg"></picimg>
  </div>
</template>

<script>
import { ref ,onMounted, nextTick} from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";

export default {
components:{
    picimg
    },
setup(){
    let bgcolor=ref({})
    let imglsit=ref([
        {
            name:'正常完成',
            src:require('@/assets/img/technology/001.png')
        },
        {
            name:'提前完成',
            src:require('@/assets/img/technology/002.png')
        },{
            name:'延期完成',
            src:require('@/assets/img/technology/003.png')
        },{
            name:'进行中',
            src:require('@/assets/img/technology/progress.png')
        },
    ])
    let getform=ref({
        ProjectCode:store.getters.code,
    })
    let myScrollbar=ref(null)
    let conScrollbar=ref(null)
    let picimg=ref(null)
    let progresslist=ref([
    ])
    let ange=['rightbefore','rightafter','leftbefore','leftafter']
     window.addEventListener('setthcolor', ()=> {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    let windownsHeight=ref(window.innerHeight)
    let scroles=ref('760px')
    onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getpicpress()
    })
    window.addEventListener('resize', function(event) {
    var windowHeights = window.innerHeight;
    if (windowHeights>windownsHeight.value) {
        scroles.value='860px'
    }else{
        scroles.value='760px'

      
    }
    })
    const getcolor=(val)=>{
        // console.log('正常',val,bgcolor.chamfer);
    //   bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))E0AE1D
        
        return val.CompletionState=='正常完成'?{borderColor:bgcolor.value.chamfer}:
        (val.CompletionState=='提前完成'?{borderColor:'#fff'}:(val.CompletionState=='进行中'?{borderColor:'#E0AE1D'}:{borderColor:'red'}))
    }
    const getbgcolor=(val)=>{
        return val.CompletionState=='正常完成'?{background:bgcolor.value.chamfer}:
        (val.CompletionState=='提前完成'?{background:'#fff'}:(val.CompletionState=='进行中'?{background:'#E0AE1D'}:{background:'red'}))

    }
    const getpicpress=async()=>{
        const {data:res}=await gettable('ProgressVerticalTable',getform.value)
        // console.log('进度',res);
        if (res.code=="1000") {
            progresslist.value=res.data
            scrollToBottom()

        }
        
    }
    const pic=(val)=>{
        picimg.value.piclist(val)
    }
   const scrollToBottom=()=> {
    // nextTick
            // let scrollElem = myScrollbar.value;GetMeetingSummaryTable
			nextTick(() => {
                let scrollElem = myScrollbar.value;

                scrollElem.scrollTo({top:conScrollbar.value.scrollHeight,behavior: 'smooth'});
          })
		　　}
    return{
        getform,
        bgcolor,
        myScrollbar,
        conScrollbar,
        picimg,
        windownsHeight,
        ange,
        progresslist,
        imglsit,
        scroles,
        getcolor,
        getbgcolor,
        getpicpress,
        pic
    }
}
}
</script>
<style lang="scss" scoped>
.Projectprogress{
    height: 93%;
    padding: 20px;
    width: 100%;
    // .el-scrollbar{
    //     height: 80%!important;
    // }
    &-count{
        display: grid;
        grid-template-columns: 10% 20% 70%;
        align-items: center;
        justify-items: center;
        margin-bottom: 5px;
        // height: 150px;
    &-round{
        display: flex;
        height: 100%;
        // justify-content: center;
        flex-direction: column;
        align-items: center;
        .round{
            width: 30px;
            height: 30px;
            border-radius: 100%;
            background: #03FBFF;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        }
        .line{
            height: 60px;
            border-left: 5px dotted #03FBFF;
            width: 5px;
        }
    }
    &-img{
        position: relative;
        width: 100%;
        height: 140px;
        border: 1px solid ;
        background: rgba(15, 43, 63, 0.6)!important;
        .pointleft{
            position: absolute;
            width: 15px;
            height: 15px;
            left: -9px;
            top: 45%;
            clip-path: polygon(0 0, 100% 0, 0 100%);
            background: rgba(15, 43, 63, 0.6);
            border-left: 1px solid #000;
            border-top: 1px solid #000;
            transform: rotate(-45deg);
        }
    }
    &-content{
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        height: 100%;
        position: relative;
        padding-left: 5%;
        p{
            padding: 10px;
            text-align: start;
        }
        p:nth-child(1),p:nth-child(2){
            font-weight: bold;
            font-size: 18px;
        }
        img{
            position: absolute;
            right: 5%;
            // top: 5%;
        }

    }
    }
    .myScrollbar{
        height: 100%;
    }
}
</style>