<template>
  <!-- 出勤 -->
  <div class="Projectschedule padding"  :style="{color:bgcolor.font}">
    <div :class="['Projectschedule-top','lefticon']"
	:style="{background:'linear-gradient(90deg, '+bgcolor.titlecolor+' 0%, rgba(1, 194, 255, 0) 97%)'}"
	>
        <img  src="@/assets/img/technology/Projectschedule.png"  >
        <span class="padding-text-span" >项目进度情况</span>
    </div>
    <div class="Projectschedule-two">
      <div class="Projectschedule-two-one">
        <div  v-for="(item,index) in label" :key="index">
            <p>{{item.name}}:</p>
            <p :style="index==0?'color:#03FBFF':'color:red'">{{toplable[item.value]}}</p>
        </div>
      </div>
      <div class="echatr">
        <schedulechart :ids="'schedulechart'" :options1="option0"></schedulechart>
        <schedulechart :ids="'schedulechart1'" :options1="option1"></schedulechart>
        <schedulechart :ids="'schedulechart2'" :options1="option2"></schedulechart>
      </div>
    </div>
	<div :class="'leftbefore'" :style="{borderColor:bgcolor.chamfer}"></div>
  <div :class="'leftafter'" :style="{borderColor:bgcolor.chamfer}"></div>
  </div>
</template>

<script>
// import echartsProjectschedule from "@/components/connet/Common/echartscom.vue";
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import schedulechart from "@/components/connet/Common/echartscom.vue";
export default {
props:['homeindex','materialtype'],
components:{
// echartsProjectschedule
// attecahrt,
schedulechart
},
setup(props){

  let bgcolor=ref({})
  let option=ref({})
  let option0=ref({})
  let option1=ref({})
  let option2=ref({})
  let label=[{
    name:'开工日期',
    value:'StartDate'
    },{
        name:'竣工日期',
        value:'CompleteDate'
    }]
  let toplable=ref({})
  let getform=ref({
      ProjectCode:store.getters.code,

    })

  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        gettopleft()

  })
  const gettopleft=async()=>{
    const {data:res}=await gettable('GetProgressSpeed',getform.value)
    if (res.code=="1000") {
      toplable.value=res.data[0]
      firstecaht(res.data[0])
      second(res.data[0])
      thesechart(res.data[0])
    }
      
  }
  const firstecaht=(val)=>{
    // console.log('获取',(val.CurrentProgress/val.TotalDuration).toFixed(2)*100);
    
    let GUID={
        name:'当前进度',
        value:(val.CurrentProgress/val.TotalDuration).toFixed(2),
        values:val.CurrentProgress

    }
    bieechart(GUID,'0')
  }
  const second=async(val)=>{
    // const {data:res}=await gettable('GetProgressSpeed',getform.value)

    let GUID={
        name:'剩余工期',
        value:(val.RemainDuration/val.TotalDuration).toFixed(2),
        values:val.RemainDuration

    }
    bieechart(GUID,'1')
  }
  const thesechart=async(val)=>{
    let GUID={
        name:'总工期',
        value:'1',
        values:val.TotalDuration
    }
    bieechart(GUID,'2')
  }
  const bieechart=(val,index)=>{
        // console.log('生成',index,`${option}${index}`.value);
        
        option.value = {
            series: [
                {
                type: 'gauge',
                startAngle:210,
                endAngle: -30,
                center: ['50%', '50%'],
                radius: '95%',
                min: 0,
                max: 1,
                splitNumber: 15,
                axisLine: {
                    lineStyle: {
                    width: 0,
                    color: [
                        [val.value,'#0BA29A'],
                        [1, '#ccc'],
                    ]
                    }
                },
                pointer: {
                    icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                    length: '12%',
                    width:0,
                    offsetCenter: [0, '-60%'],
                    itemStyle: {
                    color: 'auto'
                    }
                },
                axisTick: {
                    length: 6,
                    splitNumber:3,
                    lineStyle: {
                    color: 'auto',
                    width: 2,
                    cap:'round'
                    }
                },
                splitLine: {
                    length: 10,
                    distance: 10,
                    lineStyle: {
                    color: 'auto',
                    width: 3,
                    cap:'round'
                    }
                },
                axisLabel: {
                    color: '#07B1CB',
                    fontSize: 15,
                    show:false,
                    distance: -60,
                    rotate: 'tangential',
                },
                title: {
                        offsetCenter: [0, '90%'],
                        fontSize: 14,
                        color:'#fff'
                    },
                detail: {
                    fontSize: 16,
                    offsetCenter: [0, '-7%'],
                    valueAnimation: true,
                    formatter: function (values) {
                        // console.log('获取',values);
                        
                    return val.values + '天';
                    },
                    color: 'inherit'
                },
                data: [
                    val
                ]
                },
            ]
        };
        // console.log('获取',option.value);
        
        if (index==0) {
        option0.value= option.value
            
        }else if (index==1){
        // option1.value= JSON.parse(JSON.stringify(option.value))
        option1.value= option.value

        }else if (index==2){
        // option2.value= JSON.parse(JSON.stringify(option.value))
        option2.value= option.value

        }
        // console.log('获取',option[index]);
        
    }
 

	return{
        option,
    option0,
    option1,
    option2,
		getform,
		bgcolor,
    toplable,
    label,
    bieechart,
    firstecaht,
    second,
    thesechart,
    gettopleft

	}
}
}
</script>

<style lang="scss" scoped>
.Projectschedule{
&-two{
  height: 80%;
    &-one{
        display: grid;
        grid-template-columns: repeat(2,50%);
        justify-items: start;
        font-size: 14px;
        p{
            display: inline-block;
            padding: 5px;
        }
    }
}
}

.echatr{
//   display: flex;
//   align-items: center;
  width: 100%;
  height: 85%;
  display: grid;
  grid-template-columns: repeat(3,33%);
}

</style>