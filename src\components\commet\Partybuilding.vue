// 党建
<template>
  <div class="partbuild">
    <div class="partbuild-one">
        <img class="imgleft" src="@/assets/img/party/002.png" alt="" style="width:150px;height:100px">
        <p class="partbuild-one-p">{{PROJECTNAME}}党支部</p>
        <img class="imgright" src="@/assets/img/party/002.png" alt="" style="width:150px;height:100px">
    </div>
    <div class="partbuild-two two" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]">
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
            <div class="datedelog-body-one">
                <img src="@/assets/img/home/<USER>" alt="">
                <p class="datedelog-p">党建动态</p>
            </div>
        </div>
        <el-scrollbar class="Notice">
            <div class="partbuild-content" v-for="(isx,i) in trends" :key="i" 
            :style="`background: rgba(${bgcolor.delogcolor}, 0.2);`">
                <img :src="isx.Detail.MovingImage" alt="" class="cursor" style="width:200px;height:100px" @click="pic(isx.Detail.MovingImage)">
                <div class="partbuild-content-lables" >
                    <p v-for="(item,index) in lables" :class="['p'+index]"
                     :key="index">{{item.name}} 
                     <span :class="[item.value=='Theme'?'cursor':'']" @click="openhtml(isx.Detail)">{{isx.Detail[item.value]}}</span></p>
                </div>
            </div>
        </el-scrollbar>
    </div>
    <div class="partbuild-three two" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]">
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
            <div class="datedelog-body-one">
                <img src="@/assets/img/home/<USER>" alt="">
                <p class="datedelog-p">公示公告（45）</p>
            </div>
        </div>
        <el-scrollbar class="Notice">
            <div  v-for="(isx,i) in gsgglist" :key="i" :class="['partbuild-contents marpad cursor',i%2==0?'three':'']"
            :style="`background: rgba(${bgcolor.delogcolor}, 0.2);`" @click="openhtml(isx)">
            <span >{{isx.FileName}}</span>
            <span>{{isx.ReleaseDate}}</span>
            </div>
        </el-scrollbar>
    </div>
    <div class="partbuild-four two" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]">
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
            <div class="datedelog-body-one">
                <img src="@/assets/img/home/<USER>" alt="">
                <p class="datedelog-p">党建活动照片（9）</p>
            </div>
        </div>
        <el-scrollbar class="rightscroll">
            <div  v-for="(isx,i) in rightpic" :key="i" :class="['partbuild-contentfour marpad']">
                <img class="cursor" v-lazy="isx.PhotoUrl"  :key="i" alt="" style="width:100%;height:180px" @click="pic(isx.PhotoUrl)">
            </div>
        </el-scrollbar>
    </div>
    <div class="partbuild-five two" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]">
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
            <div class="datedelog-body-one">
                <img src="@/assets/img/home/<USER>" alt="">
                <p class="datedelog-p">党组成员（{{Total}}）</p>
            </div>
        </div>
        <el-scrollbar class="btscrollbar">
            <div class="scrollbar-flex-content">
                <div  v-for="(isx,i) in djform" :key="i" :class="['partbuild-contentfive marpad']">
                    <img class="cursor" :src="isx.Photo" alt=""
                     style="width:100px;height:100%" @click="pic(isx.Photo)"
                    >
                    <p v-for="(item,index) in bot" :key="index" :class="['p'+index]">{{isx[item]}}</p>

                </div>
            </div>
            
        </el-scrollbar>
    </div>
    <el-dialog v-model="dialogTableVisible1" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:#fff`]" 
    class="textdelog" width="80%" >
        <div class="heaereq-one">
            <div class="toplist" >
            <div :class="['heaereq cursor']"
            :style="[`background:rgba(${bgcolor.delogcolor},0.35)`]">
                <div class="icontop1 bor" :style="[`border:1px solid ${bgcolor.titlecolor}`]" ></div>
                <div class="icontop" :style="[`border:1px solid ${bgcolor.titlecolor}`]"></div>
                <div class="icontop2 bor" :style="[`border:1px solid ${bgcolor.titlecolor}`]"></div>
            </div>
            </div>
            <div class="closedelog cursor" :style="[`background: radial-gradient(50% 50% at 50% 50%,
            rgba(3, 251, 255, 0.17) 0%, ${bgcolor.hovercor} 100%);left:${'99%'}`]" @click="close()">
                <el-icon class="closeicon"><CloseBold /></el-icon>
            </div>
        </div>
        <div>

        </div>
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
            <div class="datedelog-body-one">
                <img src="@/assets/img/home/<USER>" alt="">
                <p class="datedelog-p">{{content.FileName?content.FileName:content.Theme}}</p>
            </div>
            <p class="datedelog-p">{{content.ReleaseDate}}</p>
        </div>
        <el-scrollbar height="600px">
            <div class="dialog-content" v-html="content.Contents"></div>
        </el-scrollbar>
    </el-dialog>
    <picimg ref="picimg"></picimg>
  </div>
</template>

<script>
import store from "@/store";
import { ref,onMounted } from 'vue';
import picimg from "@/components/connet/Common/picimg.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";

export default {
components:{
        picimg
    },
setup(){
    let bgcolor=ref({})
    let dialogTableVisible1=ref(false)
    let PROJECTNAME=ref(store.getters.date[0].PROJECTNAME)
    let lables=[{
        name:'党建活动主题：',
        value:'Theme'
    },{
        name:'党建活动日期：',
        value:'IssueDate'
    },{
        name:'党建活动地点：',
        value:'Location'
    },{
        name:'活动负责人：',
        value:'Author'
    },{
        name:'党建参与人数：',
        value:'People'
    }
    ]
    let getform=ref({
            ProjectCode:store.getters.code,
            PartyMemberName:'',
            InUserName:store.getters.username,
            page:1,
            count:1000
        })
    let rightpic=ref([])
    let picimg=ref(null)
    let bot=ref(['PartyMemberName','Position','JoiningPartyTime'])
    let gsgglist=ref([])
    let content=ref({})
    let trends=ref([])
    let djform=ref([])
    let Total=ref(0)
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // getlocation()
        getpic()
        getannouncement()
        GetNewsDynamic()
        getdj()
    })
    
    const getpic=async()=>{
        const {data:res}=await gettable('GetFineProjectTable',getform.value)
        // console.log('获取',res);
        if (res.code=="1000") {
            rightpic.value=res.data
        }
    }
    const pic=(val)=>{
        picimg.value.piclist(val)
    }
    const openhtml=(val)=>{
        content.value=val
        dialogTableVisible1.value=true
    }
    const close=()=>{
        dialogTableVisible1.value=false

    }
    // 获取党建
    const getdj=async()=>{
        const {data:res}=await gettable('GetPartyMemberTable',getform.value)
            if (res.code=="1000") {
                djform.value=res.data
                Total.value=res.Total
            }
    }
    // 获取公示公告
    const getannouncement=async()=>{

        const {data:res}=await gettable('GetMianAnnouncement',getform.value)
        // console.log('获取公示公告',res);
        if (res.code=="1000") {
            gsgglist.value=res.data
        }
    }
    // 获取党建动态
    const GetNewsDynamic=async()=>{
        const {data:res}=await gettable('GetNewsDynamic',getform.value)
        // console.log('动态党建',res);
        // let
        if (res.code=="1000") {
            trends.value=res.data
            trends.value.map((item,index)=>{
                // console.log('循环',item.DetailInfo[0]);
                
              item.Detail= item.DetailInfo[0]

            })
        }
        // console.log('处理数据',trends.value);
        

    }
    return{
        bgcolor,
        dialogTableVisible1,
        PROJECTNAME,
        lables,
        content,
        bot,
        gsgglist,
        rightpic,
        picimg,
        getform,
        djform,
        Total,
        pic,
        trends,
        openhtml,
        getpic,
        close,
        getannouncement,
        GetNewsDynamic,
        getdj
    }
}
}
</script>
<style lang="scss">
.btscrollbar{
    height: 73%!important;
    .el-scrollbar__view{
        height: 95%!important;
    }
}
.Notice{
    height: 85%!important;

}
.rightscroll{
    height: 90%!important;
}
.textdelog{
    margin-top: 50px!important;
    opacity: 1;
    box-sizing: border-box!important;
    .el-dialog__header{
        display: none!important;
    }
    .el-dialog__body{
        padding: 10px!important;
        word-break: break-all!important;
        white-space:pre-wrap;
    }
}
</style>
<style lang="scss" scoped>

.partbuild{
    width: 100%;
    height: 100%;
    background-image: url('@/assets/img/party/001.png') ;
    background-repeat: no-repeat;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-size: 100%;
    display: grid;
    grid-template-columns:50% 33% 17%;
    grid-template-rows: 20% 50% minmax(0, 1fr);
    // align-items: end;
    &-one{
        grid-column: 3 span;
        height: 100%;
        display: grid;
        grid-template-columns: 7% 86% 7%;
        align-items: end;
        justify-items: center;
        position: relative;
        // margin-bottom: 10px;
        &-p{
            font-size: 40px;
            color: #000;
            font-weight: bold;
        }
        img,p{
            position: absolute;
        }
        .imgleft{
            left: 0%;
            top: 2%;
        }
        .imgright{
            right: 0%;
            top: 2%;
        }
    }
    .two{
        margin: 10px;
        padding: 10px;
    }
    .datedelog-header{
        margin: 10px 0px;
    }
    &-content{
        display: grid;
        align-items: center;
        grid-template-columns: 23% 77%;
        margin: 5px;
        font-size: 14px;

        img{
            margin: 10px;
        }
        &-lables{
            display: grid;
            grid-template-columns: repeat(2,50%);
            justify-items: start;
            color: #000;
            p{
            padding: 10px;
                
            }
            .p0{
                grid-row: 1;
                grid-column: 1/2 span;
            }
        }
    }
    .three{
        background: rgba(15, 43, 63, 0.6)!important;
    }
    &-contents{
        // padding: 5px;
        // margin: 5px;
        display: grid;
        font-size: 14px;
        grid-template-columns: 80% 20%;
        span:nth-child(1){
            text-align: start;
            margin-left: 10px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
    &-three{
        color: #000;
    }
    .marpad{
        padding: 5px;
        margin: 5px;
    }
    &-four{
        grid-row: 2/3 span;
        grid-column: 3;
        color: #000;
    }
    &-five{
        grid-row: 3;
        grid-column: 1/2 span;
        color: #000;

        // height: 260px;
    }
    &-contentfive{
        width: 300px;
        height: 100%;
        flex-shrink: 0;
        display: grid;
        grid-template-columns: 40% 60%;
        align-items: center;
        justify-items: start;
        img{
            margin: 10px;
            grid-row: 1/3 span;
            grid-column: 1;
        }
        .p0{
            font-size: 18px!important;
            font-weight: bold;
        }
        
    }
}
.scrollbar-flex-content {
  display: flex;
  height: 100%;
}
.dialog-content{
    // line-height: 2px;
    text-align: left;
}
// .homewqmit{
    // :deep().el-dialog__body{
    //     word-break: break-all!important;
    // }
// }
</style>