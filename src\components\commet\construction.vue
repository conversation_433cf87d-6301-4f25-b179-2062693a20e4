<template>
<!-- 绿色施工 -->
  <div class="model" >
    <div class="leftmodel left wid" v-show="amplify==0">
        <Dustdetection  class="Homebgco" 
        :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" ></Dustdetection>
        <organization  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :teamtype="weathers"></organization>
        <foundation  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" ></foundation>
    </div>
    <div class="homecontent" :style="amplify==0?'width:60%':'width:100%'">
      <home  @getamplify1="getamplify2"></home>
    </div>
    <div class="rightmodel right wid" v-show="amplify==0">
      <Largevolume class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        :bigcew="bigcew"
        ></Largevolume>
      <electricity class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        :electricity1="electricity"
        ></electricity>
      <electricity class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        :electricity1="Smartwater"
        ></electricity>
    </div>

  </div>
</template>

<script>

// import Projectschedule from "@/components/connet/technology/Projectschedule.vue";
import organization from "@/components/connet/technology/organization.vue";
import Largevolume from "@/components/connet/technology/Largevolume.vue";
// import ConstructionLog from "@/components/connet/technology/ConstructionLog.vue";

import Dustdetection from "@/components/connet/construction/Dustdetection.vue";
import foundation from "@/components/connet/construction/foundation.vue";
import electricity from "@/components/connet/construction/electricity.vue";

import home from "@/components/connet/construction/content/home.vue";

import { onMounted, ref ,computed} from 'vue';

export default {
components:{

        // Projectschedule,
        Largevolume,
        organization,
        // ConstructionLog,

        Dustdetection,
        foundation,
        electricity,
        home,

    },
setup(){
    let themelist=ref([])

    let bgcolor=ref({})
    let amplify=ref(0)
    let weathers=ref({
      src:require('@/assets/img/construction/extreme.png'),
        titles:'极端天气应急通知',
        type:'weathersechart'
    })



    let bigcew=ref({
        src:require('@/assets/img/construction/temporary.png'),
        titles:'临时管理',
        type:'disclosure'
    })
    let electricity=ref({
        src:require('@/assets/img/construction/electricity.png'),
        titles:'智慧用电',
        type:'electricity',
        ids:'electricity'
    })
    let Smartwater=ref({
        src:require('@/assets/img/construction/Smartwater.png'),
        titles:'智慧用水',
        type:'Smartwater',
        ids:'Smartwater'
    })

    
    // let Typeworlist=ref({
    //     src:require('@/assets/img/material/Materialtype.png'),
    //     titles:'材料种类验收分析',
    //     type:'物料'
    // })
    window.addEventListener('setItem', ()=> {
      // console.log('项目引导');
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
   })
   window.addEventListener('setthcolor', ()=> {
      // console.log('主题切换');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(()=>{
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    

    })
    const getamplify2=(val)=>{
      // console.log('放大',val);
      
      amplify.value=val
    }
    return{
        electricity,
        bgcolor,
        themelist,
        Smartwater,
        amplify,
        bigcew,
        // bigcew1,
        weathers,
        // Typeworlist,
        getamplify2
    }
}
}
</script>
<style lang="scss" scoped>
@mixin homeflex($deg) {
  height: 31.3%;
  width: 95%;
  opacity: 1;
  margin:7px 10px;
  background: linear-gradient($deg, #0096C7 7%, rgba(0,52,75,0.00) 97%);
}
@mixin leftdjx($left,$right,$top,$bottom,) {
  position: absolute;
  content: '';
  display: block;
  left: $left;
  top: $top;
  right: $right;
  bottom: $bottom;
  width: 10.06px;
  height: 10.84px;
  opacity: 1;
}

.Homebgco{
  position: relative;
  @include homeflex(90deg)
  }
.Homeright{
  position: relative;
  @include homeflex(-90deg);

}
</style>
<style lang="scss" scoped>
.model{
  width: 100%;
  height: 100%;
  display: flex;
  .wid{
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .homecontent{
    width: 60%;
    height: 100%;
    // position: relative;
  }
}
</style>