<template>
<!-- 节材与材料资源利用 -->
  <div>
    <div class="rightbox" v-if="brelist.includes(titles)">
        <div class="rightbox-top">
            <span >{{titles}}</span>
            <el-radio-group v-model="radio" @change="radios">
                <el-radio value="按材料名称">按材料名称</el-radio>
                <el-radio value="按材料名称及规格">按材料名称及规格</el-radio>
            </el-radio-group>
        </div>
        <div id="bieechats" class="bieechats"></div>
        <el-scrollbar height="580px" class="Collarring">
            <div v-for="(item,index) in bredata.MaterialProcureTable" :key="index"
             :style="getwab(item)"  class="righttable">
                <span v-for="(its,i) in forms" :key="i" v-show="its.type==radio"  :class="['cursor ',getstyle(i)]"
                 :title="item[its.value]+(item[its.value1]??'')">{{its.name}}{{item[its.value]}}{{item[its.value2]}}{{item[its.value1]}}</span>
            </div>
        </el-scrollbar>
    </div>
    <el-scrollbar  v-else-if="barchart.includes(titles)" height="600px" :class="`Collarring ${ getclass()}`">
        <div v-for="(item,index) in bredata" :key="index" class="Collarring-one">
            <div class="box" v-if="titles=='主要建筑材料损耗率'">
                <span v-for="(ims,i) in loss" :key="i" :class="`p${i}`">{{ims.name}}{{item[ims.value]}}</span>
                <div class="percentage">
                    <div class="percentage-box">
                        <div class="percentage-one" :style="`width:${item.MaterialUsingRate}%;`"></div>
                        <div class="percentage-two" :style="`width:${100 - item.MaterialUsingRate}%;`"></div>
                        <p v-for="(ps,inde) in plist" :key="inde" :class="['potion-text',`tops${inde}`]"
                        :style="getcolor(ps,inde,item)"
                        >{{`${ps.name+item[ps.value]}`}}%</p>
                    </div>
                </div>
                <span class="bottom">预算使用量：{{item.PlanUsing}}{{item.MaterialUnit}}、实际用量：{{item.RealUsing}}{{item.MaterialUnit}}</span>
            </div>
            <div v-else-if="toptype.includes(titles)" class="waterbox padding">
                <div class="waterbox-one">
                    <img :src="item.EquipPhoto" alt="" class="cursor" style="width:100%;height:140px" @click="preview(item.EquipPhoto)">
                    <p v-for="(items,i) in reuselist"  :key="i">{{`${items.name+item[items.value]}`}}</p>
                </div>
            </div>
            <div class="reduces" v-else :style="`background:${colors[index]}`">
                <p v-for="(items,index) in reuselist" :key="index">{{`${items.name+item[items.value]}`}}</p>
            </div>
        </div>
    </el-scrollbar>
    <div v-else-if="somelist.includes(titles)" class="allbox">
        <div id="pres"></div>
        <div id="bres"></div>
        <el-scrollbar height="300px" class="listpie reuse padding">
            <div v-for="(item,index) in bredata?.LedgerDetail" class="allbox-one padding" :key="index" 
            :style="`background:${colors[index]}`">
            <p v-for="(ites,i) in reuselist" :key="i">{{ites.name+item[ites.value]}}</p>
            </div>
        </el-scrollbar>
    </div>
    <div v-else-if="certificate.includes(titles)" class="twobar">
        <div class="threed">
            <workecharts :ids="'treeth'" :options="optionss" ></workecharts>
            <div class="text">
                <h3 class="h3">证书统计分析</h3>
                <p v-for="(item,index) in optionss" :key="index">{{item.name}}<span :style="`color:${item.itemStyle.color}`">{{item.value}}</span></p>
            </div>
        </div>
        <div id="bres" class="bres"></div>
        <tablelist ref="tablelists" class="listpie"></tablelist>
    </div>
    <div v-else class="boxtwo">
        <h3>劳动力计划与台账</h3>
        <div class="boxsect padding">
            <el-select v-model="getform.Types" popper-class="pagination" placeholder="请选择材料"
             size="small" style="width:100px"  @change="changetable">
                <el-option v-for="(item,index) in optionss" :key="index" :label="item.PhaseName" 
                 :value="item.PhaseName"/>
            </el-select>
            <span v-for="(item,index) in $labelist('劳动力计划表顶部')" class="p" :key="index">{{item.name+bredata[item.value]??''+bredata[item.value1]??''}}</span>
        </div>
        <div class="boxid" id="barid"></div>
        <el-scrollbar height="400px" class="boxtwo-scro wateruser">
            <div v-for="(item,index) in bredata.PlanLedgerTable" :key="index"
             :style="`background:${$colorlist()[index]}`"  class="righttable">
                <span v-for="(its,i) in $labelist('劳动力计划表')" :key="i"  :class="['cursor ']"
                 >{{its.name+(item[its.value]??'')}}</span>
            </div>
        </el-scrollbar>
    </div>
    <picimg ref="picimg"></picimg>
  </div>
</template>
<script>
import { nextTick, ref,getCurrentInstance, onBeforeUnmount } from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";
import echarts from "./echarts.vue";
import tablelist from "./tablelist";
import workecharts from "@/components/connet/personnelcon/echarts3d/workerkq.vue";
export default {
components:{
    picimg,
    echarts,
    tablelist,
    workecharts
},
setup(){
  const $colorlist = getCurrentInstance().appContext.config.globalProperties.$colorlist
  const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist
  const $http = getCurrentInstance().appContext.config.globalProperties.$http

        let titles=ref('')
        let radio=ref('按材料名称')
        let myChart=null
        let getform=ref({
            ProjectCode:store.getters.code,
            InUserName:store.getters.username,
            Types:'按材料名称',
            Type:''
        })
        let bredata=ref([])
        let rightlist=ref([])
        let values=ref('')
        let forms=ref([
            {
                name:'',
                value:'MaterialName',
                value1:'',
                type:''

            },{
                name:'规格型号：',
                value:'MaterialModel',
                type:''
            },{
                name:'',
                value:'TransportationNum',
                type:'',
                value1:'MaterialUnit',
                value2:'EntryNum'

            },{
                name:'厂家名称：',
                value:'Manufacturer',
                type:''

            },{
                name:'平均运输距离：',
                value:'Distance',
                type:''

            },
        ])
        let url='GetMaterialProcureCharts'
        let brelist=['主要材料采购记录表','材料、机具进出场台账']
        let barchart=['主要建筑材料损耗率','非实体工程材料重复利用','节水设备使用统计表','设备总体耗能计划',
        '节能设备使用统计表'
        ]
        let loss=[{
            name:'',
            value:'MaterialName'
        },{
            name:'规格型号：',
            value:'MaterialModel'
        },{
            name:'计量单位：',
            value:'MaterialUnit'
        },
        ]
        let plist=[
            {
            name:'材料损耗率',
            value:'MaterialUsingRate'
            },{
            name:'定额损耗率',
            value:'RatedLoss'
            },
        ]
        let colors=ref([])
        let reuselist=ref([])
        let source=ref(null)
        let picimg=ref(null)
        let toptype=['节水设备使用统计表','设备总体耗能计划','节能设备使用统计表']
        let falge=ref(false)
        let somelist=['现场临时用房、硬化、植草砖铺装等各临建建设面积台账']
        let certificate=['特种作业人员登记表']
        let tablelists=ref(false)
        let optionss=ref([])
        // let addform=ref({})
        let showdelog=(val)=>{
            // console.log('弹窗',val,$labelist(val.Type));
            // falge.value=false
            titles.value=val.Type
            if (source.value) {
                source.value.cancel("取消")
            }
            getform.value.Type=val.Type
            colors.value=$colorlist()

            switch (val.Type) {
                case '主要材料采购记录表':
                    url='GetMaterialProcureCharts'
                    break;
                case '材料、机具进出场台账':
                    url='GetMaterialIELedgerCharts'
                    break;
                case '主要建筑材料损耗率':
                    url='GetMaterialQuotaCharts'
                    break;
                case '非实体工程材料重复利用':
                    // colors.value=$colorlist()
                    reuselist.value=$labelist(val.Type)
                    url='GetJCMaterialReuseTable'
                    break;
                case '节水设备使用统计表':
                    reuselist.value=$labelist(val.Type)
                    url='GetSaveWaterEquipTable'
                    break;
                case '设备总体耗能计划':
                    reuselist.value=$labelist(val.Type)
                    url='GetEquipPowerPlanTable'
                    break;
                case '节能设备及太阳能、风能、空气能设备配备情况登记表':
                    titles.value='节能设备使用统计表'
                    getform.value.Type='节能设备使用统计表'
                    reuselist.value=$labelist(val.Type)
                    url='GetSaveEnergyEquipTable'
                    break;
                case '现场临时用房、硬化、植草砖铺装等各临建建设面积台账':
                    reuselist.value=$labelist(val.Type)
                    url='GetTemporaryAreaLedgerCharts'
                    break;
                case '特种作业人员登记表':
                    url='GetSpecialOperationCharts'
                    nextTick(()=>{
                    tablelists.value.showdelog(val)
                    })
                    break;
                case '劳动力计划表、劳动力使用台账':
                    url='GetPlanLedgerDetailCharts'
                    getform.value.Types='桩基阶段'
                    // console.log('获取劳动计划表');
                    getcldata()
                    break;
            }
            const CancelToken = $http.CancelToken;
    	    source.value = CancelToken.source();
            getechartsdata()
        }
        const getechartsdata=async()=>{
            const {data:res}=await gettable(url,getform.value,source.value)
                // console.log('获取列表',res);
                   bredata.value=res.data
                if (res.code=="1000") {
                   if (brelist.includes(titles.value)) {
                    forms.value.forEach(item => item.type = radio.value);

                    // 特殊情况处理
                    if (titles.value === '材料、机具进出场台账') {
                        forms.value[4].type = '';
                    }
                    nextTick(()=>{
                        showecharts()
                    })
                   }else if (somelist.includes(titles.value)) {
                    nextTick(()=>{
                        someecharts('pres',res.data.LedgerCharts,{
                            show:false,
                        })
                        someecharts('bres',res.data.LedgerCharts,{
                            show:true,
                            text:'使用面积（m³）'
                        })
                    })
                   }else if (certificate.includes(titles.value)) {
                        optionss.value=res.data.CertificateNameList
                        someecharts('bres',res.data.CertificateTypeList,{
                            show:true,
                            text:'证书类型统计分析'
                        })
                    }else if(titles.value=='劳动力计划表、劳动力使用台账'){
                        someecharts('barid',res.data.LedgereCharts,{
                            show:false,
                        })
                    }
                }
        }
        const changetable=(val)=>{
            // console.log('选择材料',val);
            getform.value.Types=val
            getechartsdata()
        }
        // 获取材料下拉框
        const getcldata=async()=>{
            const {data:res}=await gettable('GetPlanLedgerDetailDrop',getform.value)
            // console.log('获取材料下拉框',res);
            optionss.value=res.data
        }
        // 获取echarts图表数据
        const someecharts=(ids,val,type)=>{
            // console.log('获取图表数据显示',ids,val);
            var echarts = require('echarts');
            let myChart1 = echarts.getInstanceByDom(document.getElementById(ids));
            if (myChart1 == null) {
                myChart1 = echarts.init(document.getElementById(ids));
            }
            let pie={}
            // 柱状图
            if (type.show) {
                pie={
                    data: val.map((item,index)=>{
                        return item.value
                    }),
                    type: 'bar',
                    
                    itemStyle: {
                        borderRadius: [20, 20, 0, 0],
                        width:'10%',
                        color:function(params) {
                            return $colorlist()[params.dataIndex]
                        }
                    },
                }
            }else{
                pie={
                    name: '数据来源',
                    type: 'pie',
                    radius: '90%',
                    color:$colorlist(),
                    label: {
                        position: 'inner',
                        rotate: 'radial'
                    },
                    data: val,
                }
            }
            let option={
                title: {
                    show:type.show,
                    text: type.text,
                    textStyle: {
                        fontSize: 13,
                        // position
                    },
                },
                tooltip: {
                    trigger: 'item'
                },
                grid: {
                    left: '5%',
                    right: '5%',
                    bottom: '2%',
                    top: '10%',
                    containLabel: true
                },
                xAxis: {
                    show:type.show,
                    type: 'category',
                    axisLabel: {
                        interval: 0,
                        formatter:function(val){
                        let names=val.split("").join("\n")
                            return names.slice(0, 2) + '...'
                        },
                        "textStyle": {
                            "color": "#000"
                            }
                    },
                    data: val?.map((item)=>{
                        return item.name
                    })
                },
                yAxis: {
                    show:type.show,
                    type: 'value'
                },
                series: pie
            }
            myChart1.setOption(option)
            window.addEventListener("resize", function() {
            myChart1.resize();
            });
        }
        const preview=(val)=>{
            let imgtype=['jpg','png','Jpeg','jpeg','JPG']
                if (val) {
                    let lastIndex= val.lastIndexOf('.')
                    let file=val.substring(lastIndex+1)
                    if (imgtype.includes(file)) {
                        picimg.value.piclist(val)
                    }else{
                    window.open('https://f.zqface.com/?fileurl='+val,'_slef')
                    }
                }
        }
        const getclass=()=>{
            if (titles.value=='非实体工程材料重复利用') {
                return 'reuse'
            }else if (toptype.includes(titles.value)) {
                return 'wateruser'
            }
            return ''
        }
        const getwab=(val)=>{
            let columns=radio.value=='按材料名称'?'30% 70%':'80% 20%'
            return `background:${val.color};grid-template-columns: ${columns};`
        }
        const getstyle=(val)=>{
            // 根据 radio.value 设置不同的样式
            switch (radio.value) {
                case '按材料名称':
                    forms.value[0].value1 = '';
                    return `Materialname mater${val}`;
                case '按材料名称及规格':
                    forms.value[0].value1 = 'MaterialModel';
                    return `Material maters${val}`;
                default:
                    return '';
            }
        }
        // 获取文字的像素宽度 - Canvas方法
        const getTextWidth = (text, font = '14px Arial') => {
            const canvas = document.createElement('canvas')
            const context = canvas.getContext('2d')
            context.font = font
            return context.measureText(text).width
        }

        // 获取文字的像素宽度 - DOM方法（更精确）
        const getTextWidthByDOM = (text, className = 'potion-text') => {
            const span = document.createElement('span')
            span.style.visibility = 'hidden'
            span.style.position = 'absolute'
            span.style.whiteSpace = 'nowrap'
            span.style.fontSize = '14px'
            span.style.fontWeight = 'bold'
            span.className = className
            span.textContent = text
            document.body.appendChild(span)
            const width = span.offsetWidth
            document.body.removeChild(span)
            return width
        }

        // 计算顶部位置 - 根据新的布局调整位置
        const getcolor=(val,i,value)=>{
            let lefts=''
            if (i=='0') {
                // 第一个指示器位置与percentage-one对齐（右侧元素）
                lefts = value.MaterialUsingRate
            }else{
                // 第二个指示器位置与percentage-two对齐（左侧元素）
                
                lefts =value.RatedLoss
            }


            return `right:${lefts}%; `
        }
        const showecharts=()=>{
            myChart?.dispose()
            var echarts = require('echarts');
            myChart = echarts.getInstanceByDom(document.getElementById('bieechats'));
            if (myChart == null) {
                myChart = echarts.init(document.getElementById('bieechats'));
            }

            const data=bredata.value.ChartsByModelList
            const total = calculateTotal(data);
            // console.log('总数',total);
            
            let option = {
                tooltip: {
                    trigger: "item",
                },
                series: {
                    type: 'sunburst',
                    data: data,
                    radius: [0, '90%'],
                    label:{
                        // show: true,
                        // showThreshold: 1000
                    }
                }
                };
                myChart?.setOption(option)

                window.addEventListener("resize", function() {
                myChart?.resize();
                });
        }
        const calculateTotal=(data)=>{
            let total = 0;

            function traverse(node) {
                if (node.value !== undefined) {
                    total += node.value;
                }
                if (node.children && Array.isArray(node.children)) {
                    node.children.forEach(traverse);
                }
            }

            data.forEach(traverse);
            return total;
        }
        const getThreshold=(total, depth)=>{
            const depthThresholds = [0.05, 0.02, 0.01]; // 不同层级的阈值比例
            return total * (depthThresholds[depth] || 0.01);
        }
        const radios=(val)=>{
            radio.value=val
            getform.value.Types=val
            getechartsdata()
        }
        onBeforeUnmount(()=>{
            myChart?.dispose()
        })
        return{
            titles,
            radio,
            myChart,
            getform,
            bredata,
            rightlist,
            forms,
            values,
            url,
            // falge,
            brelist,
            barchart,
            loss,
            plist,
            colors,
            reuselist,
            source,
            picimg,
            toptype,
            somelist,
            certificate,
            tablelists,
            optionss,
            // addform,


            showdelog,
            radios,
            showecharts,
            getechartsdata,
            getstyle,
            getwab,
            getcolor,
            getclass,
            preview,
            someecharts,
            getcldata,
            changetable,
            calculateTotal,
            getThreshold,
            getTextWidth,
            getTextWidthByDOM
        }
    }

}
</script>
<style lang="scss">
.Collarring{
    .el-scrollbar__view{
    flex-direction: column;
    }
}
.reuse{
    .el-scrollbar__view{
        display: grid!important;
        grid-template-columns: repeat(4,25%);
    }
}
.wateruser{
    .el-scrollbar__view{
        display: grid!important;
        grid-template-columns: repeat(2,50%);
        // justify-items: center;
    }
}
</style>
<style lang="scss" scoped>
@import "./echartsbre.scss";
</style>
