<template>
  <!-- 偏差分析 -->
  <div class="deviation padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms" 
    @opens="opentable()" ></Chamfering>

    <div class="deviation-two">
      <div class="deviation-two-one">
        <attecahrt  v-if="echartcount" :ids="'ghsechart'" :options="options1" :distances="320"></attecahrt>
      </div>
      <div class="deviation-two-table">
        <el-table :data="gridData" class="pice cursor" :style="['width: 100%',`color:${bgcolor.font};
        --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
        :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}" 
        empty-text="暂无数据" :loading="loading" max-height="200px" @row-dblclick="opendetil"
        >
            <el-table-column  prop="rowNum" align="center" label="排名"  width="40"></el-table-column>
            <el-table-column  prop="SupplierName" align="center"  label="供货商" width="60" ></el-table-column>
            <el-table-column  prop="DeviationNum" align="center"  label="偏差次数" width="70"></el-table-column>
        </el-table>
      </div>
      
    </div>
    <tablelist ref="tablelist" v-if="falge"></tablelist>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    <delog ref="delogs"></delog>
  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import attecahrt from "@/components/connet/personnelcon/echarts3d/attecahrt.vue";
import tablelist from "@/components/connet/material/content/home.vue";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/material/content/delog.vue";

import store from "@/store";
export default {
components:{
    attecahrt,
    tablelist,
    Chamfering,
    delog
},
setup(){

  let bgcolor=ref({})
  let loading=ref(false)
  let falge=ref(false)
  let getform=ref({
      ProjectCode:store.getters.code,

    })
  let gridData=ref([])
  let options1=ref([])
  let echartcount=ref(true)
  let tablelist=ref(null)
  let topforms=ref({
      url:require('@/assets/img/material/deviation.png'),
      name:"供货商偏差分析",
      text:'更多记录',
      lefs:'lefs'
  })
  let delogs=ref(null)
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getrighttable()
        // nextTick(()=>{
        //     getecharts()
        // })
  })

  const getecharts=(val)=>{
        echartcount.value=false
        nextTick(()=>{
        echartcount.value=true

        options1.value=[
        {
            name: '超负差',
            value: val.DeviationList[0].value,
            itemStyle: {
                // opacity: 0.5,
                color: '#74f3b5',
            }
        },

        {
            name: '正常',
            value: val.DeviationList[1].value,
            itemStyle: {
                // opacity: 0.5,
                color: '#00b7f7',
            }
        },
    ]
        })
        
    }
  const opentable=()=>{
    delogs.value.showdelog('','供货商偏差分析')
  }
  const getrighttable=async()=>{
      loading.value=true
      const {data:res}=await gettable('GetSupplierDeviation',getform.value)
      loading.value=false
      if (res.code=="1000") {
        gridData.value=res.data.SupplierList
        nextTick(()=>{
            getecharts(res.data)
        })
      }
  }
  const opendetil=async(val)=>{
    // console.log('详情',val);
    // falge.value=true
    // nextTick(()=>{
    // tablelist.value.showdelog('供货商偏差统计',val)

    // })
    delogs.value.showdelog(null,'供货商偏差分析')

    
    // if (val.SupplierName==) {供货商偏差统计
      
    // }
  }
  const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }

	return{
    falge,
    echartcount,
    tablelist,
    loading,
		getform,
		bgcolor,
    options1,
    topforms,
    delogs,

    gridData,


    getecharts,
    tableRowClassName,
    getrighttable,
    opendetil,
    opentable
	// getdeviation,
	}
}
}
</script>
<style lang="scss">
.deviation{
  .el-table{
    --el-table-row-hover-bg-color:rgba(1, 194, 255, 0.6);
  }
  .deviation-two-table{
    .pice{
    font-size: 12px;
    }
    .el-table .cell{
      padding:  0 4px!important;
    }

  }
  .el-table .warning-row {
    background: rgba(15, 43, 63, 0.6)!important;
  }
}

</style>
<style lang="scss" scoped>
.deviation{
&-two{
  height: 86%;
  padding: 10px;
  display: grid;
  grid-template-columns: 45% 55%;
  align-items: center;
  &-one{
    width: 100%;
    height: 100%;
  }
  &-table{
    width: 100%;
    height: 100%;
    padding: 5px;
  }
  #ghsechart{
    width: 100%;
    height: 100%;
  }
}
}



</style>