<template>
    <el-dialog :title="title" append-to-body v-model="dialogVisibles" width="60%" class="zckq" @close="closeDialog">
        <div class="map-container">
            <!-- 添加地址搜索框 -->
            <div class="search-box">
                <el-input
                    v-model="searchAddress"
                    placeholder="请输入地址"
                    class="search-input"
                    clearable
                    :disabled="isSearching"
                    @keyup.enter="searchByAddress"
                    @clear="clearSearch">
                    <template #append>
                        <el-button
                            type="primary"
                            icon="el-icon-search"
                            :loading="isSearching"
                            @click="searchByAddress">
                            {{ isSearching ? '搜索中' : '搜索' }}
                        </el-button>
                    </template>
                </el-input>
                <p v-if="searchError" class="search-error">{{ searchError }}</p>
            </div>
            
            <div id="baiduMap" class="map"></div>
            <div class="coordinate-info">
                <p>当前坐标：{{ currentLng }}, {{ currentLat }}</p>
                <p>当前地址：<span v-if="isAddressLoading">获取中...</span><span v-else>{{ currentAddress || '未获取到地址' }}</span></p>
                <!-- <el-button type="primary" @click="confirmCoordinate">确认选择</el-button> -->
            </div>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        dialogTitle: {
            type: String,
            default: '选择位置'
        },
        defaultCenter: {
            type: Object,
            default: () => ({
                lng: 120.21551,
                lat: 30.264811
            })
        },
        defaultZoom: {
            type: Number,
            default: 15
        }
    },
    data() {
        return {
            dialogVisibles: this.visible,
            title: this.dialogTitle,
            map: null,
            marker: null,
            currentLng: '',
            currentLat: '',
            currentAddress: '', // 当前坐标对应的地址
            searchAddress: '', // 搜索地址
            searchError: '',   // 搜索错误信息
            geocoder: null,     // 地址解析对象
            isAddressLoading: false, // 地址加载状态
            isSearching: false  // 搜索按钮加载状态
        }
    },
    watch: {
    },
    mounted() {
        if (this.dialogVisibles) {
            this.$nextTick(() => {
                this.initMap();
            });
        }
    },
    methods: {
        showdelog(val,adress){
            this.searchAddress=adress
            // console.log(val,adress);
            
            this.dialogVisibles = true;
            this.title='定位点'
            this.$nextTick(() => {
                this.initMap();
                if (adress) {
                    setTimeout(() => {
                        this.searchByAddress()
                    }, 500)
                // this.searchByAddress()
                    
                }
            });
        },
        initMap() {
            // 确保地图容器已经渲染
            this.$nextTick(() => {
                // 创建地图实例
                this.map = new BMapGL.Map("baiduMap");
                
                // 创建点坐标
                const point = new BMapGL.Point(this.defaultCenter.lng, this.defaultCenter.lat);
                
                // 初始化地图，设置中心点坐标和地图级别
                this.map.centerAndZoom(point, this.defaultZoom);
                
                // 开启鼠标滚轮缩放
                this.map.enableScrollWheelZoom(true);
                
                // 添加地图控件
                this.map.addControl(new BMapGL.ScaleControl());
                this.map.addControl(new BMapGL.ZoomControl());
                this.map.addControl(new BMapGL.LocationControl());
                
                // 设置当前坐标值
                this.currentLng = this.defaultCenter.lng;
                this.currentLat = this.defaultCenter.lat;
                
                // 添加标记
                this.addMarker(point);
                
                // 点击地图事件
                this.map.addEventListener("click", (e) => {
                    // 兼容不同版本的百度地图API
                    const point = e.latlng || e.point;
                    this.currentLng = point.lng;
                    this.currentLat = point.lat;
                    
                    // 更新标记位置
                    this.updateMarker(point);
                });
                
                // 初始化地址解析对象
                this.geocoder = new BMapGL.Geocoder();
                
                // 获取初始位置的地址
                this.getAddressByPoint(point);
            });
        },
        
        // 添加标记
        addMarker(point) {
            if (this.marker) {
                this.map.removeOverlay(this.marker);
            }
            
            this.marker = new BMapGL.Marker(point);
            this.map.addOverlay(this.marker);
        },
        
        // 更新标记位置
        updateMarker(point) {
            if (this.marker) {
                this.map.removeOverlay(this.marker);
            }
            
            const newPoint = new BMapGL.Point(point.lng, point.lat);
            this.marker = new BMapGL.Marker(newPoint);
            this.map.addOverlay(this.marker);
            
            // 使用逆地理编码获取地址
            this.getAddressByPoint(newPoint);
        },
        
        // 根据坐标点获取地址
        getAddressByPoint(point) {
            // 显示加载状态
            this.isAddressLoading = true;
            this.currentAddress = '';
            
            // 使用逆地理编码服务获取地址
            this.geocoder.getLocation(point, (result) => {
                this.isAddressLoading = false;
                if (result) {
                    // 获取详细地址信息
                    this.currentAddress = result.address;
                } else {
                    // 获取地址失败
                    this.currentAddress = '';
                }
            });
        },
        
        // 根据地址搜索位置
        searchByAddress() {
            // 清除之前的错误信息
            this.searchError = '';

            // 检查地址是否为空
            if (!this.searchAddress.trim()) {
                this.searchError = '请输入搜索地址';
                return;
            }

            // 开始搜索，显示加载状态
            this.isSearching = true;

            // 使用地址解析服务查询地址
            this.geocoder.getPoint(this.searchAddress, (point) => {
                // 搜索完成，隐藏加载状态
                this.isSearching = false;

                if (point) {
                    // 搜索成功，更新地图位置和标记
                    this.currentLng = point.lng;
                    this.currentLat = point.lat;

                    // 将地图中心设置为搜索结果位置
                    this.map.centerAndZoom(point, 18);

                    // 更新标记
                    this.updateMarker(point);

                    // 搜索成功后，使用搜索的地址作为当前地址
                    this.currentAddress = this.searchAddress;

                    // 搜索成功提示
                    this.$message.success('地址搜索成功');
                } else {
                    // 搜索失败
                    this.searchError = '未找到该地址，请尝试更精确的地址';
                    this.$message.error('地址搜索失败，请检查地址是否正确');
                }
            }, '全国');
        },

        // 清除搜索内容
        clearSearch() {
            this.searchAddress = '';
            this.searchError = '';
        },
        
        // 确认选择坐标
        confirmCoordinate() {
            this.$emit('maps', {
                lng: this.currentLng,
                lat: this.currentLat,
                address: this.currentAddress || this.searchAddress // 优先使用逆地理编码获取的地址，如果没有则使用搜索地址
            });
            this.closeDialog();
        },
        
        // 关闭对话框
        closeDialog() {
            this.dialogVisibles = false;
            this.$emit('update:visible', false);
            this.$emit('close');
        }
    }
}
</script>

<style lang="scss" scoped>
.map-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.search-box {
    margin-bottom: 15px;

    .search-input {
        width: 100%;

        // 移动端适配：占位符文本为黑色
        :deep(.el-input__inner) {
            &::placeholder {
                color: #000000 !important;
                opacity: 0.6;
            }
        }

        // 搜索按钮样式优化
        :deep(.el-input-group__append) {
            .el-button {
                border-radius: 0 4px 4px 0;
                font-weight: 500;
                min-width: 80px;

                &.is-loading {
                    .el-icon-loading {
                        margin-right: 5px;
                    }
                }

                // 移动端触摸优化
                @media (max-width: 768px) {
                    min-height: 44px;
                    font-size: 14px;
                }
            }
        }

        // 移动端输入框优化
        @media (max-width: 768px) {
            :deep(.el-input__inner) {
                font-size: 16px; // 防止iOS缩放
                height: 44px;
                line-height: 44px;
            }
        }
    }

    .search-error {
        color: #F56C6C;
        font-size: 12px;
        margin-top: 5px;
        margin-bottom: 0;
        padding-left: 5px;
        animation: shake 0.3s ease-in-out;
    }
}

// 错误提示动画
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.map {
    width: 100%;
    height: 450px;
    margin-bottom: 15px;
}

.coordinate-info {
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    p {
        margin-bottom: 10px;
        word-break: break-all;
    }
    
    button {
        align-self: flex-end;
    }
}
</style>
