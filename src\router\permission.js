import router from "./index"
import store from "../store"
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
// console.log('导入路由',router);

// 不需要登录即可访问的白名单页面
const whiteList = ['/login', '/large-screen', '/three-demo', '/404','/phone','/ycphone']

// 配置NProgress选项
NProgress.configure({ 
  easing: 'ease',  // 动画方式
  speed: 500,      // 递增进度条的速度
  showSpinner: true, // 显示加载图标
  trickleSpeed: 200, // 自动递增间隔
  minimum: 0.3      // 初始化时的最小百分比
})

// 创建一个全局状态来控制加载动画
export const loadingState = {
  isLoading: false
}

//  获取角色信息，根据用户权限动态加载路由
router.beforeEach((to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 同时使用之前的加载动画（如果需要）
  // store.dispatch('loading/setLoading', true)
  
  // 判断该路由是否在白名单中
  if (whiteList.includes(to.path)) {
    // 在白名单中，直接放行
    next()
    return
  }
  
  // console.log('路由权限',store.getters);
  // NProgress.start()
  if (store.getters.username) {
    // console.log('获取登录');
    if (to.path === "/login") {
      // console.log('获取登录');
      
      next()
    } else {
      if (to.matched.length===0) {
        next({path: "/404"})
        return false
      }
      next()
    }
    // next()
    
  } else {
    // 不在白名单中且未登录，重定向到登录页
    console.log('没有登录');
    next({path: "/login"})
  }
})

router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})

export default router
