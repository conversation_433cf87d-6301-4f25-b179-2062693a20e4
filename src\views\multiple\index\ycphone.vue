<template>
  <div class="ycphones">
    <div class="ycphone" v-if="falge=='0'">
      <div class="search-container">
        <el-input
          v-model="getform.name"
          class="search-input"
          placeholder="请输入项目名称或杨尘编号"
          clearable :prefix-icon="Search"
          size="large"
        />
      </div>
      <el-scrollbar class="content-scrollbar">
          <div class="box">
            <!-- 搜索加载状态 -->
            <div v-if="isSearching" class="loading-state">
              <div class="loading-spinner">
                <div class="spinner"></div>
              </div>
              <p>正在搜索中...</p>
            </div>

            <!-- 搜索结果显示 -->
            <div v-else-if="filteredList.length > 0" class="result-list">
              <div  v-for="(item, index) in filteredList"
                :key="index"  class="result-item" :style="getstyle(item)">
                <div class="item-header" v-for="(its,i) in lables" :key="i" >
                  <span class="item-label">{{ its.name }}:</span>
                  <span class="mn-code" :style="getmn(its,item[its.value])">{{ gettext(its,item[its.value])}}</span>
                  <el-button v-if="its.name=='项目名称'" type="primary" @click="showdelogs(item,its.value)" link>定位</el-button>
                </div>
              </div>
            </div>

            <!-- 无搜索结果时显示 -->
            <div v-else-if="hasSearched && getform.name.trim() !== ''" class="no-result">
              <p>未找到匹配的项目</p>
              <p class="tip">请检查输入的项目名称或杨尘编号是否正确</p>
            </div>
            <div v-else class="empty-state">
              <p>请输入项目名称或杨尘编号进行搜索</p>
            </div>
          </div>
      </el-scrollbar>
    </div>
    <maploaction v-else ref="maploactions" @back="back"></maploaction>
  </div>
  <!-- maploaction -->
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import maploaction from "./delog/maploaction";
// import { ElMessage } from 'element-plus';
const $http = getCurrentInstance().appContext.config.globalProperties.$http
const $moist = getCurrentInstance().appContext.config.globalProperties.$moist
import { Calendar, Search } from '@element-plus/icons-vue'

// 获取路由参数
const route = useRoute()

let getform=ref({
    key:'hzzq9806',
    name:'',

})
let list=ref([])
let filteredList=ref([])
let hasSearched=ref(false) // 标记是否已经进行过搜索
let isSearching=ref(false) // 搜索加载状态
let shouldShowDefaults=ref(false) // 是否应该显示默认数据
let lables=[
    {
    name:'项目名称',
    value:'projectName'
    },
    {
    name:'地区',
    value:'districtName'
    },
    {
    name:'项目地址',
    value:'projectAddress'
    },
    {
    name:'扬尘厂家',
    value:'operationCompanyName'
    },
    {
    name:'扬尘编号',
    value:'mnCode'
    },
    {
    name:'设备状态',
    value:'devicePoleStatus'
    },{
    name:'在线情况',
    value:'status'
    },
    {
    name:'最新数值',
    value:'dust'
    },
    {
    name:'最后更新时间',
    value:'uploadTime'
    }
]
let falge=ref(0) // 用于标记是否已经获取过数据
let Defaults=['HZCN0HZ0010022','HZCN0HZ0010032','HBJH0HZ0100085','CSKS0HZ2205010','HZCN0HZ0010035','HZCN0HZ0010037']
let maploactions=ref(null)

onMounted(()=>{
    // 尝试多种方式获取参数
    let typeParam = null

    // 方法1: 使用Vue Router的query参数
    // if (route.query.type) {
    //     typeParam = route.query.type
    //     console.log('从Vue Router获取type参数:', typeParam)
    // }

    // 方法2: 如果Vue Router没有获取到，则手动解析URL
    if (!typeParam) {
        let url = window.location.href?.split('?')
        // console.log('URL分割结果:', url)
        if (url && url.length > 1) {
            const params = url[1].split('&')
            // console.log('参数数组:', params)
            for (let param of params) {
                const [key, value] = param.split('=')
                // console.log('解析参数:', key, '=', value)
                if (key === 'type') {
                    typeParam = value
                    break
                }
            }
        }
    }
    if (typeParam === '1') {
        shouldShowDefaults.value = true
        // console.log('设置shouldShowDefaults为true')
    }

    gettable()
})

const gettable=async()=>{
    const {data:res}=await $http.post('/aiot/dust/api.ashx?PostType=get&Type=HZDUST',getform.value)
        // console.log('API响应:', res);
        if (res.code=='1000') {
            // 过滤掉设备状态为"拆除"的数据
            list.value = res.data.Data.filter(item =>
                item.devicePoleStatus !== '拆除'
            )
            // console.log('过滤后的数据:', list.value);
            
            // 如果URL参数type=1，则显示默认数据
            if (shouldShowDefaults.value) {
                filteredList.value = list.value.filter(item =>
                    Defaults.includes(item.mnCode)
                )
                hasSearched.value = true // 标记为已搜索状态，显示结果
            }
        }
}

// 判断输入是否为设备编号格式（英文字母+数字组合）
const isDeviceCode = (value) => {
    // 支持多种格式：纯数字、字母+数字、字母数字混合
    const trimmedValue = value.trim()
    return /^[A-Za-z0-9]+$/.test(trimmedValue) &&
           (trimmedValue.length >= 3) &&
           (/[A-Za-z]/.test(trimmedValue) || /^\d+$/.test(trimmedValue))
}
const gettext=(val,value)=>{
    // val.name=='设备状态'
    if (val.name=='在线情况') {
        if (value=='1') {
            return '在线'
        }else{
            return '离线'
        }
    }
    return value
}
const getstyle=(val)=>{
    if (val.status=='1') {
        return 'border-color:#15C917'
    }else{
        return 'border-color:red'
    }
}
const back=()=>{
    falge.value = '0' // 切换回列表界面
    // nextTick(()=>{
    //     maploactions.value.clearMap() // 清除地图上的标记
    // })
}


const getmn=(val,value)=>{
    if (val.name=='在线情况'|| val.name=='设备状态') {
        if (value=='1'||value=='正常') {
            return 'color:#15C917'
        }else{
          if (value=='拆除') {
            return 'color:#FF6C37'
          }
            return 'color:red'
        }
    }
    return ''
}

// 搜索防抖定时器
let searchTimer = null

// 执行搜索的函数
const performSearch = (searchValue) => {
    if (isDeviceCode(searchValue)) {
        // 如果输入的是杨尘编号格式（如HZCN0HZ0010035），从mnCode字段匹配
        // 同时过滤掉设备状态为"拆除"的数据
        filteredList.value = list.value.filter(item =>
            item.mnCode &&
            item.mnCode.toString().toLowerCase().includes(searchValue.toLowerCase()) &&
            item.devicePoleStatus !== '拆除'
        )
    } else {
        // 按项目名称搜索，同时过滤掉设备状态为"拆除"的数据
        filteredList.value = list.value.filter(item =>
            item.projectName &&
            item.projectName.toLowerCase().includes(searchValue.toLowerCase()) &&
            item.devicePoleStatus !== '拆除'
        )
    }

    // 搜索完成，关闭加载状态
    isSearching.value = false
}
const showdelogs=(item,its)=>{
    // console.log('定位项目:', item, its);
    // $moist.showdelog(item,its)
    falge.value = '1' // 切换到地图定位界面
    nextTick(()=>{
        maploactions.value.showdelog(item,its)
    })
    // maploactions.value.showdelog(item,its)
}

// 监听搜索输入变化
watch(() => getform.value.name, (newValue) => {
    // 清除之前的定时器
    if (searchTimer) {
        clearTimeout(searchTimer)
    }

    if (!newValue || newValue.trim() === '') {
        // 如果输入为空，根据是否应该显示默认数据来决定状态
        if (shouldShowDefaults.value) {
            // 如果应该显示默认数据，则显示默认过滤结果，同时过滤掉设备状态为"拆除"的数据
            filteredList.value = list.value.filter(item =>
                Defaults.includes(item.mnCode) && item.devicePoleStatus !== '拆除'
            )
            hasSearched.value = true
        } else {
            // 否则重置为空状态
            filteredList.value = []
            hasSearched.value = false
        }
        isSearching.value = false
        return
    }

    const searchValue = newValue.trim()
    hasSearched.value = true // 标记已经开始搜索
    isSearching.value = true // 显示加载状态

    // 设置防抖延迟，500ms后执行搜索
    searchTimer = setTimeout(() => {
        performSearch(searchValue)
    }, 500)
}, { immediate: true })

</script>
<style scoped>
.ycphones {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.ycphone {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
  background-color: #f5f5f5;
}

.search-container {
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #dcdfe6;
}

.search-input :deep(.el-input__inner) {
  font-size: 16px;
  padding: 12px 16px;
}

.search-input :deep(.el-input__inner::placeholder) {
  color: #000000 !important;
  opacity: 1;
}

.content-scrollbar {
  flex: 1;
  height: calc(100vh - 80px);
  overflow: hidden;
}

.content-scrollbar :deep(.el-scrollbar__view) {
  padding: 16px;
}

.box {
  width: 100%;
  min-height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

/* 搜索结果样式 */
.result-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.result-item:hover {
  background-color: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
}

.item-header {
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
  margin-bottom: 8px;
  color: #000000;
}


.mn-code {
  background-color: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
}
.loading-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}
.item-label{
    display: inline-block;
    width: 130px;
    text-align: start;
}
.loading-spinner {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  margin: 8px 0;
  font-size: 14px;
  color: #666;
}

/* 无结果状态 */
.no-result, .empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-result p, .empty-state p {
  margin: 8px 0;
}

.tip {
  font-size: 12px;
  color: #999;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .ycphone {
    height: 100vh;
    overflow: hidden;
  }

  .search-container {
    padding: 10px 12px;
  }

  .search-input :deep(.el-input__inner) {
    font-size: 16px; /* 防止iOS缩放 */
  }

  .content-scrollbar {
    height: calc(100vh - 70px);
  }

  .content-scrollbar :deep(.el-scrollbar__view) {
    padding: 12px;
  }

  .box {
    padding: 12px;
    border-radius: 6px;
  }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
  .search-container {
    padding: 8px 10px;
  }

  .content-scrollbar {
    height: calc(100vh - 65px);
  }

  .content-scrollbar :deep(.el-scrollbar__view) {
    padding: 10px;
  }

  .box {
    padding: 10px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .search-input :deep(.el-input__wrapper) {
    min-height: 44px; /* 符合触摸目标最小尺寸 */
  }

  .search-input :deep(.el-input__inner) {
    min-height: 44px;
    line-height: 44px;
  }
}
</style>