"use strict";(self["webpackChunksmart"]=self["webpackChunksmart"]||[]).push([[578],{9578:function(e,a,l){l.d(a,{Z:function(){return f}});var t=l(73396),i=l(87139);const n={class:"row1"},s={class:"quilet"},u=["src","onClick"];function o(e,a,l,o,r,c){const d=(0,t.up)("selection"),m=(0,t.up)("el-empty"),v=(0,t.up)("el-table-column"),p=(0,t.up)("el-button"),g=(0,t.up)("el-table"),h=(0,t.up)("el-pagination"),w=(0,t.up)("el-step"),b=(0,t.up)("el-steps"),f=(0,t.up)("picimg"),y=(0,t.up)("el-dialog"),C=(0,t.Q2)("loading");return(0,t.wg)(),(0,t.j4)(y,{class:"delogss",modelValue:o.dialogVisible,"onUpdate:modelValue":a[2]||(a[2]=e=>o.dialogVisible=e),title:"",width:"1"==o.dialogVisible1?"50%":"70%","before-close":o.handleClose},{default:(0,t.w5)((()=>[(0,t.Wm)(d,{ref:"selections",onColses:o.closes,titles:o.titles},null,8,["onColses","titles"]),"0"==o.dialogVisible1?((0,t.wg)(),(0,t.iD)("div",{key:0,class:"bodybottom",style:(0,i.j5)(o.getstyle())},[(0,t.Wm)(g,{data:o.tableDate,style:(0,i.j5)([`width: 100%;color:${o.bgcolor.font};\n             --el-table-border-color:${o.bgcolor.titlecolor}`]),"row-class-name":o.tableRowClassName,"header-cell-style":{background:"rgba(15, 43, 63, 0.6)",color:"#fff"},"empty-text":"暂无数据","max-height":"500px"},{empty:(0,t.w5)((()=>[(0,t.wy)((0,t.Wm)(m,null,null,512),[[C,o.loading]])])),default:(0,t.w5)((()=>[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(o.lables,((e,a)=>((0,t.wg)(),(0,t.j4)(v,{key:a,width:e.widths,prop:e.value,label:e.name,align:"center"},null,8,["width","prop","label"])))),128)),(0,t.Wm)(v,{prop:"SnapImage",label:"操作",align:"center"},{default:(0,t.w5)((e=>[(0,t.Wm)(p,{type:"primary",link:"",onClick:a=>o.getdetil(e.row)},{default:(0,t.w5)((()=>[(0,t.Uk)("查看")])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data","style","row-class-name","header-cell-style"]),(0,t.Wm)(h,{"current-page":o.getform.page,"onUpdate:currentPage":a[0]||(a[0]=e=>o.getform.page=e),"page-size":o.getform.count,"onUpdate:pageSize":a[1]||(a[1]=e=>o.getform.count=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:o.Totles,onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])],4)):((0,t.wg)(),(0,t.iD)("div",{key:1,style:(0,i.j5)(o.getstyle()),class:"bodybottom"},[(0,t.Wm)(b,{active:o.stepindex,"align-center":"","finish-status":"success"},{default:(0,t.w5)((()=>[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(o.steps,((e,a)=>((0,t.wg)(),(0,t.j4)(w,{title:e.title,key:a},{description:(0,t.w5)((()=>[(0,t._)("div",n,[(0,t._)("span",null,(0,i.zw)(e.description),1),(0,t._)("span",null,(0,i.zw)(e.time),1)])])),_:2},1032,["title"])))),128))])),_:1},8,["active"]),(0,t._)("div",s,[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(o.formlabes,((e,a)=>((0,t.wg)(),(0,t.iD)("div",{key:a,style:(0,i.j5)(o.getwidths(e.name)),class:"lefs"},[(0,t._)("span",{class:(0,i.C_)([0==e.type?"lefttips":"","text"]),style:(0,i.j5)(o.widths.includes(e.name)?"margin-left:5%":"")},(0,i.zw)(e.name)+(0,i.zw)(o.addfrom[e.value]),7),1==e.type?((0,t.wg)(),(0,t.iD)("img",{key:0,class:"cursor",src:o.addfrom[e.value1],alt:"",onClick:a=>o.pic(o.addfrom[e.value1]),style:{width:"80px",height:"80px"}},null,8,u)):(0,t.kq)("",!0)],4)))),128))])],4)),(0,t.Wm)(f,{ref:"picimg"},null,512)])),_:1},8,["modelValue","width","before-close"])}l(57658);var r=l(44870),c=l(24239),d=l(57597),m=l(36331);function v(e){let a=[],l=[{name:"质量巡检",value:[{name:"序号",value:"rowNum",widths:"",index:0},{name:"检查日期",value:"CheckDate",widths:"",index:6},{name:"检查人",value:"CheckName",widths:"",index:7},{name:"地点",value:"QualityMatterPlace",widths:"",index:3},{name:"问题类型",value:"QualityPatType",widths:"",index:4},{name:"问题描述",value:"MatterDescribe",widths:"",index:5},{name:"问题状态",value:"ReformStatic",widths:""},{name:"整改时间",value:"OperateDate",widths:""},{name:"整改人",value:"Operator",widths:""}]},{name:"质量巡检详情",value:[{name:"质检信息",type:0,value:""},{name:"质检员：",value:"CheckName"},{name:"质检时间：",value:"CheckDate"},{name:"所属项目：",value:"ProjectName"},{name:"问题类别：",value:"QualityPatType",value1:"SafeCheckType"},{name:"问题地点：",value:"QualityMatterPlace",value1:"SafeCheckPlace"},{name:"紧急程度：",value:"DegreeUrgency"},{name:"问题描述：",value:"MatterDescribe"},{name:"问题照片：",value1:"CheckPhoto",type:1},{name:"整改情况",type:0,value:""},{name:"整改人：",value:"Operator"},{name:"整改时间：",value:"OperateDate"},{name:"整改描述：",value:"OperateDescribe"},{name:"整改照片：",value1:"OperatePhoto",type:1},{name:"复查结果",type:0,value:""},{name:"检查人：",value:"Operator2"},{name:"复查时间：",value:"OperateDate2"},{name:"是否合格：",value:"IsQualified"}]}];return l.forEach(((l,t)=>{l.name==e&&(a=l.value)})),a}var p=l(18089),g=l(15941),h={components:{picimg:m.Z,selection:p.Z},setup(){let e=(0,r.iH)(!1),a=(0,r.iH)({}),l=(0,r.iH)(""),i=(0,r.iH)(!1),n=(0,r.iH)(0),s=(0,r.iH)([]),u=(0,r.iH)({ProjectCode:c.Z.getters.code,InUserName:c.Z.getters.username,Months:"",ReformStatic:"",QualityPatType:"",TYPE:0,page:1,count:10}),o=(0,r.iH)([]),m=(0,r.iH)([]),p=(0,r.iH)(0),h=(0,r.iH)([]),w=(0,r.iH)({}),b=(0,r.iH)(null),f=(0,r.iH)(1),y=(0,r.iH)([]),C=(0,r.iH)("GetQualityDetailInfo"),k=["质检信息","所属项目：","问题类别：","问题描述：","问题照片：","整改情况","整改描述：","整改照片：","复查结果"],D=(0,r.iH)([]),x=(0,r.iH)(0);window.addEventListener("setthcolor",(()=>{a.value=JSON.parse(sessionStorage.getItem("themecolor"))})),(0,t.bv)((()=>{a.value=JSON.parse(sessionStorage.getItem("themecolor"))}));const H=(a,t,i,n)=>{let s=a?.seriesName;h.value=v("质量巡检详情"),l.value=a.name?a.name+a.seriesName+"记录":a,o.value=v("质量巡检"),u.value.ReformStatic=i,u.value.QualityPatType=n,u.value.Months=a?.name??"",u.value.TYPE=t,D.value=[],x.value=0;let r=JSON.parse(JSON.stringify(v("质量巡检")));if(C.value="GetQualityDetailInfo",1==t){C.value="GetSafetyDetailInfo";let e=["隐患类别：","隐患地点："];r.map(((e,a)=>{"问题"==e.name.substring(0,2)&&(e.name="隐患"+e.name.slice(2,e.name.length))})),h.value.map(((a,l)=>{"问题"==a.name.substring(0,2)&&(a.name="隐患"+a.name.slice(2,a.name.length)),e.includes(a.name)&&(a.value=a.value1)})),k.map(((e,a)=>{"问题"==e.substring(0,2)&&(e="隐患"+e.slice(2,e.length))}))}switch(s){case"整改问题":case"整改隐患":o.value=O(r);break;case"新增问题":case"新增隐患":let e=[{name:"状态修改时间",value:"OperateDate",widths:""},{name:"状态修改人",value:"Operator",widths:""}];r.splice(7,2,...e),o.value=r;break;case"待整改问题":case"待整改隐患":let a=[{name:"状态修改时间",value:"OperateDate",widths:"",index:1},{name:"状态修改人",value:"Operator",widths:"",index:2}];r.splice(7,2,...a),r.sort(((e,a)=>e.index-a.index)),o.value=r;break}S(),e.value=!0},S=async()=>{const{data:e}=await(0,d.rT)("GetZLSafeCheckRecordTable",u.value);"1000"==e.code&&(s.value=e.data,n.value=e.Total)},O=e=>{let a=[{name:"复查状态",value:"ReformStatic",widths:""},{name:"复查时间",value:"OperateDate",widths:""},{name:"复查人",value:"Operator",widths:""}],l=[{name:"整改时间",value:"OperateDate",widths:""},{name:"整改人",value:"Operator",widths:""}];return e.splice(6,3,...a),e.splice(1,2,...l),e},T=()=>{if(D.value.length>0){const e=D.value.pop();Q(e)}else e.value=!1},P=e=>{b.value.piclist(e)},_=()=>`border:2px solid ${a.value.titlecolor};\n      background:rgba(${a.value.delogcolor},0.35)`,z=async e=>{j(),x.value++,p.value=1;let a={GUID:e.GUID};const{data:l}=await(0,d.rT)(C.value,a);"1000"==l.code&&(w.value=l.data[0],y.value=l.data[0].steps,y.value.forEach(((e,a)=>{e.id=a+1,1==e.Status&&(f.value=y.value[a].id)})))},N=e=>k.includes(e)?"grid-column: 1/span 2;":"",I=()=>{e.value=!1},j=()=>{D.value.push({titles:l.value,lables:[...o.value],getform:{...u.value},tableDate:[...s.value],Totles:n.value,level:x.value})},Q=(e,a=!1,t=null)=>{p.value=0,l.value=e.titles,o.value=e.lables,u.value=e.getform,s.value=e.tableDate,n.value=e.Totles,x.value=null!==t?t:e.level,a&&(D.value=[]),S()},V=({row:e,rowIndex:a})=>a%2!=0?"warning-row":"",U=e=>{g.log(`${e} 显示多少页`),u.value.count=e,S()},W=e=>{g.log(`选择第几: ${e}`),u.value.page=e,S()};return{dialogVisible:e,titles:l,tablelabe:m,bgcolor:a,loading:i,Totles:n,getform:u,tableDate:s,lables:o,dialogVisible1:p,formlabes:h,addfrom:w,widths:k,picimg:b,stepindex:f,steps:y,url:C,handleClose:I,showdelog:H,handleSizeChange:U,handleCurrentChange:W,tableRowClassName:V,gettables:S,getdetil:z,getwidths:N,pic:P,getretable:O,closes:T,getstyle:_}}},w=l(40089);const b=(0,w.Z)(h,[["render",o],["__scopeId","data-v-9c9603ec"]]);var f=b}}]);