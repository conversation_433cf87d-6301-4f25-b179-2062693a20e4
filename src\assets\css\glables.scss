// @media (max-width: 768px) {
//     /* 在小于等于768的屏px幕上应用的样式 */
//     /* 可以修改元素的大小、位置、字体大小等 */
// }
  
// @media screen and (min-width: 769px) and (max-width: 1024px) {
//     /* 在769px到1024px之间的屏幕上应用的样式 */
//     /* 可以修改元素的大小、位置、字体大小等 */


//     .Equipment-two-left{
//         font-size: 10px!important;
//         img{
//             width: 40px!important;
//             height: 40px!important;
//         }
//     }
// }
  
// @media (min-width: 1025px) and  (max-width: 1281px) {
//     /* 在大于等于1025px的屏幕上应用的样式 */
//     /* 可以修改元素的大小、位置、字体大小等 */
//     .Equipment-two-left{
//         font-size: 10px!important;
//         img{
//             width: 40px!important;
//             height: 40px!important;
//         }
//     }
//     .produce-two{
//         .produce-two-top1{
//             font-size: 9px!important;
    
//         }
//         .echatr{
//             height: 75%!important;
//         }
//     }
//     .quality-two{
//         .quality-two-top1{
//             font-size: 9px!important;

//         }
//         .quality-two-top1 span{
//             width: 32%!important;
//         }
//     }
//     .secure-two{
//         .secure-two-top1{
//             font-size: 9px!important;

//         }
//         .secure-two-top1 span{
//             // width: 32%!important;
//         }
//     }
    
// }
// @media (min-width: 1281) {
//     /* 在大于等于1025px的屏幕上应用的样式 */
//     /* 可以修改元素的大小、位置、字体大小等 */
//     // .Equipment-two-left{
//     //     font-size: 10px;
//     //     img{
//     //         width: 40px!important;
//     //         height: 40px!important;
//     //     }
//     // }
// }

// 全局对话框样式 - 确保 delogss 类的样式生效
.delogss {
    padding: 0px !important;

    .el-dialog__header {
        padding: 0px !important;
    }

    .el-dialog__body {
        padding: 0px !important;
    }

    .el-dialog__footer {
        padding: 0px !important;
    }
}

// 更具体的选择器，确保覆盖Element Plus默认样式
.el-dialog.delogss {
    padding: 0px !important;

    .el-dialog__header,
    .el-dialog__body,
    .el-dialog__footer {
        padding: 0px !important;
        margin: 0px !important;
    }
}
