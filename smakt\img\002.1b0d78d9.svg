﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="291px" height="6px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip47">
      <path d="M 291 1  L 98.82428470588235 1  L 98.82428470588235 1.4540000000000077  L 94.17102352941174 6  L 0 6  L 0 1  L 0 0  L 291 0  L 291 1  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -412 -1371 )">
    <path d="M 291 1  L 98.82428470588235 1  L 98.82428470588235 1.4540000000000077  L 94.17102352941174 6  L 0 6  L 0 1  L 0 0  L 291 0  L 291 1  Z " fill-rule="nonzero" fill="rgba(0, 255, 252, 1)" stroke="none" transform="matrix(1 0 0 1 412 1371 )" class="fill" />
    <path d="M 291 1  L 98.82428470588235 1  L 98.82428470588235 1.4540000000000077  L 94.17102352941174 6  L 0 6  L 0 1  L 0 0  L 291 0  L 291 1  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 412 1371 )" class="stroke" mask="url(#Clip47)" />
  </g>
</svg>