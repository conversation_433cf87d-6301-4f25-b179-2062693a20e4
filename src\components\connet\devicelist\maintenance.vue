<template>
  <!-- 设备维保 -->
  <div class="effect padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" 
    :form="topforms" @opens="opentable()" ></Chamfering>
    <div class="effect-two">
      <div class="echatr">
       <el-table :data="gridData"  :style="['width: 100%',`color:${bgcolor.font};
        --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
        :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
        empty-text="暂无数据" show-overflow-tooltip max-height="200px"
        >
            <el-table-column width="" property="EquipName" label="设备名称" />
            <el-table-column width="" property="OperateDate" label="维保日期" />
            <el-table-column width="" property="MaintainType" label="维保类型" />
            <el-table-column  align="center" prop="" label="维保内容">
                <template #default="scope">
                    <el-button type="primary" link  @click="look(scope.row,btns)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
      </div>
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    <delog ref="delogss"></delog>
  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import workecharts from "@/components/connet/personnelcon/echarts3d/workerkq.vue";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/devicelist/content/delog.vue";
export default {
props:['homeindex'],
components:{
// echartsAttendance
workecharts,
Chamfering,
delog
},
setup(){

  let bgcolor=ref({})
  let falge=ref(0)
  let echart=ref(true)
  let countlist=ref([])
  let loading=ref(false)
  let getform=ref({
      ProjectCode:store.getters.code,

    })
  let gridData=ref([])
  let topforms=ref({
    url:require('@/assets/img/device/effect.png'),
    name:"设备维保",
    text:'更多维保',
    lefs:'lefs'
  })
  // let btnlist=['全部人员','管理人员','建筑工人']
  let addteion=ref([])
  let Attendslist=ref([])
  let delogss=ref(null)
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

      // 检查浏览器是否支持 WEBGL_lose_context 扩展
    // let loseContextExtension = gl.getExtension('WEBGL_lose_context');
    // if (loseContextExtension) {
    //   // 释放当前 WebGL 上下文
    //   loseContextExtension.loseContext();
    // }
    // getAttendance()
	  getsunm()
    // getmangen()
    // getconstruction()
  })
  const opentable=()=>{
    // console.log('维保');
    
    let GUID={
      name:'设备维保记录'
    }
    nextTick(()=>{
      delogss.value.showdelog('','设备维保记录')
    })
  }
  const look=(val,value)=>{
    console.log('查看维保详情',val,value);
    delogss.value.showdelog(val,'设备维保详情')
  }
  const getsunm=async()=>{
    const {data:res}=await gettable('GetNewMaintainByEquip',getform.value)
    // console.log('功效',res);
    gridData.value=res.data
    
  }
 const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }

	return{
		falge,
    echart,
		countlist,
		getform,
		bgcolor,
    loading,
    addteion,
    gridData,
    topforms,
    delogss,

  tableRowClassName,
  opentable,
  look
	}
}
}
</script>
<style lang="scss">
.effect{
.el-table{
    --el-table-tr-bg-color:transparent!important;
    // border:1px solid transparent!important;
    // --el-table-border-color:transparent;
    --el-table-row-hover-bg-color:rgba(1, 194, 255, 0.6);
    --el-table-bg-color:transparent;
}
.el-table .warning-row {
//   --el-table-tr-bg-color: #000 !important;
  background: rgba(15, 43, 63, 0.6)!important;
}

}
</style>
<style lang="scss" scoped>
// :deep(.heighttop){
//     height: 7.1%!important;
// }
.effect{
&-two{
  height: 90%;
  .lables{
    display: flex;
    flex-wrap: wrap;
  }
  
  &-label{
    display: flex;
    align-items: center;
    padding: 5px;
    .label-center{
      width: 10px;
      height: 10px;
      border-radius: 100%;
      border: 1px solid #000;
    }
    .line{
        width: 5px;
        height: 2px;
        background: #000;
      }
  &-one{
    display: flex;
    align-items: center;
    // position: relative;
  }
  }
  &-top{
    height: 27%;
    font-size: 12px;
  }
  &-btn{
    padding: 10px;
    width: 80%;
    position: relative;
    border: 2px solid #03558F;
    // margin: 10px;
  }
  .bgcolorcz{
        background: #F29961;

    }
    .bgcolorsj{
          background: #4582ff;

    }
}
}
.echatr{
    margin-top: 10px;
}

</style>