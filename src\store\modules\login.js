import router from "@/router/index";
const login = {
  state: {
    username:JSON.parse(window.sessionStorage.getItem("username")),
    code:JSON.parse(window.sessionStorage.getItem("code")),
    data:JSON.parse(window.sessionStorage.getItem("data")),
    falgenav:JSON.parse(window.sessionStorage.getItem("falgenav"))
  },
  mutations: {
    setusername: (state, username) => {
        // console.log('获取路由');
        state.username=username
        sessionStorage.setItem("username", JSON.stringify(username))

      },
    setcode:(state, code)=>{
      state.code=code
      sessionStorage.setItem("code", JSON.stringify(code))

    },
    setdata:(state, data)=>{
      state.data=data
      sessionStorage.setItem("data", JSON.stringify(data))

    },
    setfalgenav:(state, data)=>{
      state.falgenav=data
      sessionStorage.setItem("falgenav", JSON.stringify(data))

    },
    removeall:(state, data)=>{
      // console.log('触发',);
      sessionStorage.clear();
      router.push('/login')
    }
    
    
  },
  actions: {
    getusernme:({commit}, username)=>{

      commit('setusername', username)

    },
    getcode:({commit}, username)=>{

      commit('setcode', username)

    },
    getdata:({commit}, username)=>{

      commit('setdata', username)

    },
    getfalgenav:({commit}, username)=>{

      commit('setfalgenav', username)

    },
    removealls:({commit}, username)=>{

      commit('removeall', username)

    },
  }
}

export default login
