<template>
  <div class="lgoimg">
    <img class="bgimg" src="@/assets/img/login/login2.png" alt="">
    <div class="login">
        <img src="@/assets/img/login/tilte.png" alt="">
        <div class="submit">
            <img class="login-two" src="@/assets/img/login/login.png" alt="">
            <p class="submit-p">用户登录</p>

            <div class="submit-two">
                <el-form :model="addForm"  :rules="addformrule" ref="addFormRef" class="">
                    <el-form-item label=""  label-width="0px" class=" " prop="username">
                            <el-input v-model="addForm.username" autocomplete="off"  placeholder="请输入您的账号">
                            <template #prefix>
                               <el-icon><Avatar /></el-icon>
                            </template>
                            </el-input>
                    </el-form-item>
                    <el-form-item label=""  label-width="0px" class=" " prop="password">
                        <el-input v-model="addForm.password" autocomplete="off" type="password"  placeholder="请输入您的密码">
                        <template #prefix>
                            <el-icon><Unlock /></el-icon>
                        </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label=""  label-width="0px" class="yzm" prop="value">
                        <el-input v-model="addForm.value" autocomplete="off"  style="width:60%"  placeholder="请输入验证码"  @keydown.enter.native="login()">
                        <template #prefix>
                            <i class="iconfont icon-anquan"></i>
                        </template>
                        </el-input>
                        <div id="auth_code"></div>
                    </el-form-item>
                </el-form>
                <div class="rememi">
                    <el-checkbox v-model="checked1" label="记住密码" size="large" />
                </div>
                <el-button  class="loginsubmit"  :loading="loading" @click="login">登录</el-button>
                <!-- <div class="loginsubmit cursor" :loading="loading" @click="login">
                    <p>登录</p>
                </div> -->
            </div>
        </div>
    </div>
    
  </div>
</template>

<script>
import { nextTick, onMounted, ref,watchEffect,getCurrentInstance } from 'vue';
import {useRouter} from 'vue-router'
import {useStore} from 'vuex'
import { GVerify } from "./../utils/code";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import { ElMessage } from 'element-plus'
export default {
    setup(props, context){
        const $sseion = getCurrentInstance().appContext.config.globalProperties.$sseion
        const $moist = getCurrentInstance().appContext.config.globalProperties.$moist

        const store = useStore();
        const router=useRouter()
        let loading=ref(false)
        let addForm=ref({
            PlatformType:'智慧工地',
            username:'',
            password:"",
            value:''
        })
        let addformrule=ref({
            username:[{ required: true, message: '账号名称不能为空', trigger: 'blur' }	],
            password:[{ required: true, message: '密码不能为空', trigger: 'blur' }	],
            value:[{ required: true, message: '验证码不能为空', trigger: 'blur' }	],
        })
        let checked1=ref(true)
        let verifyCode=ref('')
        let value=ref('')
        let addFormRef=ref(null)
        onMounted(()=>{
            // console.log('获取',$moist.url);
        
        window.location.href=$moist.url

        verifyCode.value = new GVerify("auth_code");

        })
        watchEffect(()=>{
            if (addForm.value.username) {
                setTimeout(()=>{
              verifyCode.value.refresh();
                },300)
                
            }
            
        })
        const login=async()=>{
            // console.log('点击触发');
            addFormRef.value.validate(async(valid) => {
                if (valid) {
            loading.value=true
            if(verifyCode.value.validate(addForm.value.value)){
            const {data:res}=await gettable('Login',addForm.value)
            loading.value=false

            if (res.code=="1000") {
                store.dispatch('getusernme',addForm.value.username)
                store.dispatch('getcode',res.data[0].PROJECTCODE)
                store.dispatch('getdata',res.data)
                getqxdat()
                getsettype(res.data[0].PROJECTCODE)
                let colorlist={
                    bgcolor:'#0096C7',//背景颜色
                    chamfer:'#03FBFF',//倒角颜色
                    titlecolor:'#01C2FF',//标题背景
                    font:'#03FBFF',//字体颜色
                    navcolor:'0, 48, 70',//导航背景
                    hovercor:'#03FBFF',//鼠标移入
                    delogcolor:'2, 193, 253',//弹窗背景
                    changcolor:'#01C2FF'//弹窗选择背景
                    }
                $sseion('themecolor',JSON.stringify(colorlist))

                nextTick(()=>{
                router.push('/index')

                })
                  
            }else{
              verifyCode.value.refresh();
                ElMessage({
                type: 'error',
                message: res.msg
                });
            }
            }else{
            loading.value=false
                ElMessage({
                    message: '验证码错误',
                    type: 'warning',
                })
              verifyCode.value.refresh();
              return
            }}else{
              verifyCode.value.refresh();

            }
            })
        
        }
        // 获取页面排版
        const getsettype=async(val)=>{
            let GUID={
                ProjectCode:val
            }
            let theme =[]
            // let colorlist={
            //     bgcolor:'#0096C7',//背景颜色
            //     chamfer:'#03FBFF',//倒角颜色
            //     titlecolor:'#01C2FF',//标题背景
            //     font:'#03FBFF',//字体颜色
            //     navcolor:'0, 48, 70',//导航背景
            //     hovercor:'#03FBFF',//鼠标移入
            //     delogcolor:'2, 193, 253'//弹窗背景
            //     }
            const {data:res}=await gettable('CreateAllModule',GUID)
                if (res.code=="1000") {
                  theme=res.data.LModule
                }
                // console.log('打印');
                // $sseion('themecolor',JSON.stringify(colorlist))
                
                // window.sessionStorage.setItem("data", JSON.stringify(res.data));
            nextTick(()=>{
                $sseion('theme',JSON.stringify(theme))

            })

        }
        const getqxdat=async()=>{
            let form={
                UserAccount:addForm.value.username
            }
            const {data:res}=await gettable('LoginFunctionSel',form)

					if(res.code=="1000"){
						const jurisdiction= res.data
					window.sessionStorage.setItem("jurisdiction",JSON.stringify(jurisdiction));
					}
        }
        return{
            // colorlist,
            loading,
            verifyCode,
            addFormRef,
            value,
            addForm,
            addformrule,
            checked1,
            login,
            getsettype
        }
    },
}
</script>
<style lang="scss" scoped>
.lgoimg{
    height: 100%;
    width: 100%;
}
.bgimg{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}
#auth_code{
      width: 100px;
      height: 30px;
    //   margin-left: 20px;
    }
.el-input{
    height: 100%;
}
.login{
    position: fixed;
    width: 50%;
    height: 100%;
    top: 6%;
    transform: translate(50%, 0%);
    .login-two{
        width: 42%;
        margin-top: 10%;
    }
}
.el-form-item{
    background: url('@/assets/img/login/input.png') no-repeat!important;
    background-size: 100%!important;
    height: 43px;
}
:deep(.el-input__wrapper){
    background-color: transparent!important;
    box-shadow:none!important;
    // height: 43px;
    // line-height: 43px;
    color: #fff;
}
:deep(.el-input__inner){
// height: 43px;
color: #fff;
-webkit-text-fill-color: #ededed !important;
    background-color: transparent !important;
    transition: background-color 50000s ease-in-out 0s;
}
.submit{
    position: relative;
    color: #fff;
    &-p{
        position: absolute;
        top: 31%;
        left: 46.5%;
    }
    &-two{
        position: absolute;
        width: 30%;
        top: 38%;
        left: 35%;
    }
}
::v-deep .el-form-item__error{
    color: #FF2C00!important;
}
:deep(.el-input__prefix){
    font-size: 20px!important;
    color: #fff;

}
// .el-input__inner{

// }
:deep(input){
    -webkit-text-fill-color: #09276E !important;
    background-color: transparent !important;
    transition: background-color 50000s ease-in-out 0s;
}
.icon-anquan{
font-size: 20px!important;
    color: #fff;
}

.loginsubmit{
    // width: ;
    border: none;
    // background: url('@/assets/img/login/loginsubmit.png') no-repeat!important;
    // background-size: 100%!important;
    width: 100%;
    height: 38px;
    line-height: 38px;
    background: linear-gradient(90deg, #0E4DAC 0%, #072A82 52%, #0E4CAC 100%);
    color: #fff;
    font-weight: bold;
    box-sizing: border-box;
    border: 1px solid #03FBFF;
}
  :deep(.el-button:focus),
  :deep(.el-button:hover) {
    background: linear-gradient(90deg, #4c7bc2 0%, #0e43ca 52%, #4c84d6 100%)!important;
    width: 100%;
    color: aqua;
  }

:deep(.el-checkbox){
color: #fff!important;

}
.el-checkbox__label{
color: #fff!important;
}
:deep(.yzm){
    // display: flex;
    margin-bottom: 0px!important;
    .el-form-item__content{
        display: flex;
    }
}
.rememi{
    text-align: end;
}
</style>