export function loadBimfaceSDK() {
  return new Promise((resolve, reject) => {
    // 检查是否已加载
    if (window.BimfaceSDKLoader) {
      resolve();
      return;
    }

    // 创建script标签
    const script = document.createElement('script');
    script.src = 'https://static.bimface.com/api/BimfaceSDKLoader/<EMAIL>';
    script.charset = 'utf-8';
    
    script.onload = () => {
      resolve();
    };
    
    script.onerror = () => {
      reject(new Error('BimfaceSDK 加载失败'));
    };

    document.head.appendChild(script);
  });
} 