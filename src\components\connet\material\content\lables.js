export function labelist(params) {
    // 表单
    let lablist=[]
    let formcout=[
        {
         name:'领用记录列表',
         value:[{
            name:'序号',
            value:'rowNum',
            widths:'80',
            },{
            name:'供货商',
            value:'SupplierName',
            widths:'',
            },{
            name:'使用类型',
            value:'UsingType',
            widths:'100',
            },{
            name:'材料种类',
            value:'MaterialClass',
            widths:'100',
            },{
            name:'材料名称',
            value:'MaterialName',
            widths:'',
            },{
            name:'规格型号',
            value:'MaterialModel',
            widths:'',
            },{
            name:'计量单位',
            value:'MaterialUnit',
            widths:'100',
            },{
            name:'总数量',
            value:'Stock',
            widths:'100'
            },{
            name:'剩余库存',
            value:'AlertThreshold',
            widths:'100'
            },{
            name:'仓库预警',
            value:'AlertThresholdStatus',
            widths:'80'
            }
         ]
        },{
         name:'领用记录查询',
         value:[{
               name:'序号',
               value:'rowNum',
               type:1,
               widths:'',
               }
         ]
        },{
        name:'供货商验收统计',
        value:[{
               name:'序号',
               value:'rowNum',
               widths:'60',
               },{
               name:'供货商名称',
               value:'SupplierName',
               widths:'',
               },{
               name:'验收批次',
               value:'ReceiveCount',
               widths:'',
               },{
               name:'计量单位',
               value:'MaterialUnit',
               widths:'',
               },{
               name:'总数(重)量',
               value:'Netweight',
               widths:'',
               }
        ]
        },{
        name:'供货商查询',
        value:[{
              name:'关键字搜索',
              value:'SupplierName',
              type:1,
              widths:'',
              }
        ]},{
        name:'供货商列表',
        value:[{
            name:'序号',
            value:'rowNum',
            widths:'60',
            },{
            name:'日期',
            value:'ReceiveTime',
            widths:'',
            },{
            name:'车牌号码',
            value:'CarNumber',
            widths:'',
            },{
            name:'材料种类',
            value:'MaterialClass',
            widths:'',
            },{
            name:'材料名称',
            value:'MaterialName',
            widths:'',
            },{
            name:'规格型号',
            value:'MaterialModel',
            widths:'',
            },{
            name:'数（重）量',
            value:'Netweight',
            widths:'',
            }
        ]
        },{
        name:'供货商列表查询',
        value:[{
              name:'日期',
              value:'ReceiveTime',
              type:3,
              widths:'150',
              },{
              name:'材料种类',
              value:'MaterialClass',
              type:2,
              widths:'150',
              },{
             name:'关键字搜索',
             value:'SearchStr',
             type:1,
             widths:'150',
             }
        ]},{
        name:'磅单详情',
        value:[{
            name:'称毛时间',
            value:'ReceiveTime',
            type:'0',
            widths:'',
            },{
            name:'称皮时间',
            value:'LeaveTime',
            type:'0',
            widths:'',
            },{
            name:'磅房名称',
            value:'WaagName',
            type:'0',
            widths:'',
            },{
            name:'进场称重人员',
            value:'Weigher1',
            type:'0',
            widths:'',
            },{
            name:'出场称重人员',
            value:'Weigher2',
            type:'0',
            widths:'',
            },{
            name:'收料员',
            value:'Receiver',
            type:'0',
            widths:'',
            },{
            name:'收料类型',
            value:'ReceiveType',
            type:'0',
            widths:'',
            },{
            name:'发料单位',
            value:'SupplierName',
            type:'0',
            widths:'',
            },{
            name:'车牌号码',
            value:'CarNumber',
            type:'0',
            widths:'',
            },{
            name:'仓库',
            value:'Warehouse',
            type:'0',
            widths:'',
            },{
            name:'使用部位',
            value:'UsedPart',
            type:'0',
            widths:'',
            },{
            name:'运单编号',
            value:'WaybillCode',
            value1:'WaybillPhoto',
            type:'0',
            widths:'',
            },{
            name:'材料名称',
            value:'MaterialName',
            type:'0',
            widths:'',
            },{
            name:'规格型号',
            value:'MaterialModel',
            type:'0',
            widths:'',
            },{
            name:'计量单位',
            value:'MaterialUnit',
            type:'0',
            widths:'',
            },{
            name:'毛重',
            value:'GrossWeight',
            type:'0',
            widths:'',
            },{
            name:'皮重',
            value:'TareWeight',
            type:'0',
            widths:'',
            },{
            name:'扣重',
            value:'BuckleWight',
            type:'0',
            widths:'',
            },{
            name:'净重',
            value:'Netweight',
            type:'0',
            widths:'',
            },{
            name:'运单重量',
            value:'WayBillWeight',
            type:'0',
            widths:'',
            },{
            name:'重量偏差',
            value:'WeightDeviation',
            type:'0',
            widths:'',
            },{
            name:'偏差情况',
            value:'Deviation',
            type:'0',
            widths:'',
            },{
            name:'进场照片',
            value:'AppearancePhotos',
            img:['PhotoUrl1','PhotoUrl2'],
            type:'0',
            widths:'',
            },{
            name:'出场照片',
            value:'ApproachPhotos',
            type:'0',
            img:['PhotoUrl1','PhotoUrl2'],
            widths:'',
            }
        ]},{
        name:'移动收料详情',
        value:[{
            name:'收料时间',
            value:'ReceiveTime',
            type:1,
            widths:'',
        },{
            name:'收料员',
            value:'Receiver',
            type:1,
            widths:'',
        },{
            name:'备注',
            value:'Remark',
            type:1,
            widths:'',
        },{
            name:'发料单位',
            value:'SupplierName',
            type:1,
            widths:'',
        },{
            name:'车牌号码',
            value:'CarNumber',
            type:1,
            widths:'',
        },{
            name:'仓库',
            value:'Warehouse',
            type:1,
            widths:'',
        },{
            name:'运单编号',
            value:'WaybillCode',
            value1:'WaybillPhoto',
            type:1,
            widths:'',
        },{
            name:'',
            value:'materialsInfo',
            type:2,
            widths:'',
        },{
            name:'进场照片',
            value:'',
            value1:'ApproachPhotos',
            type:1,
            widths:'',
        },{
            name:'出场照片',
            value:'',
            value1:'AppearancePhotos',
            type:1,
            widths:'',
        },{
            name:'收料员签名',
            value:'',
            value1:'WaybillPhoto',
            type:1,
            widths:'',
        },
        ]
        },{
        name:'移动收料材料',
        value:[{
                name:'材料名称',
                value:'MaterialName',
                type:1,
                widths:'',
            },{
                name:'规格型号',
                value:'MaterialModel',
                type:1,
                widths:'',
            },{
                name:'计量单位',
                value:'MaterialUnit',
                type:1,
                widths:'',
            },{
                name:'运单（重）量',
                value:'Netweight',
                type:1,
                widths:'',
            },{
                name:'实际（重）量',
                value:'WayBillWeight',
                type:1,
                widths:'',
            },{
                name:'确认（重）量',
                value:'ConfirmWeight',
                type:1,
                widths:'',
            }
        ]
        },{
        name:'领用记录查询',
        value:[{
              name:'供应商',
              value:'SupplierName',
              type:2,
              list:[],
              widths:'',
              },{
              name:'材料种类',
              value:'MaterialClass',
              type:2,
              list:[],
              widths:'',
              },{
             name:'偏差情况',
             value:'Deviation',
             list:[
                {
                name:'正常',
                value:'正常',
                },
                {
                name:'负偏差',
                value:'负偏差',
                },{
                name:'超负差',
                value:'超负差',
                },{
                name:'正偏差',
                value:'正偏差',
                },{
                name:'超正差',
                value:'超正差',
                },
                
             ],
             type:2,
             widths:'',
             },{
             name:'关键字搜索',
             value:'SearchStr',
             type:1,
             widths:'',
             }
        ]},{
        name:'领用记录详情',
        value:[{
            name:'序号',
            value:'rowNum',
            widths:'60',
            },{
            name:'日期',
            value:'TakingDate',
            widths:'',
            },{
            name:'计量单位',
            value:'MaterialUnit',
            widths:'',
            },{
            name:'出入类型',
            value:'OutInType',
            widths:'',
            },{
            name:'数量',
            value:'TakingNum',
            widths:'',
            },{
            name:'领用人',
            value:'WorkerName',
            widths:'',
            },{
            name:'剩余库存',
            value:'Stock',
            widths:'',
            },{
            name:'备注',
            value:'Remarks',
            widths:'',
            }
        ]
        },{
        name:'供货商偏差分析',
        value:[{
            name:'序号',
            value:'rowNum',
            widths:'60',
            },{
            name:'供货商',
            value:'SupplierName',
            widths:'',
            },{
            name:'材料种类',
            value:'MaterialClass',
            widths:'',
            },{
            name:'材料名称',
            value:'MaterialName',
            widths:'',
            },{
            name:'过磅次数',
            value:'ReceiveCount',
            widths:'',
            },{
            name:'偏差异常次数',
            value:'DeviationCount',
            widths:'',
            },{
            name:'偏差率',
            value:'DeviationRate',
            widths:'',
            },{
            name:'过磅总重量',
            value:'Netweight',
            widths:'',
            },{
            name:'总偏差',
            value:'WeightDeviation',
            widths:'',
            }
        ]
        },
        
    ]


    formcout.forEach((item,index)=>{
        if (item.name==params) {
            lablist=item.value
            
        }
    })
    
    return lablist
}