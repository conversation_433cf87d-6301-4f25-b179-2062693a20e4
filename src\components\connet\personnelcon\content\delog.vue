<template>
  <el-dialog  v-model="dialogTableVisible"  destroy-on-close
    class="delogss" width="60%" :title="titles" append-to-body :before-close="closes" 
    :close-on-click-modal="modal">
    <selection ref="selection" @colses="closes" :titles="names"></selection>
    <div class="bodybottom" :style="`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`">
      <p v-if="titles=='人员库'" class="counts">历史人员数量较多，请耐心等待!</p>
      <div v-if="falge1==0">
        <el-table  :data="tableData" border class="bs-table cursor" max-height="500px" :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
        empty-text="暂无数据" :loading="loading" @row-click="rows"  :row-class-name="tableRowClassName"
        @cell-mouse-leave="mouseles" @cell-mouse-enter="mouseenter" :style="[`width: 100%;color:${bgcolor.font};
        --el-table-border-color:${bgcolor.titlecolor}`]">
            <template #empty>
                  <el-empty  v-loading="loading"></el-empty>
            </template>
          <el-table-column v-for="(item,index) in lables" :key="index" 
              :label="item.name" align="center" :prop="item.value" :width="item.widths">
            <template #default="scope" >
                <span v-if="typename!='证书'">{{item.value=='IDCardNumber'?scope.row.IDCardNumber?.replace(/(\w{4})\w*(\w{4})/,'$1********$2'): scope.row[item.value]}}</span>
                <div v-else class="flex-btn">
                    <span v-if="!typelist.includes(scope.column.label)">
                        {{item.value=='IDCardNumber'?scope.row.IDCardNumber?.replace(/(\w{4})\w*(\w{4})/,'$1********$2'): scope.row[item.value]}}
                    </span>
                    <el-button v-if="typelist.includes(titles)&&typelist.includes(scope.column.label)" link type="primary" @click="preview(scope.row.FileUrl)">预览</el-button>
                    <el-button v-if="proew.includes(titles)&&proew.includes(scope.column.label)"  link type="primary" @click="looks(scope.row)">详情</el-button>
                    <el-button v-if="names=='特种作业证'&&item.name=='特种作业证'"  link type="primary" @click="verify(scope.row)">验证</el-button>
                </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else-if="falge1==1">
          <div id="maps"></div>
      </div>
      <el-date-picker
          v-if="falge==2"
              v-model="value1"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="daterange"
          >
      </el-date-picker>
      <div v-else-if="falge1==2" class="flex-img">
          <div v-for="(item,index) in tableData" :key="index" class="flex-time">
            <img :src="item.Image" alt="" class="cursor" @click="preview(item.Image)" style="width:150px;height:100px">
            <p>抓拍时间：{{item.InDate}}</p>
            <p>抓拍设备：{{item.ChannelName}}</p>
          </div>
      </div>
      <el-pagination v-if="falge1!=1&&falge1!=2" v-model:current-page="getform.page" v-model:page-size="getform.count" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          :total="Number(Totles) " @size-change="handleSizeChange" @current-change="handleCurrentChange" />

    </div>
    <el-dialog :title="title2" class=""  v-model="dialogTableVisible1" width="50%" append-to-body>
      <div class="zscounts">
        <div v-for="(item,index) in getdetil" :key="index" :style="getstylable(item.name)">
            <span>{{item.name}}：{{!showlable.includes(item.name)?addform[item.value]+(item.name=='培训时长'?addform.DurationUnit:''):''}}</span>
            <img v-if="showlable.includes(item.name)&&item.name!='附件'" alt="" class="cursor" :src="addform[item.value]" style="wdith:100px;height:100px" @click="preview(addform[item.value])">
            <div class="upload" v-if="item.name=='附件'">
                <el-icon><Document /></el-icon>
                <span class="cursor" @click="preview(addform.TrainAppendix)">预览文件</span>
				        <span class="cursor"  @click="dwon(addform.TrainAppendix)">下载文件</span>
            </div>
        </div>
      </div>
    <picimg ref="picimg"></picimg>

	</el-dialog>
    <picimg ref="picimg"></picimg>
  </el-dialog>
</template>

<script>
import { onMounted, ref,getCurrentInstance, nextTick } from 'vue'
import selection from "@/components/connet/Common/selection.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";
import china from "@/utils/china.json";
import login from '@/store/modules/login';
// import cadperon from "@/components/connet/personnelcon/content/cadperon.vue";
export default {
components:{
  selection,
  picimg,
  // cadperon
  },
setup(){
  const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist

    let dialogTableVisible=ref(false)
    let dialogTableVisible1=ref(false)
    let bgcolor=ref({})
    let titles=ref('')
    let title2=ref('')
    let modal=ref(true)
    let falge=ref(0)
    let panett=['']
    let names=ref('')
    let loading=ref(false)
    let tableData=ref([])
    let lables=ref([])
    let getform=ref({
      ProjectCode:'',
      TeamSysNo:'',
      page:1,
      count:10,
      InUserName:"",
      IDCardNumber:'',
      WorkerType:"",//工人类型 例：管理人员
      CorpName:"",//参见单位  例：浙江钱水建设有限公司
      Status:"1",//1在场 0离场 除了离场列表，根据其它参数获取列表的时候，Status 都传 1
      Age:"",//年龄 20以下，20-30等   例：20-30
      GrantOrg:'',
      Begtime:'',
      Endtime:'',
      Type:'',
      WorkType:'',
      BegDate:"",
      EndDate:"",
      EventType:"",
      Severity:"",
      IsQianTai:'前台',
      WorkTypeCode:'',
      IsSearchAttend:'',
      Date:''
    })
    let Totles=ref(0)
    let url=ref('')
    let typename=ref('')
    let typelist=['劳务合同','三级教育']
    let picimg=ref(null)
    let proew=['特种作业证','人员培训']
    let getdetil=ref([])
    let showlable=['培训照片','证书照片','附件']
    let addform=ref({TrainName:'',
                TypeName:'',
                TrainPerson:'',
                TrainTarget:'',
                TrainDate:'',
                TrainDuration:'',
                TrainContent:'',
                TrainPhoto:'',
                TrainAppendix:'',
                CertificateType:'',
                CertificateTypeName:'',
                CertificateName:'',
                CertificateCode:'',
                CertificateTerm:'',
                CertificatePhoto:'',
                Position:'',})
    let widths=['培训内容','培训照片','附件']
    let geturl=''
    let falge1=ref(0)
    let alldata=ref([])
    let value1=ref([])
    // let titles=ref()
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    })
    const showdelog=(val,name,CorpName)=>{
      // console.log('获取弹窗',val);
      for (const key in addform.value) {
            addform.value[key]=''
        }
      getform.value.ProjectCode=store.getters.code
      getform.value.InUserName=store.getters.username
      titles.value=val.name
      lables.value=[]
      names.value=val.name
      getform.WorkerType=name
      getform.CorpName=CorpName
      getform.Status='1'
      modal.value=true
      typename.value=''
      getform.value.GUID=''
      falge1.value=0
      getform.value.Type=val.name
      getform.value.IsSearchAttend=''
      lables.value=[...$labelist(val.name)]
      getform.value.page=1
      getform.value.count=10
      // modal.value=false
      switch (val.name) {
        case '人员库':
          names.value='历史人员库'
          getform.value.WorkerType=''
          getform.value.CorpName=''
          getform.value.Status='2'
          url.value='GetProjectWorkerTableByParam'
          break;
        case '劳务合同':
        case '三级教育':
        // case '特种作业证':
          typename.value='证书'
          url.value='GetContractThreeEduTable'
          break;
        case '身份证':
          // typename.value='证书'
          url.value='GetWorkerTableByIDCard'
          break;
        case '特种作业证':
          typename.value='证书'
          url.value='GetZGCertificateTable'
          break;
        case '人员培训':
          typename.value='证书'
          url.value='GetPersonTrainTable'
          break;
        case '黑名单人员':
          url.value='GetBadPostTable'
          break;
        case '人员户籍地':
          falge1.value=1
          modal.value=false
          url.value='GetPersonOrgByPro'

          break;
        case 'AI抓拍':
          falge1.value=2
          // modal.value=false
          url.value='GetAIStatisticsTableByType'

          break;
      }
      gettabledata()
      dialogTableVisible.value=true
    }
    const daterange=(val)=>{
      if (val) {
            getform.value.Begtime=val[0]
            getform.value.Endtime=val[1]
        }else{
            getform.value.Begtime=''
            getform.value.Endtime=''
        }
        // this.gettable()
        gettabledata()
    }
    const preview=(val)=>{
        let imgtype=['jpg','png','Jpeg']
      if (val) {
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1)
          if (imgtype.includes(file)) {
              picimg.value.piclist(val)
          }else{
          window.open('https://f.zqface.com/?fileurl='+val,'_slef')
          }
            }
	  }
    const getmaps=()=>{
        let _this=this
    var echarts = require('echarts');
    let myChart = echarts.getInstanceByDom(document.getElementById('maps'));
        if (myChart == null) {
        myChart = echarts.init(document.getElementById('maps'));
    }
    var geoCoordMap = {};
    
    myChart.on('click', function(params) {
			// console.log("数据",params);
			if (params.seriesType === 'map') {
          url.value='GetProjectWorkerTableByParam'
          gettabledata()
          falge1.value=0
          myChart.dispose()
			}
		});

    echarts.registerMap('china', china)
        var option = {  
          tooltip: {
          show: true,
          formatter: function (params) {
              // console.log('提交',params);
              if (params.data?.value) {
              return params.name + '：' + params.data.value
              }else{
                  return params.name
              }
          }
          },
          
           legend: {
                orient: 'vertical',
                left: 'right',
                data:['']
                  },      
           visualMap: {
               min: 0,
               max: alldata.value[0].value,
               right: '10%',
               top: 'bottom',
               text: ['高','低'],
               calculable : true,
               color:['#9704FF','#02FBFF']
                  },   
           series : [                      
           {
             name: '', 
             type: 'map',
             map: 'china',
             roam: true,
             label: {
                show: true
              },
              borderColor: 'rgba(0, 0, 0, 0.2)',
              emphasis:{
                   shadowOffsetX: 0,
                   shadowOffsetY: 0,
                   shadowBlur: 20,
                   borderWidth: 0,
                   shadowColor: 'rgba(0, 0, 0, 0.5)'
               },
              showLegendSymbol: false,
              data:alldata.value
            }
          ],
               }
        myChart.setOption(option);
           window.addEventListener("resize", function() {
           myChart.resize();
        });
    }
    const looks=(val)=>{
      getform.value.GUID=val.GUID
      if (titles.value=='特种作业证') {
        title2.value=val.WorkerName+'资质证书'
        getdetil.value=[...$labelist('特种作业证详情')]
        geturl='ZGCertificateSingleDetail'
      }else{
        title2.value='培训详情'
        getdetil.value=[...$labelist('人员培训详情')]
        geturl='GetPersonTrainDetail'

      }
      getdetilform()
      dialogTableVisible1.value=true
    }
    // 获取特种作业
    const verify=(val)=>{
      // console.log('验证',val);
        if (val.VerifyLink) {
        window.open(val.VerifyLink,'_block')
          
        }
        // window.open(val.VerifyLink,'_block')
    }
    const dwon=(val)=>{
      if (val) {
        window.location.href=val+ '?t=' + Math.random()
      }
    }
    const closes=()=>{

      if (titles.value=='人员户籍地'&&falge1.value==0) {
        falge1.value=1
        modal.value=true
        nextTick(()=>{
          getmaps()
        })
      }else{
        dialogTableVisible.value=false
      }
    }
    // 获取详情
    const getdetilform=async()=>{
        const {data:res}=await gettable(geturl,getform.value)
          if (res.code=="1000") {
            // console.log('是否是组数',Array.isArray(res.data));
            addform.value=Array.isArray(res.data)?res.data[0]:res.data
          }
    }
    // 获取列表
    const gettabledata=async()=>{
        tableData.value=[]
        loading.value=true
        const {data:res}=await gettable(url.value,getform.value)
        loading.value=false
        if (titles.value=='人员户籍地'&&falge1.value==1) {
          alldata.value=res.data[0].ECharts
          nextTick(()=>{
            getmaps()
          })
        }else{
          tableData.value=res.data
          Totles.value=res.Total
        }
        
    }
    const getstylable=(val)=>{

            let styles=widths.includes(val)?'grid-column: 1/span 2':''
            return styles
      }
    const rows=(val)=>{

    }
    const mouseles=(val)=>{

    }
    const mouseenter=(val)=>{
      
    }
    const tableRowClassName=({row,rowIndex,})=>{

      if (titles.value=='黑名单人员') {
        if (row.Status=='在场') {
            return 'warning-rows'
        }else if (row.Status=='离场'){
            return 'uline'
        }
      }
      if (rowIndex%2 != 0) {
          return 'warning-row'
      }
      
        return ''
    }
    const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        gettabledata()
        }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        getform.value.page=val
        gettabledata()
        }
    
    return{
        dialogTableVisible,
        dialogTableVisible1,
        picimg,
        titles,
        bgcolor,
        modal,
        falge,
        panett,
        names,
        loading,
        tableData,
        lables,
        getform,
        Totles,
        url,
        typename,
        typelist,
        proew,
        showlable,
        getdetil,
        title2,
        addform,
        widths,
        geturl,
        dwon,
        falge1,
        alldata,
        value1,

        closes,
        showdelog,
        mouseles,
        mouseenter,
        tableRowClassName,
        rows,
        gettabledata,
        preview,
        handleSizeChange,
        handleCurrentChange,
        getstylable,
        looks,
        getdetilform,
        daterange,
        verify,
        
        

    }
}
}
</script>
<style lang="scss">
.delogss{
    .el-table .warning-rows {
        .cell{
        color: red!important;

        }
    }
    .el-table .uline{
        .cell{
            color:#E88B0D!important;
        }
    }
}
</style>
<style lang="scss" scoped>
.delogss{
  margin-top: 15vh!important;
  --el-dialog-margin-top:15vh!important;
}
.counts{
  color: red;
  margin-bottom: 10px;
}
.zscounts{
  color: #000;
  display: grid;
  grid-template-columns: repeat(2,50%);
  div{
    margin: 10px;
  }
}
.flex-img{
    color: #fff;
    display: grid;
    grid-template-columns: repeat(5,20%);
    
}
.flex-time{
    margin: 10px;
}
.flex-btn{
  display: flex;
  justify-content: center;
}
.upload{
    display: grid;
    grid-template-columns: 10% 10%;
    .el-icon{
      grid-column: 1;
      grid-row: 1/span 2 ;
      font-size: 50px!important;
    }
}
#maps{
  width: 100%;
  height: 60vh;
}
</style>