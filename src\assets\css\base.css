@import "./normalize.css";
/* @import"./comment.scss"; */
/*:root -> 获取根元素html*/
:root {
	--color-text: #fff;
	--color-high-text: #ff5777;
	--color-tint: #ff8198;
	--color-background: #fff;
	--font-size: 12px;
	--line-height: 1.5;
}

*,
*::before,
*::after {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	/* font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",'Arial',sans-serif; */
	/* user-select: none; */
	user-select: text;
	/* 禁止用户鼠标在页面上选中文字/图片等 */
	-webkit-tap-highlight-color: transparent;
	/* webkit是苹果浏览器引擎，tap点击，highlight背景高亮，color颜色，颜色用数值调节 */
	background: var(--color-background);
	color: var(--color-text);
	/* rem vw/vh */

}

a {
	color: var(--color-text);
	text-decoration: none;
}

li {
	list-style: none;
}

.clear-fix::after {
	clear: both;
	content: '';
	display: block;
	width: 0;
	height: 0;
	visibility: hidden;
}
/* .el-form-item__label{
    width:120px ;
  }
.card{
	padding: 20px;
}
.el-main{
	padding: 0!important;
	background: #F0F2F5;

} */
.clear-fix {
	zoom: 1;
}

.left {
	float: left!important;
}

.right {
	float: right!important;
}

#app {
	height: 100%;
}
::-webkit-scrollbar {
	width: 10px;
	height: 10px;
}

::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	background: #AFAFAF;
}

::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	/* border-radius: 10px;
	background: #fff; */
}
.cursor{
	cursor: pointer;
}
 /* .pagepopr{ */
 	/* .el-select__popper.el-popper{
 		background: transparent!important;
 	} */
 /* } */
