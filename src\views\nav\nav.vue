<template>
  <div class="navbottom" ref="navbottomref" v-click-outside="test">
    <div :class="['navbottom-btom animate__animated',falge?'animate__rollIn':'animate__rollOut']" v-if="falge" >
    <!-- <div :class="['navbottom-btom']" v-if="falge" > -->
      <div class="navbottom-btom-semi">
        <div class="navbottom-external" >
          <div  v-for="(item,index) in external" :key="index"
           :class="['navbottom-external-'+item.claname,'rootnav','cursor',
           ]" :style="addmenui==item.claname?'color:'+bgcolor.hovercor:''" @mouseenter="mouseenter($event,'external')"
          @mouseleave="mouseleave($event,'external')" @click="linkrouter(item)">
            <i :class="['iconfont icon-'+item.icon]"></i>
            <p>{{item.name}}</p>
          </div>
        </div>
        <div class="navbottom-internal" >
          <div v-for="(item,index) in internal" :key="index"
           :class="['navbottom-internal-'+item.claname,'cursor','internal']" :style="addmenui==item.claname?
           `background:linear-gradient(154deg, rgba(1, 194, 255, 1) 13%, ${bgcolor.hovercor} 99%)`:''" 
           @mouseenter="mouseenter($event,'navmenu')"
           @mouseleave="mouseleave($event,'navmenu')"
           @click="linkrouter(item)"
          >
            <i :class="['iconfont icon-'+item.value1]"></i>
            <p>{{item.name}}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="navmenu cursor" title="点击展开导航，长按进行拖拽" @click="open()" @mouseenter="mouseenter($event,'navmenu')"
    @mouseleave="mouseleave($event,'navmenu')"
     @mousedown="onmousedown" @mouseup="cleartime" >
      <i class="iconfont icon-iconfontwuyuandaohang"></i>
      <p>导航</p>
    </div>
  </div>
</template>

<script>
import { onMounted, ref, watch, watchEffect,nextTick,inject  } from 'vue';
import {useRouter} from 'vue-router'
import { color } from 'echarts';
import store from "@/store";

let navmenu1
export default {
    // inject: ['reload'],

    setup(){
      // console.log('传值',inject());
        // const reload = inject('reload');
        // console.log('daioy',reload);
        
        const router=useRouter()
        let falge=ref(false)
        let addmenui=ref('')
        // let navbottomref=ref(null)
        let menu=['首页','人员管理','设备管理','物料管理','生产技术','绿色施工','后台管理']
        let internal=[
          {
            name:'质量安全',
            claname:'qure',
            value1:'a-zhilianganquan'
          },
          {
            name:'智能监控',
            claname:'monitor',
            value1:'monitoring'
          },
          {
            name:'党建管理',
            claname:'building',
            value1:'danghui'
          },
        ]
        let external=[
          {
            name:'首页',
            claname:'index',
            icon:'shouye'
          },
          {
            name:'人员管理',
            claname:'personnel',
            icon:'sc-'
          },{
            name:'设备管理',
            claname:'device',
            icon:'shebei1'
          },{
            name:'物料管理',
            claname:'material',
            icon:'wuliaotoufang'
          },{
            name:'生产技术',
            claname:'produce',
            icon:'shengchan'
          },{
            name:'绿色施工',
            claname:'construction',
            icon:'shigongdi'
          },{
            name:'后台管理',
            claname:'honme',
            icon:'houtai'
          },
        ]
        let timer=ref(null)
        let showfalge=ref(true)
        let mousecount=ref({
          left:null,
          top:null
        })
        let bgcolor=ref({})
        const test=()=>{
          // console.log('点击外部事件',navbottomref.value,falge.value);
          // console.log('点');
          let oBox = document.querySelector(".navbottom");
          let navmenu = document.querySelector(".navmenu");

          if (falge.value) {
          falge.value=false
          oBox.style.width='120px'
          oBox.style.height='120px'
          store.dispatch('getfalgenav',falge.value)
          
          oBox.style.top = (mousecount.value.top) + 'px';
          
          }
          // falge.value=false
        }
        window.addEventListener("resize", function(e) {
              // console.log('适应数据',e);
              let navbottom = document.querySelector(".navbottom");

              if (e.currentTarget.innerHeight>'940') {
                if (falge.value) {
                navbottom.style.width='50%'
                  
                }
              }else{
                if (falge.value) {
                navbottom.style.width='45%'
                  
                }
              }
              // myChart.resize();
               window.onresize = function(e){
                // myChart.resize();
              }
            });
        window.addEventListener('setthcolor', ()=> {
            // console.log('导航');
            bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // 获取包含伪元素的元素
        })
        onMounted(()=>{
          bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

          let oBox = document.querySelector(".navbottom");
          oBox.style.width='120px'
          oBox.style.height='120px'
          // console.log('获取导航是否展开',store.getters.falgenav);
          nextTick(()=>{
            open()
          })

        })

        watch(() => router.currentRoute.value.path,(toPath) => {
        // console.log('监听数据',toPath.split('/')[1]);
        if (toPath) {

        addmenui.value=toPath.split('/')[1]
          
        }
        //要执行的方法
        const query = router.currentRoute.value.query;
        },
        {immediate: true,deep: true})

        const btm=(val,index)=>{
            router.push('/personnel')
        }
        const handleRouteChange =()=>{
          // router.go(0)

        }
        const linkrouter=(val)=>{
          // console.log('路由跳转',val);
          router.push('/'+val.claname).then(handleRouteChange)
          // router.push('/'+val.claname)
          
        }
        const open=(val,index)=>{
          // console.log('先走');
          if (showfalge.value) {
          let oBox = document.querySelector(".navbottom");
          let navmenu = document.querySelector(".navmenu");
          // console.log('获取当前位置',navmenu.getBoundingClientRect());
          var scroll_height = document.documentElement.scrollHeight; 
          
          falge.value=!falge.value

          store.dispatch('getfalgenav',falge.value)

          if (falge.value) {
          if (scroll_height>'940') {
                oBox.style.width='50%'
            
          }else{
          oBox.style.width='45%'

          }


          // oBox.style.width='45%'
          oBox.style.height='45%'
          nextTick(()=>{
            let color=JSON.parse(sessionStorage.getItem('themecolor')).navcolor
            // console.log('导航颜色',color);
            let navavolor=`rgba(${color},0.6)`
            // 创建一个新的元素
            const beforeElement = document.createElement('div');

            // 添加类名和样式
            beforeElement.className = 'before-element';

            beforeElement.style.content = '';
            beforeElement.style.display = 'block';
            beforeElement.style.position = 'absolute';
            beforeElement.style.top = 0;
            beforeElement.style.left = 0;
            beforeElement.style.right = 0;
            beforeElement.style.bottom = 0;
            beforeElement.style.borderRadius = '50%';
            beforeElement.style.opacity='1'
            beforeElement.style.boxSizing='border-box'
            beforeElement.style.border=`140px solid  ${navavolor}`


            // 将新元素插入到容器中
            const container = document.querySelector('.navbottom-btom-semi');
            container.insertBefore(beforeElement, container.firstChild);
            // const element = document.querySelector('.navbottom-btom-semi');

          })
          if (mousecount.value.top) {
            let cels=Math.ceil(navmenu.getBoundingClientRect().y)
            // console.log('获取',mousecount.value.top,Math.ceil(navmenu.getBoundingClientRect().y));
            
          oBox.style.top =mousecount.value.top-(cels-mousecount.value.top) + 'px';
          // console.log('高度',oBox.style.top);
          
          }
          
          }else{
            // console.log('关闭');
            
          oBox.style.width='120px'
          oBox.style.height='120px'

          oBox.style.top = (mousecount.value.top) + 'px';
            }
          }
            // router.push('/personnel')
        }
        const cleartime=()=>{
          clearTimeout(timer.value)
          navmenu1.style.cursor="pointer"
          setTimeout(()=>{
          showfalge.value=true

          },500)
          
        }
        
        const onmousedown=(e)=>{
          navmenu1 = document.querySelector(".navmenu");
          // navmenu1.style.cursor='pointer'
          // falge.value=false

          timer.value=setTimeout(()=>{
            // console.log('长按移动导航',navmenu1.style.cursor);
          navmenu1.style.cursor='move'
          falge.value=false
          showfalge.value=false
          store.dispatch('getfalgenav',falge.value)

          var oBox = document.querySelector(".navbottom");
          oBox.style.width='120px'
          oBox.style.height='120px'

        // move
          let partenwidth=oBox.clientWidth/2
          let partenheight=oBox.clientHeight-50
                document.onmousemove = (e) => { //鼠标按下并移动的事件
                    mousecount.value.left = e.clientX;
                    mousecount.value.top = e.clientY-partenheight;

                    //移动当前元素
                    oBox.style.left = mousecount.value.left + 'px';
                    oBox.style.top = mousecount.value.top + 'px';
                    // console.log('移动当前位置',oBox.style.top);
                    
                    }
                document.onmouseup = (e) => {
                    document.onmousemove = null;
                    document.onmouseup = null;
                };
          },300)
            // router.push('/personnel')
        }
        const mouseenter=(val,name)=>{
          // console.log('鼠标移入',name,val.target.style.background);
          let hovercolor=JSON.parse(sessionStorage.getItem('themecolor'))
          if (name=="navmenu"&&!val.target.style.background) {
          val.target.style.background=`linear-gradient(200deg, rgba(1, 194, 255, 1) 13%, ${hovercolor.hovercor} 99%)`
            
          }else{
            if (name!="navmenu") {
          val.target.style.color=hovercolor.hovercor
              
            }
          }
        }
        const mouseleave=(val,name)=>{
          let classs=val.target.classList[0].split('-')
          let menu=classs[classs.length-1]
          // console.log('虎丘',menu);
          
        if (name=="navmenu") {
            // console.log('获取',val.target.classList);
            if (menu!=addmenui.value) {
              val.target.style.background=""
            }
          }else{
            if (menu!=addmenui.value) {
              val.target.style.color=''
            }

          }
        }
        return{
          // mousecount,
          bgcolor,
            timer,
            addmenui,
            showfalge,
            falge,
            menu,
            internal,
            external,
            btm,
            open,
            cleartime,
            onmousedown,
            mouseenter,
            mouseleave,
            test,
            linkrouter,
            handleRouteChange
            // navbottomref
        }
    }
}
</script>
<style lang="scss" scoped>
// $falge判断有的标签是否需要该字段
@mixin fiex($wids,$heis,$sbgq,$qybgc,$falge) {
    position: fixed;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 1;
    border-radius: 50%;
    transform: translate(-50%, 0%);
    @if $falge!=0{
    background: linear-gradient(154deg, $sbgq 13%, $qybgc 88%);//渲染度数 深颜色占比 浅颜色 占比 
    height:$heis;
    width:$wids;
    }
}

@mixin lefcolor($left,$bottom,$right,$top,$name,$size){
  left:$left;
  right:$right;
  bottom:$bottom;
  top:$top;
.#{$name}{
  font-size: $size!important;
}
}

.navbottom{
  position: fixed;
  z-index: 1000;
  bottom: 0%;
  left: 50%;
  width: 45%;
  height: 45%;
  // width: 880px;
  // height: 880px;
  overflow: hidden;
  transform: translate(-50%, 0%);
  color: #fff;
  // 内部导航
  &-internal{
    p{
      font-size: 12px;
      padding: 5px 0;
    }
    &-qure{
      // 公用属性
      @include fiex(10%,10%,rgba(34,82,177,0.40),rgba(55,154,255,0.40),1);
      bottom: 52%;
      left: 28%;
      .icon-a-zhilianganquan{
      font-size: 40px!important;
      }
    }
    &-monitor{
      @include fiex(10%,10%,rgba(34,82,177,0.40),rgba(55,154,255,0.40),1);
      left: 50%;
      top: 20%;
      .icon-monitoring{
      font-size: 43px!important;
      }
    }
    &-building{
      @include fiex(10%,10%,rgba(34,82,177,0.40),rgba(55,154,255,0.40),1);
      bottom: 52%;
      right: 18%;
      .icon-danghui{
      font-size: 40px!important;
      }
    }

  }
  // 外部导航
  &-external{
    p{
      font-size: 15px;
      padding: 5px 0;
    }
    // 外部公用背景大小
    .rootnav{
    @include fiex(13%,26%,rgba(34,82,177,0.80),rgba(55,154,255,0.80),0);

    }
    // 每个定位
    &-index{
    @include lefcolor(8.5%,52%,none,none,icon-shouye,50px)
    }
    &-personnel{
      @include lefcolor(16%,70%,none,none,icon-sc-,50px)
    }
    &-device{
      @include lefcolor(30%,none,none,8%,icon-shebei1,50px)
    }
    &-material{
      @include lefcolor(50%,none,none,3%,icon-wuliaotoufang,50px)
    }
    &-produce{
      @include lefcolor(none,none,22%,8%,icon-shengchan,50px)
    }
    &-construction{
      @include lefcolor(none,70%,8%,none,icon-shigongdi,50px)
    }
    &-honme{
      @include lefcolor(none,52%,1%,none,icon-houtai,50px)
    }
  }

  &-btom{
    position: relative;
    height: 200%;
    width: 100%;
    overflow: hidden;
  &-semi{
    height: 100%;
    width: 100%;
    border-radius: 50%;
  }


  &-semi::before {
    // content: '';
    // display: block;
    // position: absolute;
    // top: 0px; /* 让圆环分别向上、下、左、右延伸出去 */
    // left: 0px;
    // right: 0px;
    // bottom: 0px;
    // // border: 140px solid #003046; /* 圆环宽度 */
    // border-radius: 50%;
    // opacity: 1;
    // box-sizing: border-box;
    // border: 140px solid rgba(0, 48, 70, 0.6);

    // backdrop-filter: blur(378px);
    }
  }
  // 宽度和高度暂时修改
  .navmenu{
    width: 110px;
    height: 105px;
    // @include fiex(13%,26%,rgba(34,82,177,0.80),rgba(55,154,255,0.80),1);
    bottom: 10px;
    left: 50%;
    position: fixed;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 1;
    border-radius: 50%;
    transform: translate(-50%, 0%);
    background: linear-gradient(154deg, rgba(34,82,177,0.80) 13%, rgba(55,154,255,0.80) 88%);//渲染度数 深颜色占比 浅颜色 占比 

    .icon-iconfontwuyuandaohang{
      font-size: 57px!important;
      color: #fff;
    }
 }
 .clickafter{
  color: #03FBFF!important;
 }
 .clickafterbg{
  background: linear-gradient(154deg, #03FBFF 13%, #00BEC1 88%);
 }
}
</style>