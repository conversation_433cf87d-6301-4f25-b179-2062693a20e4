import { createRouter, createWebHistory,createWebHashHistory } from 'vue-router'
import layout from '../views/layout'
import login from '@/views/login.vue'
const NotFound = () => import("@/views/page404")
const ThreeDemo = () => import("@/views/ThreeDemo")
const LargeScreen = () => import("@/views/multiple/largescreen.vue")
const phone = () => import("@/views/multiple/index/phone.vue")
const ycphone = () => import("@/views/multiple/index/ycphone.vue")

const Home = () => import("@/components/commet/Home")
const personnel = () => import("@/components/commet/personnel")
const device = () => import("@/components/commet/device")
const material = () => import("@/components/commet/material")
const technology = () => import("@/components/commet/technology")
const construction = () => import("@/components/commet/construction")
const safety = () => import("@/components/commet/safety")
const monitoring = () => import("@/components/commet/monitoring")
const Partybuilding = () => import("@/components/commet/Partybuilding")

// import HomeView from '../views/HomeView.vue'

const routes = [
  {
    path: '/',
    redirect: "/login",
  },
  {
    path: '/login',
    name: 'login',
    component: login,
  },
  {
    path: '/large-screen',
    name: 'largeScreen',
    component: LargeScreen
  },{
    path: '/phone',
    name: 'phone',
    component: phone
  },
  {//扬尘查看数据
    path: '/ycphone',
    name: 'ycphone',
    component: ycphone
  },
  {
    path: "/404",
    component: NotFound,
    name: "404",
    hidden: true,
    children: []
  },
  {
    path: '/three-demo',
    name: 'ThreeDemo',
    component: ThreeDemo,
    hidden: true
  },
  {
    path: '/index1',
    name: 'layout',
    redirect: "/index",
    component: layout,
    children: [
      {
      path: '/index',
      name: 'Home',
      component: Home
      },
      
      {
        path: '/personnel',
        name: 'personnel',
        component: personnel
      },
      // device设备管理
      {
        path: '/device',
        name: 'device',
        component: device
      },
      // 物料管理material
      {
        path: '/material',
        name: 'material',
        component: material
      },
      // 生产技术technology
      {
        path: '/produce',
        name: 'produce',
        component: technology
      },// 绿色technology
      {
        path: '/construction',
        name: 'construction',
        component: construction
      },// 质量安全safety
      {
        path: '/qure',
        name: 'qure',
        component: safety
      },// 智能监控monitoring
      {
        path: '/monitor',
        name: 'monitor',
        component: monitoring
      },
      // 党建管理Partybuilding
      {
        path: '/building',
        name: 'building',
        component: Partybuilding
      },
    ]
  },
]

const router = createRouter({
  history: createWebHashHistory(process.env.BASE_URL),
  routes
})

export default router
