"use strict";(self["webpackChunksmart"]=self["webpackChunksmart"]||[]).push([[73],{72073:function(e,t,n){n.r(t),n.d(t,{default:function(){return b}});var s=n(73396),a=n(87139),i=n(44870),o=n(15941);const r={class:"webgl-content"};var c={__name:"ThreeScene",setup(e,{expose:t}){const n=(0,i.iH)(null),c=(0,i.iH)({width:"1280px",height:"720px"}),l=()=>new Promise((e=>{const t=document.createElement("script");t.src="/ai/TestWeb/TemplateData/UnityProgress.js",document.head.appendChild(t);const n=document.createElement("script");n.src="/ai/TestWeb/Build/UnityLoader.js",n.onload=()=>e(),document.head.appendChild(n)})),u=e=>{null===e&&(e="打开测试标签"),null!==n.value&&n.value.SendMessage("UnityWeb","ReceiveFromWeb",e)},d=e=>{const t={detail:{hazcheeseburger:!0},bubbles:!0,cancelable:!0,composed:!0},n=new CustomEvent(e,t);window.top.dispatchEvent(n),o.log("从Unity收到消息:",n,e),u("打开测试标签")};return t({sendToUnity:u}),(0,s.bv)((async()=>{await l(),n.value=UnityLoader.instantiate("unityContainer","/ai/TestWeb/Build/TestWeb.json",{onProgress:UnityProgress}),window.ReceiveFromUnity=d})),(0,s.Jd)((()=>{n.value&&n.value.Quit()})),(e,t)=>((0,s.wg)(),(0,s.iD)("div",r,[(0,s._)("div",{id:"unityContainer",style:(0,a.j5)(c.value)},null,4)]))}},l=n(40089);const u=(0,l.Z)(c,[["__scopeId","data-v-354b0492"]]);var d=u;const v={class:"three-demo"};var p={__name:"ThreeDemo",setup(e){(0,i.iH)(null);return(0,s.bv)((()=>{})),(e,t)=>((0,s.wg)(),(0,s.iD)("div",v,[(0,s.Wm)(d,{ref:"ThreeSceneRef"},null,512)]))}};const m=(0,l.Z)(p,[["__scopeId","data-v-44fe9027"]]);var b=m}}]);