<template>
  <el-dialog v-model="dialogTableVisible"   destroy-on-close
    class="delogss" :width="widths.includes(title)?'50%':'70%'">
    <selection ref="selections" @colses="closes" :titles="title"></selection>
    <div v-if="falge=='0'" class="datedelog bodybottom bules" :style="getstyle">
        <h2 v-if="toptitle">{{title}}</h2>
        <div class="serch" v-for="(item,index) in serch" :key="index">
            <span>{{item.name}}:</span>
            <el-input v-if="item.type==1" v-model="getform[item.value]" :style="getwidth(item)"  placeholder="请输入关键字" size="small" clearable></el-input>
            <el-date-picker v-if="item.type==3" v-model="getform[item.value]" type="date" size="small" placeholder="选择日期"
             format="YYYY-MM-DD" value-format="YYYY-MM-DD"  :style="getwidth(item)"  />
            <el-select v-else-if="item.type==2" popper-class="bules" size="small" :popper-append-to-body="true"
             clearable :style="getwidth(item)" v-model="getform[item.value]" 
              class="m-2" placeholder="请选择" @click="getMaterial(item)">
                    <el-option  v-for="(items,index) in item.list" :key="index"
                    :label="items.name" :value="items.value" />
                </el-select>
        </div>
        <el-button type="primary" size="small" @click="search">搜索</el-button>
        <el-table :data="tableDate" class="cursor" :style="['width: 100%',`color:${bgcolor.font};
            --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
            :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}" max-height="550px"
            empty-text="暂无数据"
            >
            <template #empty>
                <el-empty  v-loading="loading"></el-empty>
            </template>
            <el-table-column v-for="(item,index) in labelist1" :key="index" 
            :width="item.widths" :prop="item.value" :label="item.name" align="center"> 
            <template #default="scope">
                <span v-if="item.type=='1'" :style="getcolor(item,scope.row)">{{scope.row[item.value]}}</span>
                <div v-else-if="item.name=='仓库预警'" class="round" :style="getround(item,scope.row)"></div>
                <span v-else>{{scope.row[item.value]}}</span>
            </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100" v-if="opter">
                <template #default="scope">
                    <el-button type="primary" text size="small" @click="open(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination  v-model:current-page="getform.page" popper-class="bules" v-model:page-size="getform.count" 
        :page-sizes="[10, 20, 40, 50, 100]"  :background="false" layout="total, sizes, prev, pager, next, jumper"
        :total="total"  @size-change="handleSizeChange"  @current-change="handleCurrentChange"/>
    </div>
    <div v-else class="datedelog bodybottom bules lable" :style="getstyle">
        <h2>{{title}}</h2>
        <div v-for="(item,index) in labeform" class="text" :key="index" :style="getcloume(item)">
            <span v-if="item.name">{{item.name}}：</span>
            <span v-if="!item.img&&item.name">{{addform[item.value]}}</span>
            <img v-if="!item.img&&item.value1" :src="addform[item.value1]" class="cursor"
             @click="pic(addform[item.value1])" alt="" style="width: 150px;height: 100px;">
            <template v-if="item.img">
                <img v-for="(its,i) in addform[item.value]" :key="i" :src="its" alt="" 
                style="width: 150px;height: 100px;" class="cursor" @click="pic(its)">
            </template>
            <el-scrollbar height="300px" v-if="item.type==2">
                <div v-for="(all,ii) in addform?.materialsInfo" :key="ii" class="lable">
                    <h2>材料{{ ii+1 }}</h2>
                    <div v-for="(table,index) in tablelist" :key="index" class="text"> 
                        <span>{{table.name}}：{{all[table.value]}}</span>
                    </div>
                </div>
            </el-scrollbar>
        </div>
    </div>
    <picimg ref="picimgs"></picimg>
    </el-dialog>
</template>

<script setup>

import selection from "@/components/connet/Common/selection.vue";
import picimg from "@/components/connet/Common/picimg.vue";
import { onMounted, ref, computed } from 'vue';
import { labelist } from "@/components/connet/material/content/lables.js";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import { updateFormListFields } from '@/utils/dataTransform'
let dialogTableVisible=ref(false)
let bgcolor=ref({})
let title=ref('')
let tableDate=ref([])
let loading=ref(false)
let labelist1=ref([])
let serch=ref([])
let total=ref(0)
let titleist=[]
let falge=ref(0)//0为供货商验收统计，1为详情
let getform=ref({
    page:1,
    count:10,
    SupplierName:'',
    ProjectCode:store.getters.code,
    ReceiveTime:"",//时间
    SupplierName:"",//供货商  必传
    SearchStr:"",//关键字
    MaterialClass:"",///材料分类
    DJSource:'',
    EquipCode:'',
    IconType:'',

})
let widths=['']
let url=ref('')
// 添加历史记录数组，用于存储页面状态
let history=ref([])
// 添加当前导航层级记录
let currentLevel=ref(0)
// 添加物料验收记录标记
let isMaterialRecord=ref(false)
let mater={
    url:'',
    title:'',
    value:''
}
let labeform=ref([])
let onelist=['运单编号','偏差情况','']
let twolist=[]
let geturl=''
let addform=ref({})
let picimgs=ref(null)
let tablelist=ref([])
let opter=true
let Twolayers=['移动发料记录','地磅过磅记录']
let toptitle=ref('')

window.addEventListener('setthcolor', ()=> {
    bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
})
onMounted(()=>{
    bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    getform.value.ProjectCode=store.getters.code
})
const showdelog=(val,titleText)=>{
    console.log('titleText',val,titleText);
    toptitle.value=''
    title.value=titleText
    titleist[0]=titleText
    falge.value=0
    opter=true
    labelist1.value=labelist(titleText)
    // 初始化历史记录和导航层级
    history.value = []
    currentLevel.value = 0
    // 重置物料验收记录标记
    isMaterialRecord.value = false
    
    switch (titleText) {
        case '领用记录列表':
            url.value='GetNewTakingRecordTable'
            serch.value=labelist('领用记录查询')
            break;
        case '供货商验收统计':
            serch.value=labelist('供货商查询')
            url.value='GetSupplierCheckTable'
            getform.value.SupplierName=val.SupplierName
            break;
        case '材料种类验收分析':
            // console.log('材料种类验收分析');
            url.value='GetMaterialTypeCheckTable'
            getform.value.MaterialClass=val

            serch.value=labelist('供货商查询')
            labelist1.value=labelist('供货商验收统计')
            labelist1.value.forEach((item,index)=>{
                if (item.name=='供货商名称') {
                    item.name='材料种类名称'
                    item.value='MaterialClass'
                }
            })
            break;
        case '供货商偏差分析':
            // console.log('供货商偏差分析');
            url.value='GetSupplierDeviationTable'
            serch.value=labelist('领用记录查询')
            serch.value.splice(2,1)
            labelist1.value=labelist('供货商偏差分析')
            break;
        case '物料验收记录':
        case '智慧地磅':
            // 设置物料验收记录标记为true
            isMaterialRecord.value = true
            
            // 根据不同情况设置特定配置
            const config = {
                '物料验收记录': {
                    title: val.name === '移动验收' ? '移动发料记录' : titleText,
                    url: 'GetMaterialTypeRecordTable',
                    additionalFields: [],
                },
                '智慧地磅': {
                    title: '地磅过磅记录',
                    url: 'GetReceiveTableByDJScore',
                    additionalFields: [{
                        name: '偏差情况',
                        value: 'Deviation',
                        type:'1',
                        widths: '',
                    }]
                }
            };
            
            // 设置标题和URL
            title.value = config[titleText].title;
            url.value = config[titleText].url;
            
            // 设置表格列
            labelist1.value = labelist('供货商列表');
            labelist1.value.splice(2, 0, {
                name: '供货商',
                value: 'SupplierName',
                widths: '',
            });
            
            // 添加额外字段（如偏差情况）
            if (config[titleText].additionalFields.length > 0) {
                labelist1.value.push(...config[titleText].additionalFields);
            }
            
            // 设置基础参数
            falge.value = 0;
            serch.value = labelist('供货商列表查询');
            serch.value.splice(1, 0, {
                name: '供应商',
                value: 'SupplierName',
                type: 2,
                list: [],
                widths: '150',
            });
            getform.value.DJSource = val.name;
            // 智慧地磅特有的参数
            if (titleText === '智慧地磅') {
                getform.value.IconType = val?.IconType;
                getform.value.EquipCode = val?.EquipCode;
                labelist1.value[1].name='过磅日期'
                serch.value.splice(3, 0, {
                    name: '偏差情况',
                    value: 'Deviation',
                    type: 2,
                    list: labelist('领用记录查询')[2].list,
                    widths: '150',
                });
            }else{
                labelist1.value[1].name='收发料日期'

            }
            
            break;
    }
    getdetable()
    dialogTableVisible.value=true
}

const search=()=>{
    // console.log('搜索',getform.value);
    getform.value.page=1
    getform.value.count=10
    getdetable()
}
// 获取材料下拉框
const getMaterial=async(val)=>{
    switch (val.name) {
        case '材料种类':
            mater.url='GetMaterialClassDrop'
            mater.title='材料种类'
            mater.value='MaterialClass'
            break;
        case '供应商':
            mater.url='GetSupplierNameDrop'
            mater.title='供应商'
            mater.value='SupplierName'
            break;
    }
    // console.log('获取材料',getform.value.MaterialClass);
    const {data:res}=await gettable(mater.url,getform.value)
    if (res.code=='1000') {
        serch.value=updateFormListFields(
            serch.value,
            [mater.title],
            res.data || [],
            {
                nameField:mater.value,
                valueField:mater.value
            }
        )
    }
}
 const pic=(val)=>{
    let imgtype=['png','jpg','jpeg','gif','bmp']
    if (val) {
        let lastIndex= val.lastIndexOf('.')
        let file=val.substring(lastIndex+1).toLowerCase()
        if (imgtype.includes(file)) {
            // console.log('获取',val);
            picimgs.value.piclist(val)
        }else{
            let type = val.substring(val.lastIndexOf('.') + 1).toLowerCase()
            
            if (type !== 'mp4') {
                window.open('https://f.zqface.com/?fileurl='+val,'_slef')
            }

        }
    }
}
const getcloume=(val)=>{
    // 如果 val.name 在 onelist 数组中，合并三列
    if (onelist.includes(val.name)) {
        return 'grid-column: 1 /span 3';
    }
    // 如果 val.name 在 twolist 数组中，合并两列
    else if (twolist.includes(val.name)) {
        return 'grid-column: 1 /span 2';
    }
    // 如果都不在，则不合并列
    return '';
}
 const getdetable=async()=>{ 
    loading.value=true
    const {data:res}=await gettable(url.value,getform.value)
    // console.log('获取数据',res);
    loading.value=false
    tableDate.value=res.data
    total.value=res.Total || 0
 }
//  获取详情
 const getdetil=async(val)=>{
    let GUID={
        GUID:val.GUID,
        ProjectCode:store.getters.code,
    }
    const {data:res}=await gettable(geturl,GUID)
    // console.log('获取数据',res);
    if (res.code=='1000') {
        addform.value = Array.isArray(res.data) ? res.data[0] : res.data
        
        // addform.value
        if (Array.isArray(res.data)&&Array.isArray(addform.value.AppearancePhotos)) {
            
        let arr=[addform.value.AppearancePhotos[0].PhotoUrl1,addform.value.AppearancePhotos[0].PhotoUrl2]
        let arr1=[addform.value.ApproachPhotos[0]?.PhotoUrl1,addform.value.ApproachPhotos[0]?.PhotoUrl2]
        
        addform.value.AppearancePhotos=arr
        addform.value.ApproachPhotos=arr1
        }
        // console.log('数组',addform.value);
        
    }
 }
 const getstyle=()=>{
    return `border:2px solid ${bgcolor.value.titlecolor};
    background:rgba(${bgcolor.value.delogcolor},0.35)`
 }
 const getwidth=(val)=>{
    if (val.widths) {
        return `width: ${val.widths}px`
    }else{
        return 'width: 200px'
    }
 }
 const getround=(val,row)=>{
    if (row[val.value]=='0') {
        return 'background-color: #1B9E35'
    }else if(row[val.value]=='1'){
        return 'background-color: red'
    }
 }
 // 显示磅单详情的公共函数
 const showReceiptDetail = (row) => {
    falge.value = 1
    title.value = `磅单编号 ${row.ReceivingCode}`
    labeform.value = labelist('磅单详情')
    
    if (row.DJSource == '移动收料') {
        geturl = 'GetMoveReceiveDetail'
        labeform.value = labelist('移动收料详情')
        tablelist.value = labelist('移动收料材料')
    } else {
        geturl = 'GetReceiveDetail'
    }
    
    getdetil(row)
    return true // 返回true表示已处理，调用处可以直接return
 }
 
 const open=(row)=>{
    console.log('查看', title.value);
    opter=true
    toptitle.value=''

    // 如果是物料验收记录且当前已经是第二层，不再增加导航层级
    if (isMaterialRecord.value && currentLevel.value >= 1 && row.GUID) {
        if (showReceiptDetail(row)) return // 使用公共函数并直接返回
    }
    
    // 保存当前页面状态到历史记录
    saveCurrentState();
    // 增加导航层级
    currentLevel.value++;

    switch (title.value) {
        case '供货商验收统计':
            labelist1.value = labelist('供货商列表');
            falge.value=0
            // console.log('供货商列表',labelist1.value);
            url.value='GetSupplierRecordTable'
            title.value = row.SupplierName;
            serch.value=labelist('供货商列表查询')
            getform.value.SupplierName=row.SupplierName
            break;
        case '材料种类验收分析':
            labelist1.value = labelist('供货商列表');
            falge.value=0
            url.value='GetMaterialTypeRecordTable'
            title.value = row.MaterialClass;
            // getform.value.MaterialClass=row.MaterialClass
            serch.value=labelist('供货商列表查询')
            serch.value.forEach((item,index)=>{
                if (item.name=='材料种类') {
                    item.name='供应商'
                    item.value='SupplierName'
                }
            })
            getform.value.MaterialClass=row.MaterialClass

            labelist1.value.forEach((item,index)=>{
                if (item.name=='供货商名称') {
                    item.name='材料种类名称'
                    item.value='MaterialClass'
                    labelist1.value.splice(index,0,{
                        name:'供应商',
                        value:'SupplierName',
                        widths:'',
                    })

                }
                if (item.name=='材料种类') {
                    labelist1.value.splice(index,1)
                }
            })
            break;
        case '领用记录列表':
            labelist1.value = labelist('领用记录详情');
            falge.value=0
            opter=false
            toptitle.value='领用记录列表'
            url.value='GetNewTakingRecordDetailTable'
            title.value = row.SupplierName+row.MaterialClass+row.MaterialModel;
            serch.value=labelist('供货商列表查询')
            serch.value[0].value='TakingDate'
            serch.value.splice(1,1)
            getform.value.MaterialCode=row.MaterialCode
            break;
        case '供货商偏差分析':
            url.value='GetReceiveTableBySupplier'

            labelist1.value = labelist('供货商列表');
            labelist1.value[0].name='过磅日期'
            let arr=[{
                name:'偏差情况',
                value:'Deviation',
                type:1,
                widths:'',
                },{
                name:'偏差数（重）量',
                value:'WeightDeviation',
                widths:'',
                }]
            labelist1.value.push(...arr)
            falge.value=0
            opter=true
            title.value = row.SupplierName;
            serch.value=labelist('领用记录查询')
            // serch.value[0].name='过磅日期'
            serch.value.splice(0,0,{
                name:'过磅日期',
                value:'ReceiveTime',
                type:3,
                widths:'150',
            })
            getform.value.SupplierName=row.SupplierName
            break;
        case (Twolayers.includes(title.value) && isMaterialRecord.value ? title.value : ''):
            // 物料验收记录第二层 - 磅单详情
            if (row.GUID) {
                if (showReceiptDetail(row)) return // 使用公共函数并直接返回
            }
            break;
        default:
            // 处理供货商详情页面下的第三层导航
            if (currentLevel.value >= 2) {
                // 这里处理第三层导航的逻辑
                if (row.GUID) { 
                    if (showReceiptDetail(row)) return // 使用公共函数并直接返回
                } else {
                    // 其他第三层导航逻辑
                    title.value = `${title.value} - 详情`;
                }
            }
            break;
    }
    getdetable();
}
 const getcolor=(val,row)=>{
    if (val.name=='偏差情况') {
        if (row.Deviation=='负偏差') {
            return 'color:  #01C2FF'
        }else if(row.Deviation=='超负差'){
            return 'color:  red'
        }else{
            return 'color:  #008000'
        }
    }
    return ''
 }

 // 保存当前页面状态到历史记录的函数
 const saveCurrentState = () => {
    history.value.push({
        title: title.value,
        labelist1: [...labelist1.value],
        serch: [...serch.value],
        url: url.value,
        getform: {...getform.value},
        tableDate: [...tableDate.value],
        total: total.value,
        level: currentLevel.value
    });
    // console.log('保存状态:', title.value, '层级:', currentLevel.value);
 }

/**
 * 恢复页面状态的辅助函数
 * @param {Object} state - 要恢复的状态对象
 * @param {boolean} clearHistory - 是否清空历史记录
 * @param {number} newLevel - 要设置的新导航层级
 */
const restoreState = (state, clearHistory = false, newLevel = null) => {
    // 恢复基础状态
    falge.value = 0;
    opter = true;
    
    // 恢复所有页面状态
    title.value = state.title;
    labelist1.value = state.labelist1;
    serch.value = state.serch;
    url.value = state.url;
    getform.value = state.getform;
    tableDate.value = state.tableDate;
    total.value = state.total;
    
    // 设置导航层级
    currentLevel.value = newLevel !== null ? newLevel : state.level;
    
    // 如果需要清空历史记录
    if (clearHistory) {
        history.value = [];
    }
    
    // 重新获取数据
    getdetable();
};

/**
 * 处理对话框关闭或返回上一级
 * 根据当前状态和历史记录决定如何处理:
 * 1. 如果是物料验收记录详情页，直接返回第一层
 * 2. 如果有历史记录，返回上一页状态
 * 3. 如果没有历史记录，关闭对话框
 */
const closes = () => {
    // 特殊情况：物料验收记录的详情页直接返回第一层
    if (isMaterialRecord.value && falge.value === 1 && history.value.length > 0) {
        const firstLayerState = history.value.pop();
        restoreState(firstLayerState, true, 0);
        return;
    }
    
    // 常规情况：根据历史记录决定返回上一页或关闭对话框
    if (history.value.length > 0) {
        const prevState = history.value.pop();
        restoreState(prevState);
    } else {
        // 没有历史记录，关闭对话框
        dialogTableVisible.value = false;
    }
};

const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
const handleSizeChange = (val) => {
    console.log(`${val} 显示多少页`)
    getform.value.count=val
    getform.value.page=1
    getdetable()
    }
const handleCurrentChange = (val) => {
    console.log(`选择第几: ${val}`)
    getform.value.page=val
    getdetable()
    }

defineExpose({
  showdelog
})
</script>
<style lang="scss" scoped>
.serch{
    color: #fff;
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
}
.bodybottom{
    text-align: left;
    h2{
        color: #fff;
        margin-bottom: 10px;
    }
}
.round{
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #01C2FF;
}
.lable{
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    h2{
        grid-column: 1 /span 3;
        color: #fff;
    }
}
.text{
    color: #01C2FF;
    margin: 20px 10px;
    display: flex;
}
.cursor{
    margin: 5px;
}
.el-scrollbar{
    grid-column: 1 /span 3!important;

}
</style>