<template>
  <!-- 设备 -->
  <div class="Equipment padding"  :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="topforms" @opens="opentable"></Chamfering>
    <div class="Equipment-two">
      <span class="tops" v-for="(item,index) in tops" :key="index">{{item.name}}</span>
      <div class="Equipment-two-top" v-for="(item,index) in list" :key="index">
        <div class="Equipment-two-left">
          <img :src="item.src" alt="" style="width:50px;height:50px">
          <p>{{item.name}}</p>
        </div>
        <div class="Equipment-two-right">
          <span v-for="(its,i) in 3" :key="i" :class="i==1?'offline':''">{{form[item[tops[i+1].value]]}}</span>
        </div>
      </div>
    </div>
    <delog ref="delogs"></delog>
     <Chamfering :homeindex="homeindex" :horn="0"></Chamfering>
  </div>
</template>

<script>
import { ref ,onMounted} from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/Home/content/delog.vue";

export default {
props:['homeindex'],
components:{
  Chamfering,
  delog
},
setup(props){
  let list=ref([
    {
      name:'塔式起重机',
      src:require('@/assets/img/home/<USER>/001.png'),
      online:'TjNum',
      unline:'TjOffNum',
      disassemble:'TjCXNum'
    },
    {
      name:'施工升降机',
      src:require('@/assets/img/home/<USER>/003.png'),
      online:'SjjNum',
      unline:'SjjOffNum',
      disassemble:'SjjCXNum'

    },
    {
      name:'卸料平台',
      src:require('@/assets/img/home/<USER>/002.png'),
      online:'XlNum',
      unline:'XlOffNum',
      disassemble:'XlCXNum'

    }
  ])
   let bgcolor=ref({})
  let getform=ref({
      ProjectCode:store.getters.code,

    })
    let topforms=ref({
      url:require('@/assets/img/home/<USER>'),
      name:'设备统计',
      text:'更多记录',
      lefs:'rigs',
      order:'2'
    })
    let form=ref({})
    let delogs=ref(null)
    let tops=ref([
      {
        name:'',
        value:''
      },
      {
        name:'在线',
        value:'online'
      },{
        name:'离线',
        value:'unline'
      },{
        name:'拆卸',
        value:'disassemble'
      },
    ])
  window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
   onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      topforms.value.lefs=props.homeindex>4?'lefs':'rigs'
      topforms.value.order=props.homeindex>4?'1':'2'
      geteqment()
   })
   const geteqment=async()=>{

    const {data:res}=await gettable('GetEquipStatistic',getform.value)
    // console.log('获取',res);
    
    form.value=res.data
   }
   const opentable=()=>{
    delogs.value.showdelog('1','设备统计')
   }
  return{
  list,
  form,
  bgcolor,
  getform,
  topforms,
  tops,
  delogs,

  geteqment,
  opentable
  }
}
}
</script>
<style lang="scss" scoped>
.Equipment{
  &-two{
    height: 80%;
    color: #fff;
    .tops{
      font-size: 14px;
      margin-top: 3px;
    }
    display: grid;
    grid-template-columns: repeat(4,25%);
    &-left{
      font-size: 12px;
      margin:5px 8px;
      width: 20%;
      p{
        margin-top: -10px;
      }
    }
    &-top{
      display: flex;
      align-items: center;
      grid-column: 1/span 4;
    }
    &-right{
      width: 80%;
      font-size: 30px;
      display: flex;
      justify-content: space-around;
    }
  }
  .offline{
    color: red;
  }
}
</style>