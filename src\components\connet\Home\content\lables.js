import { type } from "jquery"

export function labelist(params) {
    // 表单
    let lablist=[]
    let formcout=[
        {
         name:'项目信息',
         value:[{
            name:'项目名称',
            value:'Name',
            type:'0',
            widths:'',
            },{
            name:'建设单位名称',
            value:'BuildCorpName',
            type:'0',
            widths:'',
            },{
            name:'统一社会信用代码',
            value:'BuildCorpCode',
            type:'0',
            widths:'',
            },{
            name:'项目分类',
            value:'Category',
            type:'0',
            widths:'',
            },{
            name:'施工许可证号',
            value:'PrjPlanNum',
            type:'0',
            widths:'',
            },{
            name:'总承包单位名称',
            value:'ContractorCorpName',
            type:'0',
            widths:'',
            },{
            name:'统一社会信用代码',
            value:'ContractorCorpCode',
            type:'0',
            widths:'',
            },{
            name:'总长度',
            value:'BuildingLength',
            type:'0',
            widths:'',
            },{
            name:'总投资',
            value:'Invest',
            type:'0',
            widths:'',
            },{
            name:'总面积',
            value:'BuildingArea',
            type:'0',
            widths:'',
            },{
            name:'项目状态',
            value:'PrjStatus',
            type:'0',
            widths:'',
            },{
            name:'开工日期',
            value:'StartDate',
            type:'0',
            widths:'',
            },{
            name:'竣工日期',
            value:'CompleteDate',
            type:'0',
            widths:'',
            },{
            name:'联系人',
            value:'LinkMan',
            type:'0',
            widths:'',
            },{
            name:'联系电话',
            value:'LinkPhone',
            type:'0',
            widths:'',
            },{
            name:'项目地址',
            value:'Address',
            type:'0',
            widths:'',
            },{
            name:'经度',
            value:'Lng',
            type:'0',
            widths:'',
            },{
            name:'纬度',
            value:'Lat',
            type:'0',
            widths:'',
            }
         ]
        },{
         name:'参建单位',
         value:[{
               name:'序号',
               value:'rowNum',
               widths:'',
               },{
               name:'单位类型',
               value:'CorpType',
               widths:'',
               },{
               name:'单位名称',
               value:'CorpName',
               widths:'',
               },{
               name:'统一社会信用代码',
               value:'CorpCode',
               widths:'',
               },{
               name:'地址',
               value:'Address',
               widths:'',
               }
         ]
        },{
         name:'设备统计',
         value:[{
               name:'序号',
               value:'rowNum',
               widths:'',
               },{
               name:'设备类型',
               value:'IconType',
               widths:'',
               },{
               name:'备案编号',
               value:'EquipCode',
               widths:'',
               },{
               name:'设备名称',
               value:'EquipName',
               widths:'',
               },{
               name:'生产厂家',
               value:'Manufactor',
               widths:'',
               },{
               name:'出厂时间',
               value:'QualifiedDate',
               widths:'',
               },{
               name:'安装单位',
               value:'Installation',
               widths:'',
               },{
               name:'进场安装时间',
               value:'InstallationDate',
               widths:'',
               },{
               name:'设备状态',
               value:'CraneState',
               widths:'',
               }
         ]
        },{
        name:'设备统计搜索',
        value:[{
               name:'设备类型',
               value:'rowNum',
               type:'2',
               list:[
                {
                name:'塔机',
                value:'10101'
                },{
                name:'升降机',
                value:'10103'
                },{
                name:'卸料',
                value:'10104'
                },
               ],
               widths:'',
               },{
               name:'设备状态',
               value:'CorpType',
               type:'2',
               list:[
                {
                name:'在线',
                value:'1'
                },{
                name:'离线',
                value:'2'
                },{
                name:'未绑定',
                value:'3'
                },
               ],
               widths:'',
               },{
               name:'设备名称',
               value:'CorpName',
               type:'1',
               widths:'',
               }
         ]
        }
        
    ]


    formcout.forEach((item,index)=>{
        if (item.name==params) {
            lablist=item.value
            
        }
    })
    
    return lablist
}