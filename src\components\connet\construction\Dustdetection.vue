<template>
  <!-- 出勤 -->
  <div class="Dustdetection padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="'1'" :horn="1" :form="topforms" @opens="getopen"></Chamfering>

    <div class="Dustdetection-two">
        <div class="Dustdetection-two-imgs" v-for="(item,index) in label" :key="index">
            <img :src="item.src" alt="" style="width:40px;height:40px">
            <p class="icon-text">{{item.name}}</p>
            <div>
              <span class="ycvalue">{{froms[item.value]}}</span>
              <span>{{item.unit}}</span>
            </div>
        </div>
    </div>
    <delog ref="delogss"></delog>
    <Chamfering :homeindex="'1'" :horn="0"></Chamfering>

  </div>
</template>

<script>
// import echartsDustdetection from "@/components/connet/Common/echartscom.vue";
import { nextTick, onMounted, ref,getCurrentInstance } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/devicelist/content/delog.vue";

// import schedulechart from "@/components/connet/Common/echartscom.vue";
export default {
props:['homeindex','materialtype'],
components:{
// echartsDustdetection
delog,
Chamfering
},
setup(props){

  let bgcolor=ref({})
  let label=[{
    name:'TSP',
    value:'TSP',
    unit:'μm/m³',
    src:require('@/assets/img/construction/001.png')
    },{
    name:'PM2.5',
    value:'PM2',
    unit:'μm/m³',
    src:require('@/assets/img/construction/002.png')
    },{
    name:'PM10',
    value:'PM10',
    unit:'μm/m³',
    src:require('@/assets/img/construction/003.png')
    },{
    name:'噪音',
    value:'DB',
    unit:'dB',
    src:require('@/assets/img/construction/004.png')
    },{
    name:'温度',
    value:'WD',
    unit:'℃',
    src:require('@/assets/img/construction/005.png')
    },{
    name:'湿度',
    value:'SD',
    unit:'%RH',
    src:require('@/assets/img/construction/006.png')
    },{
    name:'大气压',
    value:'DQY',
    unit:'kpa',
    src:require('@/assets/img/construction/007.png')
    },{
    name:'风速',
    value:'FS',
    unit:'m/s',
    src:require('@/assets/img/construction/008.png')
    },{
    name:'风向风力',
    value:'FX',
    unit:'3~4级',
    src:require('@/assets/img/construction/009.png')
    },
    
    ]
  let froms=ref({})
  const $http = getCurrentInstance().appContext.config.globalProperties.$http
  const $moist = getCurrentInstance().appContext.config.globalProperties.$moist

  let getform=ref({
      ProjectCode:store.getters.code,
      // PROJECTCODE:store.getters.code
    })
  let topforms=ref({
      url:require('@/assets/img/construction/Dustdetection.png'),
      name:"扬尘在线监测",
      text:'环境告警',
      lefs:'rigs',
      order:'3'
  })
  let delogss=ref(null)
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // firstecaht()
        // second()
        getyctop()
        // console.log('获取请求',getCurrentInstance().appContext.config.globalProperties.$http.post());
        

  })
  const getopen=()=>{
    let GUID={
      name:'环境告警'
    }
    delogss.value.showdelog(GUID)
  }
  
  const getyctop=async()=>{

        const {data:res}=await gettable('GetTodayYCRealInfo',getform.value)
        if (res.code=="1000") {
          froms.value=res.data
        }
        // console.log('获取扬尘数据',res);
        
  }


	return{
    //     option,
    // option0,
    // option1,
    // option2,
    topforms,
    froms,
		getform,
		bgcolor,
    label,
    delogss,
    getopen,

    getyctop,
        // firstecaht,
        // second,
        // thesechart

	}
}
}
</script>

<style lang="scss" scoped>
.Dustdetection{
&-two{
  height: 80%;
  display: grid;
  grid-template-columns: repeat(3,33.3%);
  align-items: center;
  justify-items: start;
&-imgs{
    width: 100%;
    height: 60%;
    display: grid;
    grid-template-columns: 32% 68%;
    justify-items: start;
    color: #fff;
    img{
        grid-row: 1 / span 2;
    }
    .ycvalue{
      font-size: 18px!important;
        color: aqua;

    }
    span{
        font-size: 12px;
        color: #fff;
    }
}
.icon-text{
  font-size: 14px;

}
}
}


</style>