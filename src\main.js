import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import "@/router/permission"
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import "./assets/iconfont/iconfont.css"
import "./assets/iconfont/iconfont.js"
import store from './store'
import axios from 'axios'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import echarts from 'echarts'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import sseion from "@/utils/sseion";
import directive from './components/connet/Common/directive'
import 'echarts-liquidfill'
import moist from "@/views/moist.vue";
import VueLazyload from 'vue-lazyload'
import "./assets/css/glables.scss";
import 'animate.css';
import 'swiper/swiper-bundle.css'
// import vClickOutside from 'v-click-outside';
import EZUIKit from 'ezuikit-js';
import './assets/font/font.css'
import './assets/font/harm.css'
import 'echarts-gl';
import {formatDateTime} from "./utils/date";
import { labelist } from "@/components/connet/personnelcon/content/labeles";
import { colorlist } from "./utils/colorlist";
// 将自动注册所有组件为全局组件
import dataV from '@jiaminghi/data-view'
 
// 引入自定义NProgress样式
// import './assets/css/nprogress.css'


let app =createApp(App)
app.config.globalProperties.$http = axios;
app.config.globalProperties.$echarts = echarts;
app.config.globalProperties.$sseion = sseion;
app.config.globalProperties.$moist = moist;
app.config.globalProperties.$formatDateTime = formatDateTime;
app.config.globalProperties.$labelist = labelist;
app.config.globalProperties.$colorlist = colorlist;

// const $formatDateTime = getCurrentInstance().appContext.config.globalProperties.$formatDateTime


for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }

const loadimage = require('./assets/img/001.jpg')
const errorimage = require('./assets/img/001.jpg')

// 屏幕自适应穿透px为rem模板
const baseSize = 16; // 设计稿根字体大小
function setRem() {
  if (
    document.documentElement.clientWidth >= 762 &&
    document.documentElement.clientWidth <= 2040
  ) {
    const scale = document.documentElement.clientWidth / 1920; // 计算缩放比例
    document.documentElement.style.fontSize =
      baseSize * Math.min(scale, 2) + "px"; // 设置根字体大小
  }
  if (
    document.documentElement.clientWidth >= 300 &&
    document.documentElement.clientWidth <= 762
  ) {
    // const scale = (750) / 1920 // 计算缩放比例
    document.documentElement.style.fontSize = 62.5 + "%"; // 设置根字体大小
    // document.documentElement.style.fontSize = (baseSize * Math.min(scale, 2)) + 'px'
  }
}

setRem();
window.addEventListener("resize", setRem); // 窗口大小改变时重新计算



app.use(directive)
// app.use(VueLazyload)
// app.use(moist)
app.use(VueLazyload, {
  preLoad: 1.3,
  error: errorimage,
  loading: loadimage,
  attempt: 1
})
// app.use(DataV, { classNamePrefix: 'dv-' });
app.use(dataV)

app.use(ElementPlus, {
    locale:zhCn})
// app.use(EZUIKit)

app.use(store).use(router).mount('#app')


