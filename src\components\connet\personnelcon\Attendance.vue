<template>
  <!-- 出勤 -->
  <div class="Attendance padding"  :style="{color:bgcolor.font}">
    <div :class="['Attendance-top','lefticon']"
	:style="{background:'linear-gradient(90deg, '+bgcolor.titlecolor+' 0%, rgba(1, 194, 255, 0) 97%)'}"
	>
        <img v-if="materialtype.type=='人员'" :src="materialtype.src"  >
        <img v-else-if="materialtype.type=='物料'" :src="materialtype.src">
        <span class="padding-text-span">{{materialtype.titles}}</span>
    </div>
    <div class="Attendance-two">
      <div class="Attendance-two-top">
        <div v-for="(item,index) in btsount" :key="index" :class="['Attendance-two-btn cursor',{'changindex':falge==index}]" 
        :style="[falge==index?`background:${bgcolor.changcolor};color:#FFF`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
         rgba(7, 93, 184, 0.6) 100%);color:#FFF`]" @click="change(item,index)"
        >{{item}}</div>
      </div>
      <div class="echatr">
        <attecahrt v-if="echart" :ids="'chartsContent'" :options="addteion" :distances="260"
         @pieClick="handlePieClick" :enableClick="true"></attecahrt>
      </div>
    </div>
    <Chamfering :homeindex="'1'" :horn="0"></Chamfering>
    <delog ref="delogs"></delog>
    <perdelog ref="perdelog"></perdelog>
  </div>
</template>

<script>
// import echartsAttendance from "@/components/connet/Common/echartscom.vue";
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/material/content/delog.vue";
import perdelog from "@/components/connet/personnelcon/content/perdelog.vue";
// import echartsss from "@/components/connet/personnelcon/echarts3d/3dechart.vue";
import attecahrt from "@/components/connet/personnelcon/echarts3d/attecahrt.vue";
export default {
props:['homeindex','materialtype'],
components:{
// echartsAttendance
attecahrt,
Chamfering,
delog,
perdelog
// echartsss
},
setup(props){

  let bgcolor=ref({})
  let falge=ref(0)
  let echart=ref(true)
  let countlist=ref([])
  let getform=ref({
      ProjectCode:store.getters.code,
      Type:'全部'
    })
  let btsount=ref([])
  let btnlist=['全部人员','管理人员','建筑工人']
  let btnlists=['全部','本年度','本月度']

  let addteion=ref([])
  let Attendslist=ref([])
  let delogs=ref(null)
  let perdelog=ref(null)
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      if (props.materialtype.type=="人员") {
        btsount.value=btnlist
	      getsunm()

      }else{
        btsount.value=btnlists
        getmater()
      }
    // getAttendance()
	  // getsunm()
    // getmangen()
    // getconstruction()
  })
// 获取物料系统
  const getmater=async()=>{
    const {data:res}=await gettable('GetReceiveCheck',getform.value)
    // console.log('获取物料',res);
    echart.value=true
    Attendslist.value=res.data.CheckList
    addteion.value=Attendslist.value

    if (addteion.value.length>0) {
      addteion.value[0].itemStyle= {
                // opacity: 0.5,
                color: '#00b7f7' }
      addteion.value[1].itemStyle= {
                // opacity: 0.5,
                color: '#74f3b5' }
    }
  }


  const getsunm=async()=>{
    const {data:res}=await gettable('GetAttendanceStatus',getform.value)
    // console.log('获取考勤',res);
    Attendslist.value=res.data.KQList
    addteion.value=Attendslist.value
    if (addteion.value.length>0) {
      addteion.value[0].itemStyle= {
                // opacity: 0.5,
                color: '#00b7f7' }
      addteion.value[1].itemStyle= {
                // opacity: 0.5,
                color: '#74f3b5' }
    }
    
  }
  const change=(val,idnex)=>{
    falge.value=idnex
    echart.value=false
    // addteion.value=[]
    if (props.materialtype.type=="人员") {
      switch (idnex) {
      
      case 0:
        nextTick(()=>{
          echart.value=true

        addteion.value=Attendslist.value
        // console.log('全部');
        if (addteion.value.length>0) {
          addteion.value[0].itemStyle= {
                color: '#00b7f7' }
          addteion.value[1].itemStyle= {
                color: '#74f3b5' }
        }
        })
        
        break;
      case 1:
        // console.log('管理人员',Attendslist.value[0]);
        nextTick(()=>{
        echart.value=true
        addteion.value=[Attendslist.value[0]]

        })
        break;
      case 2:
        nextTick(()=>{
          echart.value=true

        addteion.value=[Attendslist.value[1]]
        })
        break;
  
    }
    }else{
      getform.value.Type=val
      // nextTick(()=>{
        echart.value=false
      getmater()

      // })
    }
  }
  const handlePieClick = (clickedData) => {
    // console.log('扇区点击',props.materialtype, clickedData);
    switch (props.materialtype.titles) {
      case '物料验收记录':
        // delogs.value.openlog(clickedData)
        if (clickedData.name=='移动验收') {
        delogs.value.showdelog(clickedData,'物料验收记录')
        }else{
          delogs.value.showdelog(clickedData,'智慧地磅')
        }
        break;
      case '在场人数统计':
        // delogs.value.openlog(clickedData)
        if (clickedData.name=='建筑工人') {
          perdelog.value.showdelog(clickedData,'建筑工人')
        }else{
          perdelog.value.showdelog(clickedData,'管理人员')
        }
        break;
    }
  };
 

	return{
    btsount,
		falge,
    echart,
		countlist,
		getform,
		bgcolor,
    btnlist,
    addteion,
    Attendslist,
    delogs,
    perdelog,

    getmater,
    change,
    handlePieClick
	// getAttendance,
	}
}
}
</script>

<style lang="scss" scoped>
.Attendance{
&-two{
  height: 80%;
  &-top{
    font-size: 12px;
    display: grid;
    grid-template-columns: repeat(3,33.3%);
    // margin-top: 10px;
    padding: 10px;
  }
  &-btn{
    padding: 10px;
    width: 80%;
    position: relative;
    border: 2px solid #03558F;
    // margin: 10px;
  }
  .bgcolorcz{
        background: #F29961;

    }
    .bgcolorsj{
          background: #4582ff;

    }
}
}
.changindex::before{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        left: -2px;
        top: -2px; 
        opacity: 1;
        border-top: 2px solid #E0A538;
        border-left: 2px solid #E0A538;
    }
.changindex::after{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        right: -2px;
        bottom: -2px; 
        opacity: 1;
        border-bottom: 2px solid #E0A538;
        border-right: 2px solid #E0A538;
    }
.echatr{
  display: flex;
  align-items: center;
  width: 100%;
  height: 85%;
}
// #echartsBox{
//  width: 100%;
//  height: 100%; 
// }
</style>