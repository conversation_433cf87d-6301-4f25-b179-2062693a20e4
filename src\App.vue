<template>
  <div id="app">
    <!-- <loading-animation :loading="isLoading" /> -->
    <router-view/>
  </div>
</template>
<script>
import devPixelRatio from "@/utils/devicePixelRatio.js";
import { mapGetters } from 'vuex';

export default {
  computed: {
    ...mapGetters(['isLoading'])
  },
  created() {
    new devPixelRatio().init(); 
  }
}
</script>
<style lang="scss">
  @import url("assets/css/base.css");
  @import "./assets/css/comment.scss";

#app {
  font-family: 'HarmonyOS Sans SC';

  // font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #fff;
}
</style>
