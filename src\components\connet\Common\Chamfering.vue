<template>
<div>
  <div v-if="horn=='1'" :class="['weater-top',classname,indexs.includes(homeindex)?'lefticon':'righticon']"
    :style="indexs.includes(homeindex)?
    {background:'linear-gradient(90deg, '+bgcolor.titlecolor+' 0%, rgba(1, 194, 255, 0) 97%)'}:
    {background:'linear-gradient(90deg, rgba(1, 194, 255, 0) 0%,'+bgcolor.titlecolor+' 97%)'}"
    >
      <img :src="form.url" :style="indexs.includes(homeindex)?'order:1':'order:2'" alt="">
      <span class="padding-text-span" :style="indexs.includes(homeindex)?'order:2':'order:1'">{{form.name}}</span>
      <el-button v-if="limt.includes(form.name)" :class="`${form.lefs}`" link  @click="getemits()" :style="`order:${form.order}`"> {{ form.text }}</el-button>
  </div>
  <div v-if="horn=='0'" :class="indexs.includes(homeindex)?'leftbefore':'rightbefore'" 
    :style="{borderColor:bgcolor.chamfer}"></div>
  <div v-if="horn=='0'" :class="indexs.includes(homeindex)?'leftafter':'rightafter'"
     :style="{borderColor:bgcolor.chamfer}"></div>
</div>
</template>

<script>
import store from "@/store";
import { onMounted, ref } from 'vue';
// horn 0 倒角 1顶部
// homeindex 1左侧 4右侧
export default {
props:['homeindex','form','horn','classname'],
  setup(porps,cex){
    let bgcolor=ref({})
    let indexs=ref(['1','2','3'])
    let limt=['设备维保','扬尘在线监测','领用记录','供货商偏差分析','施工日志'
      ,'会议纪要','天气状况','基础信息','现场管理人员','设备统计'
    ]
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // console.log('获取',porps.homeindex,indexs.value.includes(porps.homeindex));
        
    })
    const getemits=()=>{
      // 如果传不了值是需要添加跟标签div
      cex.emit('opens','0')
    }
    return{
        indexs,
        bgcolor,
        limt,

        getemits
    }
    }
}
</script>
<style lang="scss" scoped>
.el-button {
  color: #fff;
  font-weight: bold;
  // margin-right: auto;
}
.lefs{
  margin-right: auto;

}
.rigs{
  margin-left: auto;

}
</style>