<template>
    <el-dialog v-model="dialogTableVisible"   destroy-on-close
      class="delogss" width="80%" :close-on-click-modal="false" @close="closes">
      <selection ref="selections" @colses="closes" :titles="title"></selection>
      <div class="bodybottom">
        <el-scrollbar height="700px" class="monitor">
            <div class="monitor-table cursor" v-for="(item,index) in tableDate" :key="index" @click="play(item,index)">
                <p>{{item.MonitorName}}</p>
            </div>
        </el-scrollbar>
        <div class="delog-table">
          <div class="windows cursor" v-for="(item,i) in 4" :key="item" @click="playwindow(i)" :style="getborder(i)">
              <div class="windows-header" v-if="ids[i]">
                <h3>{{titlelist[i]}}</h3>
                <el-icon class="close" @click="closesign(i)"><CircleCloseFilled /></el-icon>
              </div>
              <div class="windows-play" :id="ids[i]" ></div>
          </div>
        </div>
      </div>
      </el-dialog>
  </template>
  
  <script setup>
  
  import selection from "@/components/connet/Common/selection.vue";
  import picimg from "@/components/connet/Common/picimg.vue";
  import { onMounted, ref, computed, onBeforeUnmount, nextTick } from 'vue';
  import { labelist } from "@/components/connet/technology/content/lables.js";
  import { gettable,setdata,deldata} from "@/network/api/requestnet";
  import store from "@/store";
  import { updateFormListFields } from '@/utils/dataTransform'
  import { ElMessage } from 'element-plus'
  import { globalPlayerManager } from '@/utils/playerManager.js'
  let dialogTableVisible=ref(false)
  let bgcolor=ref({})
  let title=ref('')
  let loading=ref(false)
  let getform=ref({
      page:1,
      count:200,
      CreateTime:'',
      GetSpecialProgramTable:'',
      ProjectCode:store.getters.code,
      InUserName:store.getters.username,
      Type:'掌勤扬尘'
  })
  let falge=ref(-1)
  let widths=['']
  let url=ref('')
  let tableDate=ref([])
  let total=ref(0)
  let ids=ref([])
  let token=ref('')
  let player=ref(null)
  let players=ref([])
  let titlelist = ref([])
  let idcount=ref('')
  
  // 定义 gettablesmodes 变量
  let gettablesmodes = {
    ProjectCode: store.getters.code
  }

  window.addEventListener('setthcolor', ()=> {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
  })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getform.value.ProjectCode=store.getters.code
  })
  
  // 组件销毁前清理资源
  onBeforeUnmount(() => {
    stopAllPlayers();
  })
  
  const showdelog=(val,titleText)=>{
      console.log('titleText',val,titleText);
      title.value=titleText
        switch (titleText) {
            case '视频监控':
                url.value='JK_MonitorMessageSel'
                break;
        }
      getdetable()
      gettoken()
      dialogTableVisible.value=true
  }
  const playwindow=(i)=>{
    // console.log('i',i);
    falge.value=i
    idcount.value = ids.value[i] || ''; // 获取当前窗口的 id

    // console.log('idcount',idcount.value);
    
  }
//    const
   const getdetable=async()=>{ 
      loading.value=true
      const {data:res}=await gettable(url.value,getform.value)
      // console.log('获取数据',res);
      loading.value=false
      tableDate.value=res.data
   }
   const play = (val, index) => {
    // console.log('val', val, index);
    
    // 检查监控是否已经在播放
    const existingIndex = ids.value.findIndex(id => id === val.MonitorCode);
    if (existingIndex !== -1) {
      // 如果已经在播放，则不执行任何操作
      ElMessage.warning('该监控已在播放中');
      return;
    }

    // 初始化数组（如果为空）
    if (ids.value.length === 0) {
      ids.value = Array(4).fill('');
      titlelist.value = Array(4).fill('');
      players.value = Array(4).fill(null);
    }
    
    // 确定目标窗口索引
    const targetIndex = falge.value !== -1 ? falge.value : findTargetWindow();
    
    // 更新窗口内容
    updateWindowContent(val, targetIndex);

    // 创建播放器
    nextTick(() => {
      playvioce(val, targetIndex);
    });
    
    // 重置选择标志
    falge.value = -1;
    // console.log('ids', ids.value);
   }

   // 查找目标窗口
   const findTargetWindow = () => {
    const emptyIndex = ids.value.findIndex(item => !item);
    if (emptyIndex !== -1) return emptyIndex;
    
    // 如果没有空闲窗口，执行轮转
    rotateWindows();
    return ids.value.length - 1;
   }

   // 更新窗口内容
   const updateWindowContent = (val, index) => {
    ids.value[index] = val.MonitorCode;
    titlelist.value[index] = val.MonitorName;
   }

   // 窗口轮转
   const rotateWindows = () => {
    for (let i = 0; i < ids.value.length - 1; i++) {
      ids.value[i] = ids.value[i + 1];
      titlelist.value[i] = titlelist.value[i + 1];
    }
   }

    //   获取token
   const gettoken=async()=>{
        const {data:res}=await gettable('GetElevatorMonitoringToken',getform.value)
        if (res.code=="1000") {
            token.value=res.data.token
        }
   }
  const getborder=(i)=>{
    // console.log('获取边框样式',i);
    if (i == falge.value) {
        return {
            border: '2px solid #77B728',
            // background: '#5698c3'
        }
    }
    return
  }
   
   // 停止所有播放器实例的函数
   const stopAllPlayers = () => {
    //  console.log('开始停止所有播放器');

     // 使用播放器管理工具销毁所有播放器
     globalPlayerManager.destroyAllPlayers();

     // 清空所有相关数据
     players.value = [];
     falge.value = -1;
     ids.value = [];
     titlelist.value = [];
     player.value = null;

    //  console.log('所有播放器停止完成');
   }
   const closesign=(i)=>{
        // console.log('关闭窗口',i,players.value[i]);
        if (players.value[i] && ids.value[i]) {
            // 使用播放器管理工具销毁播放器
            globalPlayerManager.destroyPlayer(ids.value[i]);

            // 清空数组中的引用
            players.value[i] = null;
        }

        // 重新创建 windows-play div
        const windowsContainer = document.querySelector(`.delog-table .windows:nth-child(${i + 1})`);
        if (windowsContainer) {
            // 先移除现有的 windows-play div
            const existingPlayDiv = windowsContainer.querySelector('.windows-play');
            if (existingPlayDiv) {
                existingPlayDiv.remove();
            }

            // 创建新的 windows-play div
            const newPlayDiv = document.createElement('div');
            newPlayDiv.className = 'windows-play';
            newPlayDiv.id = ''; // 初始为空，等待新的监控分配
            windowsContainer.appendChild(newPlayDiv);
        }

        // 清空对应的数据
        ids.value[i] = '';
        titlelist.value[i] = '';
    }
   const playvioce=(val, windowIndex)=>{
        // console.log('播放',ids.value[windowIndex],players.value[windowIndex]);
        if (val.FluentLink) {
            // 如果提供了窗口索引，则为特定窗口创建播放器
            if (windowIndex !== undefined) {
                // 确保 ids 数组中有值
                if (ids.value.length === 0) {
                    ids.value = Array(4).fill('');
                    titlelist.value = Array(4).fill('');
                    players.value = Array(4).fill(null);
                }
                // 先检查并停止该窗口可能存在的播放器
                if (players.value[windowIndex] && ids.value[windowIndex]) {
                    // 尝试更换播放URL
                    if (globalPlayerManager.changePlayerUrl(ids.value[windowIndex], val.FluentLink)) {
                        return; 
                    } else {
                        // 如果不支持更换URL，则销毁当前播放器，重新创建
                        globalPlayerManager.destroyPlayer(ids.value[windowIndex]);
                        players.value[windowIndex] = null;
                    }
                }
                // 确保 DOM 元素存在且有正确的 id
                const playDiv = document.querySelector(`.delog-table .windows:nth-child(${windowIndex + 1}) .windows-play`);
                if (playDiv && ids.value[windowIndex]) {
                    playDiv.id = ids.value[windowIndex];
                    // 使用播放器管理工具创建播放器
                    const player = globalPlayerManager.createPlayer({
                        id: ids.value[windowIndex],
                        url: val.FluentLink,
                        accessToken: token.value,
                        width: playDiv.clientWidth || 300,
                        height: playDiv.clientHeight || 200,
                        template: "simple",
                        handleError: (res) => {
                            console.log('播放错误回调', res);
                            // 播放出错时清理播放器实例
                            if (players.value[windowIndex]) {
                                players.value[windowIndex] = null;
                                ids.value[windowIndex] = '';
                                titlelist.value[windowIndex] = '';
                            }
                        }
                    });

                    if (player) {
                        players.value[windowIndex] = player;
                        // console.log(`播放器 ${windowIndex} 创建成功`);
                    } else {
                        // console.error(`创建播放器 ${windowIndex} 失败`);
                        // 创建失败时清理相关数据
                        players.value[windowIndex] = null;
                        ids.value[windowIndex] = '';
                        titlelist.value[windowIndex] = '';
                    }
                } else {
                    // console.error('播放容器未找到或 id 未设置', windowIndex, ids.value[windowIndex]);
                }
            }
        }
    }

  const closes = () => {
        stopAllPlayers();
        dialogTableVisible.value = false;
    //   }
  };
  
  
  defineExpose({
    showdelog
  })
  </script>
  <style lang="scss" scoped>
  
.bodybottom{
    display: flex;
    height: 100%;
}
.monitor{
    width: 25%;
    color: #fff;
    &-table{
        height: 40px;
        border: 1px solid #fff;
        border-radius: 5px;
        margin: 10px;
        background: #2177b8;
    }
    &-table:hover{
        background: #0f59a4;
    }
    p{
        margin: 10px;
        text-align: left;
    }
}
.close{
  font-size: 40px;
  color: #fff;
  position: absolute;
  right: -10px;
  top: -10px;
}
:deep(.windows-play){
  width: 100%;
  height: 100%;
}
.windows{
    height: 350px;
    width: 100%;
    position: relative;
    border: 1px solid #fff;
    &-play{
        width: 100%;
        height: 100%;
    }
    &-header{
      color: #fff;
        height: 30px;
        // background: #5698c3;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 999;
        display: flex;
        align-items: center;
    }
}
.delog-table{
    width: 75%;
    display: grid;
    grid-template-columns: repeat(2,1fr);
    gap: 10px;
}
// .monitor-table{
//     width: 100%;
// }
  </style>