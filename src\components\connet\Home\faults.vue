<template>
  <!-- 故障 -->
  <div class="faults padding" :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="topforms" @opens="opentable()"></Chamfering>

    <div class="faults-content">
      <div v-for="(item,index) in project" :key="index" class="faults-content-one">
        <span class="faults-content-ones">{{item.name}}</span>
        <span class="faults-content-one2">{{form[item.value]}}{{index==0?'万元':(index==1?'m²':'')}}</span>
      </div>
    </div>
    <delog ref="delogs"></delog>
    <Chamfering :homeindex="homeindex" :horn="0"></Chamfering>
  </div>
</template>

<script>
import { onMounted, ref } from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/Home/content/delog.vue";

export default {
  props:['homeindex'],
components:{
  Chamfering,
  delog
},
  setup(props){
    window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
   let getform=ref({
      ProjectCode:store.getters.code,

    })
    let form=ref({})
    let topforms=ref({
      url:require('@/assets/img/home/<USER>'),
      name:'基础信息',
      text:'更多信息',
      lefs:'lefs',
      order:'2'
    })
    let delogs=ref(null)
   onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      topforms.value.lefs=props.homeindex>4?'lefs':'rigs'
      topforms.value.order=props.homeindex>4?'1':'2'
    getjcdet()
   })
   let bgcolor=ref({})
   let project=ref([
      {
        name:'项目造价',
        value:'Invest'
      },{
        name:'建筑面积',
        value:'BuildingArea'
      },{
        name:'建筑工期',
        value:'ProjectCycle'
      },{
        name:'建筑单位',
        value:'ContractorCorpName'
      },{
        name:'设计单位',
        value:'CorpName011'
      },{
        name:'监理单位',
        value:'CorpName007'
      },{
        name:'勘察单位',
        value:'CorpName010'
      },{
        name:'施工单位',
        value:'CorpName006'
      },
    ])
    const opentable=()=>{
    delogs.value.showdelog(0,'基础信息')
   }
    const getjcdet=async()=>{
    const {data:res}=await gettable('GetProjectIndex',getform.value)
    // console.log('返回',res);
    
    if (res.code=="1000") {
      form.value=res.data[0]
    }
    }

    return{
      form,
      project,
      bgcolor,
      topforms,
      delogs,
      getjcdet,
      opentable
    }
  }
}
</script>
<style lang="scss" scoped>
.faults{
    // padding: 10px;
    // color: #03FBFF;
    &-top{
   span{
        // opacity: 1;
        // font-size: 16px;
        // // font-weight: bold;
        // // line-height: 28px;
        // letter-spacing: 0px;

        // color: #03FBFF;
      }
    }
    &-content{
        // font-family: '思源黑体';
        font-size: 14px;
        // font-weight: 500;
        line-height: 21px;
        // text-align: right;
        letter-spacing: 0px;
        // color: #03FBFF;
      &-ones{
        // color: #03FBFF;
        }
      &-one{
        display: flex;
        justify-content: space-between;
        margin: 6px;
      }
      &-one2{
        color: #fff;
      }
    }
}


</style>