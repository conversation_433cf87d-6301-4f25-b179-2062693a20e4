<template>
  <div class="home">
    <!-- <i class="iconfont icon-fangda cursor" v-if="amplifyindex==0" @click="amplifyopen(1)"
     :style="{color:bgcolor.titlecolor,left:amplifyindex==0?'20%':'2%'}" title="放大"></i>
    <i class="iconfont icon-suoxiao cursor" v-else-if="amplifyindex==1" @click="amplifyopen(0)"
     :style="{color:bgcolor.titlecolor,left:amplifyindex==0?'20%':'2%'}" title="缩小"></i> -->

    <div class="patrol">
        <img src="@/assets/img/safety/bgimg.png" alt="" style="width:200px">
        <div class="patrol-one">
            <p>巡更点位：<span class="location cursor" @click="openprojec()">查询巡更点位记录</span></p>
            <p>巡更人员：{{form.patrolmanName}}</p>
        </div>
    </div>

    <img v-for="(item,index) in imglist" @mouseenter="mouseenter(item,index)" @mouseleave="mouseleave(item,index)"
    :key="index" class="imgpoting cursor" v-show="amplifyindex==0?item.XPosition>'20'&&item.XPosition<'80':item"
     :src="falge==index?item.src1:item.src" 
    :style="`top:${item.YPosition}%;left:${item.XPosition}%`" alt="" srcset="" @click="open(item)">
    
    
    <el-dialog v-model="dialogTableVisible"  
    class="delogss" width="50%" >
    <selection ref="selections" @colses="closes" :titles="'巡更详情'"></selection>

    <div class="safety-one bodybottom">
        <div  v-for="(item,index) in titles" :key="index" :class="['addtence'+index]">
            <div  :class="['datedelog-header','attendance'+index]"
            :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
                rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
                <div class="datedelog-body-one">
                    <img src="@/assets/img/home/<USER>" alt="">
                    <p class="datedelog-p">{{item}}</p>
                </div>
            </div>
            <div v-if="item=='实时巡更人员'" class="safety-one-img">
                <p class="padding " v-for="(ps,iss) in lableleft" :key="iss">{{ps.name}}：{{ form[ps.value] }}</p>
                <img :src="'data:image/jpeg;base64,'+form.HeadImage" alt="" style="width:100px;height:150px">
            </div>
            <div v-else-if="item=='巡更点位'">
               <p class="padding " v-for="(ps,iss) in list" :key="iss">巡更点位{{iss+1}}：{{ps.PatrolPointName}}</p>
            </div>
            <div v-else-if="item=='考勤记录'" >
                <el-table :data="tableDate"  :style="['width: 100%',`color:${bgcolor.font};
                    --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
                    :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
                    empty-text="暂无数据"
                    >
                        <template #empty>
                            <el-empty  v-loading="loading"></el-empty>
                        </template>
                        <el-table-column width="90" prop="rowNum" label="序号" align="center"> </el-table-column>
                        <el-table-column prop="PatrolTime" label="巡更时间" align="center"> </el-table-column>
                        <el-table-column prop="PatrolPointName" label="巡更点位" align="center"> </el-table-column>
                        <el-table-column prop="PatrolWorker" label="巡更人员" align="center"> </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="getform.count"
                    v-model:page-size="getform.page"
                    :page-sizes="[100, 200, 300, 400]"
                    :background="background"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>
    </div>

    </el-dialog>
  </div>
</template>

<script>
import { computed, onMounted, reactive, ref,watch } from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import selection from "@/components/connet/Common/selection.vue";

export default {
props:['showcontent'],

components:{
        // deloglist,
        // datamodel
        selection
    },
setup(props,ctx){
    let imglist=ref(
        [{
        src:require('@/assets/img/material/weighbridge.png'),
        src1:require('@/assets/img/material/weighbridge1.png'),
        IconType:'塔式起重机',
        YPosition:'30',
        XPosition:'50'
    },
    ]
    )
    let titles=['项目进度照片','巡更点位','考勤记录']
    let tableDate=ref([
    ])
    let falge=ref(-1)
    // let falge1=ref(0)
    let bgcolor=ref({})
    let showfalge=ref(-1)//更多
    let loading=ref(false)
    let dialogTableVisible=ref(false)
    let getform=ref({
        ProjectCode:store.getters.code,
        date:'',
        BegPatrolTime: "",
        EndPatrolTime: "",
        InUserName: "",
        PatrolPointNo: "",
        PatrolWorker: "",
        page:1,
        count:10
        // UsingPage:'首页'
    })
    let total=ref(0)

    // let labelserch=ref([{
    // ])
    const background = ref(false)
    let lableleft=ref([
    {
        name:'员工姓名',
        value:'patrolmanName'
    },{
        name:'员工性别',
        value:'Gender'
    },{
        name:'员工年龄',
        value:'Age'
    },{
        name:'所在类型',
        value:'WorkerType'
    },{
        name:'所在工种',
        value:'GNAME'
    },{
        name:'所在班组',
        value:'TeamName'
    },{
        name:'手机号码',
        value:'CellPhone'
    },
    ])
    let amplifyindex=ref(0)
    let list=ref([])
    let form=ref({})

    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        getform.value.ProjectCode=store.getters.code
        getform.value.InUserName=store.getters.username
        getbulid()
    })
    const open=(item)=>{
        // console.log('打开',item);
        
        // showfalge.value=0
        // dialogTableVisible.value=true
            
    }
    const getbulid=async()=>{
      const {data:res}=await gettable('GetPatrolRealDataInfo',getform.value)
    //   console.log('获取',res);
      
        if (res.code=="1000") {
            form.value=res.data
        }
    }
    const getpoint=async()=>{
            // console.log('巡更点位', res);
      const {data:res}=await gettable('GetAllPatrolPoint',getform.value)
            if (res.code=="1000") {
                list.value=res.data
            }
    }
    const gettables=async()=>{
        loading.value=true
      const {data:res}=await gettable('GetPatrolRealDataTable',getform.value)
        loading.value=false
        tableDate.value=res.data
        total.value=res.Total
    }
    const openprojec=()=>{
        gettables()
        getpoint()
        dialogTableVisible.value=true
    }

    const closes=()=>{
        dialogTableVisible.value=false

    }
    const mouseenter=(e,index)=>{
        falge.value=index
    }

    const mouseleave=(e,index)=>{
        falge.value=-1

    }

    const amplifyopen=(val)=>{
        amplifyindex.value=val
        // emit.
        ctx.emit("getamplify", val);
    }
    // const change=(val,index)=>{
    //     falge1.value=index
    // }
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
    const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        gettables()

        }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        getform.value.page=val
        gettables()
        }

    return{
        getform,
        titles,
        dialogTableVisible,
        amplifyindex,
        lableleft,
        // form,
        bgcolor,
        showfalge,
        imglist,
        falge,
        loading,
        background,
        // tower,
        tableDate,
        // labelserch,
        total,
        list,
        form,

        mouseenter,
        mouseleave,
        // getmore,
        close,
        open,
        closes,

        amplifyopen,
        openprojec,
        // yukline,
        // change,
        tableRowClassName,
        handleSizeChange,
        handleCurrentChange,
        getbulid,
        getpoint,

        
    }
}
}
</script>

<style lang="scss" scoped>
.home{
    width: 100%;
    height: 100%;
    .btn{
    margin: 10px;
    width: 20%;
    font-size: 20px;
    font-weight: bold;
    }
}
// 使用深度选择器确保样式能应用到Element Plus组件
:deep(.delogss) {
    padding: 0px !important;

    .el-dialog__header {
        padding: 0px !important;
    }

    .el-dialog__body {
        padding: 0px !important;
    }

    .el-dialog__footer {
        padding: 0px !important;
    }
}

// 备用方案：更具体的选择器
:deep(.el-dialog.delogss) {
    padding: 0px !important;

    .el-dialog__header,
    .el-dialog__body,
    .el-dialog__footer {
        padding: 0px !important;
    }
}
.safety{
    &-one{
        display: grid;
        grid-template-columns: repeat(2,50%);
        color: #03FBFF;
        text-align: start;
        &-img{
            display: grid;
            grid-template-columns: 70% 30%;
            img{
                grid-row: 1/span 7;
                grid-column: 2;
            }
        }
    }
}
.attendance2,.addtence2{
    grid-area: 2/ span 3/2 span;
}
.location{
    color: #03FBFF;
}

.iconfont{
    font-size: 45px!important;
    position: absolute;
    left: 2%;
}
.imgpoting{
    position: absolute;
    width: 65px;
    height: 75px;
}
.patrol{
    position: fixed;
    left: 18%;
    top: 7%;
    &-one{
        font-size: 12px;
        position: fixed;
        text-align: start;
        left: 20%;
        top: 7.5%;
    }
}
.tower:hover{
    background-image: url('@/assets/img/home/<USER>/tower1.png');
    width: 65px;
    height: 75px;
}

.changindex::before{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        left: -2px;
        top: -2px; 
        opacity: 1;
        border-top: 2px solid #E0A538;
        border-left: 2px solid #E0A538;
    }
.changindex::after{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        right: -2px;
        bottom: -2px; 
        opacity: 1;
        border-bottom: 2px solid #E0A538;
        border-right: 2px solid #E0A538;
    }
</style>