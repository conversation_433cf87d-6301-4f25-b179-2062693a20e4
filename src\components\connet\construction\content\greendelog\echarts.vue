<template>
  <div class="echartsid" id="linesid"></div>
</template>

<script>
import { nextTick, onBeforeUnmount, onMounted,watch,ref,getCurrentInstance } from 'vue';
export default {
props:['forms'],
setup(porps){
  const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist
  const $colorlist = getCurrentInstance().appContext.config.globalProperties.$colorlist

    let myChart=null
    onMounted(()=>{})
    let series=ref([])
    let names=ref([])
    let legends=ref([])
    let units=''
    let getform=ref({
        Type:''
    })
    let leftlist=['扬尘监控测量记录表','PM2.5、PM10监控测量记录表']
    let yaxios={//阈值
        yclist: '1.5',
        pm10List: '0.075',
        pm25List:'0.15',
        zylist:'70',
        zylist1:'55',
        wslist:'9',
        wslist1:'6',
        echart:'',
    }
    let linename={
        yclist: '扬尘警戒目标高度',
        pm10List: 'PM10警戒目标值',
        pm25List: 'PM2.5警戒目标值',
        zylist:'昼间噪音限值',
        zylist1:'夜间噪音限值',
        wslist:'PH值上限',
        wslist1:'PH值下限',
        echart:'',

    }
    let lengname={
        yclist: '扬尘监控测量记录',
        pm10List: 'PM10数据监测',
        pm25List: 'PM2.5数据监测',
        zylist:'昼间噪音测量',
        zylist1:'',
        wslist:'PH值',
        wslist1:'PH值',
        echart:'',
    }
    let ychs=['回收直接利用','回收加工再利用','回收运出后利用']
    let yAxis1=ref({
            type: 'value'
        })
    let typeline=[]
    let colorlist=[]
    let visualMap=[]
    let typevalue='严重污染天气记录表'
    const showdelog=(val)=>{
        // console.log('获取对象',val);
        let keys1=Object.keys(val?.data)
        series.value=[]
        names.value=[] 
        getform.value.Type=val.Type
        linename.echart=val.Type//获取鼠标移动数据
        lengname.echart=val.Type//获取单独标签
        units=''
        myChart?.dispose()
        keys1.forEach((item,index)=>{
            typeline[index]={
                type:'line',
                name:'',
                i:0
            }
        })
        yAxis1.value={
            type: 'value'
        }
        switch (val.Type) {
            case '扬尘监控测量记录表':
                units='m'
                break;
            case 'PM2.5、PM10监控测量记录表':
                units='mg/m³'
                break;
            case '噪声监控测量记录表':
                units='db'
                break;
            case '污水监控测量记录表':
                units='PH'
                break;
            case '可回收建筑垃圾管理记录表':
                keys1.map((item,index)=>{
                    linename[item]=ychs[index]
                    lengname[item]=ychs[index]
                    yaxios[item]=''
                })
                break;
            case '现场扬尘控制洒水记录表':
            case '隔油池清理记录表':
            case '化粪池清理记录表':
                units='次'
                break;
            case '有毒有害垃圾管理记录表':
            case '项目用水记录及工程用水总量汇总表':
            case '施工用电记录表及工程用电总量汇总表':
                keys1.map((item,index)=>{
                    linename[item]=val.WasteList[index]
                    lengname[item]=val.WasteList[index]
                    yaxios[item]=''
                })
                // console.log('项目用水',typeline);
                break;
            case '基坑降水收集记录表':
                getyaxios(val)
                keys1.map((item,index)=>{
                    linename[item]=val.WasteList[index]
                    lengname[item]=val.WasteList[index]
                    yaxios[item]=''
                })
                typeline=[{
                    name:'#0000FF',
                    color:'#00FFFF',
                    type:'bar'
                    },{
                    name:'#FF9900',
                    color:'#FFFF00',
                    type:'bar'
                    },{
                    name:'',
                    color:'',
                    type:'line'
                    },]
                break;
            case '中水回用记录表':
                getyaxios(val)
                keys1.map((item,index)=>{
                    linename[item]=val.WasteList[index]
                    lengname[item]=val.WasteList[index]
                    yaxios[item]=''
                })
                typeline=[{
                    name:'#0000FF',
                    color:'#00FFFF',
                    type:'bar',
                    i:0
                    },{
                    name:'',
                    color:'',
                    type:'line',
                    i:1
                    },]
                break;
            case '雨水回用记录表':
                getyaxios(val)
                keys1.map((item,index)=>{
                    linename[item]=val?.WasteList[index]??''
                    lengname[item]=val?.WasteList[index]??''
                    yaxios[item]=''
                })
                typeline=$labelist('雨水回用记录表图表')
                break;
            case '大型机械保养记录表':
                units='维保设备数量'
                keys1.map((item,index)=>{
                    linename[item]=val?.WasteList[index]??''
                    lengname[item]=val?.WasteList[index]??''
                    yaxios[item]=''
                })
                typeline=[
                    {
                    name:'#006600',
                    color:'#66FF00',
                    type:'bar'
                    },{
                    name:'#9900FF',
                    color:'#FFCCFF',
                    type:'bar'
                    },{
                    name:'#0000FF',
                    color:'#00FFFF',
                    type:'bar'
                    },
                ]
                break;
            case '石化气燃料使用台账表':
                // units='维保设备数量'
                getyaxios(val)

                keys1.map((item,index)=>{
                    linename[item]=val?.WasteList[index]??''
                    lengname[item]=val?.WasteList[index]??''
                    yaxios[item]=''
                })
                typeline=[
                    {
                    name:'#006600',
                    color:'#66FF00',
                    type:'bar',
                    i:0

                    },{
                    name:'#9900FF',
                    color:'#FFCCFF',
                    type:'bar',
                    i:0
                    },{
                    name:'#0000FF',
                    color:'#00FFFF',
                    type:'bar',
                    i:1
                    },
                ]
                break;
            case '严重污染天气记录表':
                units=''
                getyaxios(val)
                keys1.map((item,index)=>{
                    linename[item]=''
                    lengname[item]='AQI空气质量指数'
                    // yaxios[item]=''
                })
                yaxios=[
                    {
                        yAxis:50
                    },{
                        yAxis:100
                    },{
                        yAxis:150
                    },{
                        yAxis:200
                    },{
                        yAxis:300
                    },{
                        yAxis:500
                    },
                ]
                visualMap={
                    top: 50,
                    right: 10,
                    pieces: [
                    {
                        gt: 0,
                        lte: 50,
                        color: '#93CE07'
                    },
                    {
                        gt: 50,
                        lte: 100,
                        color: '#FBDB0F'
                    },
                    {
                        gt: 100,
                        lte: 150,
                        color: '#FC7D02'
                    },
                    {
                        gt: 150,
                        lte: 200,
                        color: '#FD0100'
                    },
                    {
                        gt: 200,
                        lte: 300,
                        color: '#AA069F'
                    },
                    {
                        gt: 300,
                        lte: 500,
                        color: '#AC3B2A'
                    }
                ],
                outOfRange: {
                color: '#999'
                }
                }

                break;
        }
        getserir(val,val.falge)
        getecahrts()
    }
    const getyaxios=(val,value)=>{
        yAxis1.value=[
            
            {
            type: 'value',
            name: `${val.laberbar}`,
            min: 0,
            max: val.max2,
            width:'10px',
            order: 0,
            },
            {
            type: 'value',
            name: `${val.laberline}`,
            min: 0,
            max: val.max1,
            order: 1,
            }
        ]
    }
    const getserir=(val,value)=>{
        // console.log('获取数据',val,typeline);
        let echarts = require('echarts');

        series.value=[]
        names.value=[]
            let keys=Object.keys(val.data)
            names.value=val.data[keys[0]]?.map((item)=>{
                return item.name
            })
            series.value=keys.map((item,i)=>{
                // console.log('获取AQI数据',item);
                return {
                    name:lengname[item],
                    type: typeline[i].type,
                    data: val.data[item]?.map((item)=>{
                        return item.value
                    }),
                    yAxisIndex: yAxis1.value.length>1?(typeline[i].i):0,
                    itemStyle: {
                        borderRadius: [20, 20, 0, 0],
                        color:typeline[i].type=='bar'?new echarts.graphic.LinearGradient(
                            0, 0, 0, 1, // x1, y1, x2, y2 指定渐变的起点和终点位置
                            [
                                {offset: 0, color: typeline[i]?.color},   // 渐变起始颜色
                                {offset: 1, color: typeline[i]?.name}   // 渐变结束颜色
                            ]
                        ):$colorlist()[i]
                    },
                    
                    markLine: {
                        symbol: ['none', 'none'],
                        lineStyle: {
                            color: 'red'
                        },
                        label:{
                            show: true,
                            position:'insideEndTop',
                            formatter: linename[item],
                            color: 'red'
                        },
                        data: val.Type=='严重污染天气记录表'?yaxios: [{
                            yAxis: yaxios[item],
                            label:{
                                show: true,
                                position:'insideEndTop',
                                formatter: linename[item],
                                color: 'red'
                            }
                        },{
                            yAxis: yaxios[item],
                            label:{
                                show: true,
                                position:'start',
                                distance:8,
                                formatter: yaxios[item],
                                color: 'red'
                            }
                        },
                        ],
                    }
                }
            })
            legends.value=keys.map((item)=>{return lengname[item]})
            // console.log('获取颜色渐变',series.value);
    }
    const getecahrts=()=>{
        let echarts = require('echarts');
        myChart = echarts.getInstanceByDom(document.getElementById('linesid'))
         if (myChart == null) {
            myChart = echarts.init(document.getElementById('linesid'));
         }

      let option = {
        title: {
            left:'2%',
            text: porps.forms.toptwo,
            subtext: units
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            right:'10%',
            data: legends.value
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: true,
            axisLine:{
                show:true,
            },
            data: names?.value
        },
        visualMap: typevalue=='严重污染天气记录表'?visualMap:{},
        yAxis: yAxis1?.value,
        series: series?.value
        };

        myChart?.setOption(option)

        window.addEventListener("resize", function() {
          myChart?.resize();
        });
    }
    onBeforeUnmount(()=>{
        myChart?.dispose()
    })
    return{
        myChart,
        series,
        names,
        units,
        yaxios,
        linename,
        lengname,
        getform,
        leftlist,
        yAxis1,
        typeline,
        colorlist,
        visualMap,
        typevalue,

        getecahrts,
        getserir,
        showdelog,
        getyaxios,
    }
}
}
</script>
<style lang="scss" scoped>
.echartsid{
    width: 100%;
    height: 100%;
}
</style>