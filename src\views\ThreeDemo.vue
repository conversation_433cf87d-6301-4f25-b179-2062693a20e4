<template>
  <div class="three-demo">
    <ThreeScene ref="ThreeSceneRef" />
    <!-- <ThreeJS ref="ThreeJSRef"></ThreeJS> -->
    <!-- <button @click="sendMessageToUnity">发送消息到Unity</button> -->
  </div>
</template>

<script setup>
import ThreeScene from '@/components/ThreeScene.vue'
// import ThreeJS from '@/components/connet/Common/ThreeJS.vue'
import { onMounted, ref } from 'vue'
// let ref = ref(null)
let ThreeJSRef = ref(null)
onMounted(() => {
  // ThreeJSRef.value.loadModel('/assets/models/001.glb')
})
// const sendMessageToUnity = () => {
//   ThreeSceneRef.value?.sendToUnity('来自Vue的消息')
// }
</script>

<style scoped>
.three-demo {
  width: 100%;
  height: 100vh;
}
</style> 