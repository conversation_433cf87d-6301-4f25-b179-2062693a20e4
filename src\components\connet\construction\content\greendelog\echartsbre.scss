@mixin heighs($margin){
    border-radius: 10px;
    margin: $margin 0;
    padding: 10px;
    height: 100px;
    width: 100%;
    display: grid;

}
@mixin column($column){
    grid-column: $column;
    grid-row: 2/span 3;
    font-size: 20px;
    font-weight: bold;
}
@mixin radius(){
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.rightbox{
    display: grid;
    grid-template-columns: repeat(2,50%);
    &-top{
        grid-column: 1/span 2;
        span{
            margin: 10px;
            font-size: 20px;
            font-weight: bold;
        }
    }
}
.bieechats{
    width: 100%;
    height: 60vh;
}
.righttable{
    color: #fff;
    @include heighs(10px);
    span{
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 18px;
        margin: 5px 0;
    }
}
.percentage{
    width: 100%;
    height: 20px;
    line-height: 20px;
    background: #97CF8A;
    grid-row: 2;
    grid-column: 1/span 5;
    padding: 5px 10px;
    @include radius();
    margin: 15px 0;
    overflow: visible; // 允许子元素溢出显示
    &-one{
        background: #C77C2B;
        height: 10px;
        position: absolute;
        right: 0; // 定位到最右侧
        // @include radius();
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;

        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #E68A3B;
            transform: scaleY(1.2);
        }
    }
    &-two{
        background: #2129FA;
        height: 10px;
        position: absolute;
        left: 0; // 定位到最左侧
        z-index: 1;
        @include radius();
        // border-top-left-radius: 10px;
        // border-bottom-left-radius: 10px;
    }
    &-box{
        width: 100%;
        height: 100%;
        position: relative;
        overflow: visible; // 允许子元素溢出显示
    }
}
.box{
    background: #E5EDEF;
    @include heighs(10px);
    align-items: center;
    grid-template-columns: repeat(5,20%)
}
.mater2{
    @include column(1);
    height: 30px!important;

}
.maters0{
    grid-column: span 2;
}
.maters2{
    @include column(2);
    height: 30px!important;
}
.bottom{
    grid-row: 3;
    grid-column: 1/span 5;
    text-align: center;
    color: #409eff;
}
.potion-text{
    display: inline-block;
    position: absolute;
}
// .tops0::after{
//     width: 2px;
//     height: 10px;
//     content: '';
//     display: inline-block;
//     position: absolute;
//     background: #C77C2B;
// }

.tops0{
    top: -25px;
    color: #C77C2B;
    &::after{
        width: 1px;
        height: 20px;
        top: 15px;
        content: '';
        display: inline-block;
        position: absolute;
        background: #C77C2B;
    }
}

.tops1{
    bottom: -20px;
    color: red;
    &::after{
        width: 1px;
        height: 20px;
        top: -10px;
        content: '';
        display: inline-block;
        position: absolute;
        background: red;
    }
}

.p0{
    font-size: 18px;
    font-weight: bold;
}
.Collarring-one{
    // width: 100%;
    margin: 10px;
    padding: 10px;
}
.reduces{
    width:100%;
    height: 150px;
    display: grid;
    padding: 10px;
    color: #fff;
    grid-template-columns: 40% 60%;
    p:nth-child(1){
        grid-row: 1;
        grid-column: 1/span 2;
        font-size: 18px;
        font-weight: bold;
    }
    p:nth-child(6){
        grid-row: 4;
        grid-column: 1/span 2;
    }
}
.waterbox{
    background: #80E6FF;
    height: 160px;
    border-radius: 10px;
    &-one{
        border: 1px solid #3057FF;
        border-radius: 10px;
        height: 100%;
        display: grid;
        grid-template-columns:repeat(2,50%);
        img{
            grid-row: 1/span 5;
            grid-column: 1;
        }
        p:nth-child(2){
            font-size: 18px;
            font-weight: bold;
        }
        p{
            padding: 5px 10px;
        }
    }
}
#pres{
    width: 200px;
    height: 200px;
}
#bres{
    width: 100%;
    height: 100%;
}
.allbox{
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 20% 80%;
    
    &-one{
        margin: 10px;
        height: 150px;
        border-radius: 10px;
        color: #fff;
        p:nth-child(1){
            font-size: 18px;
        }
        p:nth-child(2),:nth-child(3){
            display: inline-block;
        }
        p{
            margin:8px 10px
        }
    }
}
.threed,.bres{
    height: 200px;
    width: 100%;
}
.threed{
    display: grid;
    grid-template-columns: 30% 70%;
    position: relative;
    .text{
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        p{
            margin:10px;
        span{
            display: inline-block;
            font-size: 16px;
            margin: 0 10px;
        }
        }
    }
    .h3{
        position: absolute;
        left: 0;
        top: 0;
    }
}
.twobar{
    width: 100%;
    display: grid;
    grid-template-columns:repeat(2,50%)!important;
}
.listpie{
    grid-row: 2;
    grid-column: 1/span 2;
}
.boxtwo{
    display: grid;
    grid-template-columns: repeat(2,50%);
    h3{
        grid-column: 1/span 2;
    }
    &-scro{
    grid-row: 3;
    }
    .righttable{
        width: 90%!important;
        grid-template-columns: 80% 20%;
        span:nth-child(1){
            grid-column: 1/span 2;
            font-size: 16px;
            font-weight: bold;

        }
        span:nth-child(4){
            grid-row: 2/span 3;
            grid-column: 2;
            font-size: 16px;
            font-weight: bold;
        }
    }
}
.boxid{
    grid-column: 1;
grid-row: 2/span 2;
}
.boxsect{
    height: 90px;
    width: 100%;
    background-image: url('@/assets/img/construction/bgimg/002.svg');
    background-size: 100%;
    grid-row: 2;
    grid-column: 2;
    display: grid;
    grid-template-columns: repeat(2,50%);
    color: #fff;
}


// 动画效果
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}