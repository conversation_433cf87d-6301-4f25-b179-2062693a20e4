<template>
  <!-- 生产产值 -->
  <div class="produce padding" :style="{color:bgcolor.font}" >
    <Chamfering :homeindex="homeindex" :horn="1" :form="forms"></Chamfering>

    <div class="produce-two">
      <div class="produce-two-top">
        <div class="produce-two-top1">
          <span class="produce-two-top1-span">总计划产值：{{form.sumOutputPlan}}例</span>
          <span class="produce-two-top1-span">总实际产值：{{form.sumOutputActual}}</span>
          <span class="produce-two-top1-span"> 完成率：{{form.CompleteRate}}%</span>
          <div class="produce-two-top1-lend">
            <div class="produce-two-top1-lend">
              <div class="produce-two-top1-lend1 bgcolorcz" ></div>
              <span>计划产值</span>
            </div>
            <div class="produce-two-top1-lend">
              <div  class="produce-two-top1-lend1 bgcolorsj"></div>
              <span>实际产值</span>
            </div>
          </div>
        </div>
 
      </div>
      <div class="echatr">
        <div id="echartsBox" ref="echartsBox"></div>
      </div>
    </div>
     <Chamfering :homeindex="homeindex" :horn="0"></Chamfering>
  </div>
</template>

<script>
import baseCharts from "@/components/connet/Common/baseCharts.vue";
import doubleecaharts from "@/components/connet/Common/echartscom.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import { onMounted, ref,getCurrentInstance } from 'vue';
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
export default {
props:['homeindex'],
components:{
  baseCharts,
  doubleecaharts,
  Chamfering
},
setup(){
    const baseChartsRef = ref(null)
    const baseChartsRefPlus = ref(null)
    let options=ref({})
   let bgcolor=ref({})
   let forms=ref({
    url:require('@/assets/img/home/<USER>'),
    name:'生产产值'
   })
    let getform=ref({
      ProjectCode:store.getters.code,
      
    })
    let form=ref({})
    window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(() => {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getdatas()
      // getEchart()
      // // 大小可调整
      // baseChartsRefPlus.value.defalutOptions.xAxis.data = ['深圳', '广州', '汕头']
      // baseChartsRefPlus.value.defalutOptions.series[0].data = [100, 200, 300]
      // baseChartsRefPlus.value.defalutOptions.series[0].data = [100, 200, 300]
      // baseChartsRefPlus.value.setOptions(baseChartsRefPlus.value.defalutOptions, [
      //   "#0079e4", // 正面颜色渐变
      //   "#000af5", // 正面颜色渐变
      //   "#36c9ff", // 侧面颜色渐变
      //   "#003efa", // 侧面颜色渐变
      //   "#0099e4", // 顶部颜色渐变
      //   "#4a9df7", // 顶部颜色渐变
      //   "rgba(0,31,117,0.3)", // 底部颜色渐变
      //   "rgba(0,153,228,0.3)" // 底部颜色渐变
      // ], { offsetX: 36, sliderWidth: 16 }) // 正面宽度 ， 侧面宽度

    })
    const getdatas=async()=>{
      const {data:res}=await gettable('GetProductionValueByYear',getform.value)
        // console.log('获取数据',res);
        if (res.code=="1000") {
          form.value=res.data
          getEchart()

        }
    }
    const getEchart=()=>{
        var echarts = require('echarts');
    //   const pageInstance = getCurrentInstance();
    // // 获取dom节点对象
    // let myChart = pageInstance.refs.echartsBox;
    // echarts.getInstanceByDom()
    let myChart = echarts.getInstanceByDom(document.getElementById('echartsBox'));
      if (myChart == null) {
          // myChart = echarts.init(context.refs[refsid.value]);
      myChart = echarts.init(document.getElementById('echartsBox'));

        }



      
            var option;
            const sideData = form.value.OutputPlanList
            const sideData1 = form.value.OutputActualList

            option = {
              // color:['#0B7AF1','#4582ff'],
                tooltip: {
                    trigger: 'axis',
                     formatter(params) {
                        let val0 = params[0]["value"];
                        let val1 = params[1]["value"];
                        let circle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;left:5px;background-color:`;
                        let data0 = `${circle}#00DEEE"></span> ${
                          params[0]["seriesName"]
                        }: ${val0}`;
                        let data1 = `${circle}#4582ff"></span> ${
                          params[1]["seriesName"]
                        }: ${val1}`;
                        return `${params[0].axisValueLabel}<br/>${data0}<br/>${data1}`;
                      }
                },
                grid: {
                    top: '5%',
                    left:'2%',
                    bottom: '2%',
                    right: '8%',
                    containLabel: true
                },
                toolbox: {
                    show: true,
                },
                calculable: true,
                xAxis: [
                    {
                        type: 'category',
                        splitLine: {
                            show: false
                        },
                        data: ['三月', '四月', '五月', '六月', '七月', '八月'],
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#fff" //X轴文字颜色
                            },
                        },
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        splitLine: {
                            show: false
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#fff" //X轴文字颜色
                            },
                        },
                    }
                ],
                series: [
                    {
                        name: '实际产值',
                        tooltip: {
                            show: false
                        },
                        type: 'bar',
                        barWidth: 10,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                                    offset: 0,
                                    color: "#0B7AF1" // 0% 处的颜色
                                },
                                //  {
                                //     offset: 0.6,
                                //     color: "#2d8cf0" // 60% 处的颜色
                                // },
                                 {
                                    offset: 1,
                                    color: "#00DEEE" // 100% 处的颜色
                                }], false)
                            }
                        },
                        data: sideData,
                        barGap: 0,
                    }, {
                        name: '实际产值',
                        type: 'bar',
                        barWidth: 10,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                                   offset: 0,
                                    color: "#10509E" // 0% 处的颜色
                                },
                                //  {
                                //     offset: 0.6,
                                //     color: "#2d8cf0" // 60% 处的颜色
                                // },0DAFE5
                                 {
                                    offset: 1,
                                    color: "#0DAFE5" // 100% 处的颜色
                                }], false)
                            }
                        },
                        barGap: 0,
                        data: sideData,
                        label: {
                            show: true,
                            position: 'top',
                            textStyle: {
                                color: 'white',
                                fontSize: 10
                            }
                        }
                    }, {
                        name: '实际产值',
                        tooltip: {
                            show: false
                        },
                        type: 'pictorialBar',
                        itemStyle: {
                            borderWidth: 1,
                            borderColor: '#0571D5',
                            color: '#03FFEA' // 控制顶部方形的颜色
                        },
                        symbol: 'path://M 0,0 l 90,0 l -60,60 l -90,0 z',
                        symbolSize: ['19', '7'], // 第一个值控制顶部方形大小
                        symbolOffset: ['-10', '-4'], // 控制顶部放行 左右和上下
                        symbolRotate: -16,
                        symbolPosition: 'end',
                        data: sideData,
                        z: 3,
                    },
                    {
                        name: '计划产值',
                        tooltip: {
                            show: false
                        },
                        type: 'bar',
                        barWidth: 10,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                                    offset: 0,
                                    color: "#6757f1" // 0% 处的颜色
                                },
                                //  {
                                //     offset: 0.6,
                                //     color: "#11ad0a" // 60% 处的颜色4582ff
                                // },
                                 {
                                    offset: 1,
                                    color: "#638afa" // 100% 处的颜色
                                }], false)
                            }
                        },
                        data: sideData1,
                        barGap: 0,

                    }, {
                        name: '计划产值',
                        type: 'bar',
                        barWidth: 10,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                                    offset: 0,
                                    color: "#582ee8" // 0% 处的颜色
                                },
                                //  {
                                //     offset: 0.6,
                                //     color: "#26ec10" // 60% 处的颜色
                                // },
                                 {
                                    offset: 1,
                                    color: "#4582ff" // 100% 处的颜色
                                }], false)
                            }
                        },
                        barGap: 0,
                        data: sideData1,
                        label: {
                            show: true,
                            position: 'top',
                            textStyle: {
                                color: 'white',
                                fontSize: 10
                            }
                        }
                    }, {
                        name: '计划产值',
                        tooltip: {
                            show: false
                        },
                        type: 'pictorialBar',
                        itemStyle: {
                            borderWidth: 1,
                            borderColor: '#fff',
                            color: '#4c70fc' // 顶部方块的颜色
                        },
                        symbol: 'path://M 0,0 l 90,0 l -60,60 l -90,0 z',
                        symbolSize: ['19', '7'], // 第一个值控制顶部方形大小
                        symbolOffset: ['10', '-4'], // 控制顶部放行 左右和上下
                        symbolRotate: -16,
                        symbolPosition: 'end',
                        data: sideData1,
                        z: 3,

                    }]
            };

        // options.value=option
            // myChart.setOption(option, true);
            myChart.setOption(option,true);
            window.addEventListener("resize", function() {
              // console.log('适应数据');
              
              myChart.resize();
               window.onresize = function(){
                myChart.resize();
              }
            });
           
    }
    
    return {
      bgcolor,
      options,
      form,
      getEchart,
      baseChartsRef,
      baseChartsRefPlus,
      forms,
      getdatas
    };
}
}
</script>
<style lang="scss" scoped>
.produce{
&-two{
    height: 82%;
    &-top1{
      display: flex;
      flex-wrap: wrap;
      font-size: 12px;
      color: #A8D6FF;
      margin: 5px;
      &-span{
        display: inline-block;
        width: 40%;
        margin: 5px;
      }
      &-lend{
        display: flex;
        align-items: center;
        margin: 0 7px;
      }
      &-lend1{
          width: 18px;
          height: 10px;
          margin: 0 3px;
          // background: #000;
          border-radius: 20%;
        }
    }
    .bgcolorcz{
          background: #00DEEE;

    }
    .bgcolorsj{
          background: #4582ff;

    }
  }
}
.echatr{
  width: 100%;
  height: 80%;
}
#echartsBox{
 width: 100%;
 height: 100%; 
}
.schart-box {
  display: inline-block;
  margin: 20px;
}
.schart {
  width: 600px;
  height: 400px;
}
.content-title {
  clear: both;
  font-weight: 400;
  line-height: 50px;
  margin: 10px 0;
  font-size: 22px;
  color: #1f2f3d;
}
</style>