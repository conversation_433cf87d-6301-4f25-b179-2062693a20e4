<template>
  <el-dialog v-model="dialogTableVisible"   destroy-on-close
    class="delogss" :width="'50%'">
    <selection ref="selections" @colses="closes"></selection>
    <div class="datedelog bodybottom" :style="`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`">
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
          <div class="datedelog-body-one">
              <img src="@/assets/img/home/<USER>" alt="">
              <p class="datedelog-p">{{dates}}施工日志</p>
              <div class="cursor-box" v-if="username.includes(getform.InUserName)">
                <el-icon class="cursor icon" :style="getcolor(count,0)" @click="getnext(list,0)" :disabled="count==0"><ArrowLeftBold /></el-icon>
                <span>{{count+1}}/{{list.length}}</span>
                <el-icon class="cursor icon" :style="getcolor(count,1)" @click="getnext(list,1)" :disabled="count==list.length-1"><ArrowRightBold /></el-icon>
              </div>
          </div>
        </div>
        <div class="datedelog-content" v-if="falge==0">
            <div v-for="(item,index) in formlable" :key="index" :class="`text`" :style="getstyle(item)">
                <span>{{item.name}}：</span>
                <span v-if="index!==6">{{addform[item.value]}}</span>
                <img v-else-if="index==6" :src="addform[item.value]" class="cursor" @click="pic(addform[item.value])" alt="" style="width:180px;height:180px">
            </div>
        </div>
        <div v-else-if="falge==1" class="datedelog-content1">
            <p class="cursor" @click="opens()">当天未上传施工日志！</p>
        </div>

    </div>
    <picimg ref="picimgs"></picimg>
    </el-dialog>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import store from "@/store";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import selection from "@/components/connet/Common/selection.vue";
import picimg from "@/components/connet/Common/picimg.vue";

// export default {
//   components:{
//     selection
//   },
    // setup(){
        let bgcolor=ref({})
        let dialogTableVisible=ref(false)
        let formlable=[
            {
                name:'日志日期',
                value:'ConstructionDate'
            },{
                name:'星期',
                value:'Week'
            },{
                name:'气象',
                value:'Climate'
            },{
                name:'风力风向',
                value:'WindDirectionPower'
            },{
                name:'气温',
                value:'AirTemperature'
            },{
                name:'记录内容',
                value:'RecordContent'
            },{
                name:'照片',
                value:'ConstructionPhoto'
            },{
                name:'尚待解决问题',
                value:'Problems'
            },{
                name:'是否闭环',
                value:'ClosedLoop'
            },{
                name:'记录人',
                value:'Recorder'
            }

        ]
        let getform=ref({
            InUserName:store.getters.username
        })
        let picimgs=ref(null)
        let falge=ref(0)
        let addform=ref({})
        let dates=ref('')
        let widths=['记录内容','运单编号','尚待解决问题']
        let list=ref([])
        let count=ref(0)
        let username=ref(['新盛建设740'])
        
        window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        })
        onMounted(()=>{
            bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        })
        const showdelog=(val,value)=>{
            // console.log('返回',val,value);
            count.value=0
            dates.value=value.Date||value.ConstructionDate
            falge.value=val
            getdetil(value)
            dialogTableVisible.value=true
        }
        // 关闭
        const closes=()=>{
            dialogTableVisible.value=false
        }
        const pic=(val)=>{
            let imgtype = ['png', 'jpg', 'jpeg', 'gif', 'bmp']
        if (val) {
            let lastIndex = val.lastIndexOf('.')
            let file = val.substring(lastIndex + 1).toLowerCase()
            if (imgtype.includes(file)) {
                // console.log('获取',val);
                picimgs.value.piclist(val)
            } else {
                let type = val.substring(val.lastIndexOf('.') + 1).toLowerCase()
                
                if (type !== 'mp4') {
                    window.open('https://f.zqface.com/?fileurl=' + val, '_slef')
                }
            }
        }
        }
        const getdetil=async(val)=>{
        let GUID={
            GUID:val.GUID,
            ConstructionDate:val.Date||val.ConstructionDate,
            ProjectCode:store.getters.code,
        }
        const {data:res}=await gettable('GetConstructionLogWeatherDetail',GUID)
        // console.log('获取',res);
            if (res.code=="1000") {
                addform.value=Array.isArray(res.data) ? res.data[0] : res.data
                list.value=res.data
            }
        }
        // 获取下一页
        const getnext=(val,index)=>{
            if(list.value.length>=1){
                if(index === 0 && count.value > 0) {
                    count.value--;
                } else if(index === 1 && count.value < list.value.length-1) {
                    count.value++;
                }
                addform.value=list.value[count.value]
            }
        }
        const getcolor=(val,index)=>{
            if((index === 0 && count.value === 0) || (index === 1 && count.value === list.value.length-1)){
                return 'color: #CCC;'
            }else{
                return 'color: #01C2FF;'
            }
        }
        const getstyle=(item)=>{
            if (widths.includes(item.name)) {
                return 'grid-column: 1/2 span;'
            }
        }
        const opens=()=>{
            window.location.href='https://ai.zqface.com/manage/#/administrations/Constructionlog'
        }
        defineExpose({
            showdelog
        })
        
    // }
    // }
// }
</script>
<style lang="scss">
.personwqmit{
    margin-top: 2%!important;
    margin-bottom: 2%!important;
    // background: rgba(2, 193, 253, 0.24)!important;
    opacity: 1;
    box-sizing: border-box!important;
    .el-dialog__header{
        display: none!important;
    }
    .el-dialog__body{
        padding: 10px!important;
    }
    
}
</style>
<style lang="scss" scoped>
:root {
    --title-color: #fff;
    --delog-color: 0, 48, 70;
    --hover-color: #0346b0;
    --font-color: #fff;
}
.datedelog-content{
    color: #fff;
    display: grid;
    grid-template-columns: repeat(2,50%);
    justify-items: start;
    &-p{
        margin: 10px;
    }
    .text{
        padding: 10px;
        display: inline-block;
    }
}
.datedelog-body-one{
    width: 100%;
    // display: flex;
    // justify-content: space-between;
}
.icon{
    font-size: 20px;
}
.cursor-box{
    margin-left: 10%;
    display: flex;
    align-items: center;
    span{
        font-size: 20px;
    }
}
.cursor{
    color: #fff;
}
</style>