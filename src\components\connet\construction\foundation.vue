<template>
  <!-- 基坑监测 -->
  <div class="foundation padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="'1'" :horn="1" :form="topforms"></Chamfering>

    <div class="foundation-two">
      <div class="foundation-two-top">
        <div class="foundation-two-top-ecahrts">
          <p class="digit">350</p>
          <founcharts :ids="'founcharts'" :options="option"></founcharts>
        </div>
        <el-scrollbar class="scrollbar" height="80px">
          <div class="lables foundation-two-label" v-for="(item,index) in rightlabe" :key="index">
            <div class="lables-box" :style="`background:${WorkTypecolor[index]}`"></div>
            <div>{{item.name}}({{item.value}})</div>
          </div>
        </el-scrollbar>
      </div>
      <div class="foundation-two-bottom">
        <div class="tablebg" v-for="(item,index) in tablelable"  :key="index">
          <div class="title">{{item.name}}</div>
          <div class="title1">{{index>3?JKData2[item.value]:JKData1[item.value]}}</div>
        </div>
      </div>
    </div>
  <Chamfering :homeindex="'1'" :horn="0"></Chamfering>
  
  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import founcharts from "@/components/connet/personnelcon/echarts3d/workerkq.vue";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

import store from "@/store";
export default {
props:['homeindex','materialtype'],
components:{
// echartsfoundation
Chamfering,
founcharts
},
setup(props){

  let bgcolor=ref({})
  let option=ref([
    ],)
  let WorkTypecolor= [
			"#407fff",'#1F9DF5','#21F5D6','#5c2223','#eea2a4',
			"#a682e6",'#b598a1','#c08eaf','#813c85','#806d9e',
			"#e15d68",'#5e616d','#3170a7','#8fb2c9','#c3d7df',
			"#f29961",'#12a182','#737c7b','#92b3a5','#1a6840',
			"#00cccd",'#bec936','#373834','#5bae23','#e4bf11',
			"#dedede",'#b78d12','#f0d695','#b4a992','#fa5d19',
			"#FE8463",'#de7622','#f1908c','#207f4c','#22a2c3',
			"#9BCA63",'#815c94','#e16c96','#12a182','#bec936',
			'#D7504B', '#C6E579', '#F4E001', '#F0805A', '#26C0C0',
			'#FFB7DD', '#660077', '#FFCCCC', '#FFC8B4', '#550088',
			'#FFFFBB', '#FFAA33', '#99FFFF', '#CC00CC', '#FF77FF',
			'#C63300', '#9955FF', '#66FF66', '#129393', '#395203',
			'#C1232B', '#B5C334', '#FCCE10', '#E87C25', '#27727B',
			'#FAD860', '#F3A43B', '#60C0DD', '#0D7CAA'
		]
  let topforms=ref({
      url:require('@/assets/img/construction/foundation.png'),
      name:"基坑监测"
    })
  let getform=ref({
      ProjectCode:store.getters.code,
      InUserName:store.getters.username,
      Type:1
    })
  let tablelable=ref([
    {
      name:'桩顶水平位移',
      value:'value1'
    },
    {
      name:'桩顶垂直位移',
      value:'value2'
    },{
      name:'测斜',
      value:'value3'
    },{
      name:'道路沉降',
      value:'value4'
    },{
      name:'周围建筑沉降',
      value:'value1'
    },{
      name:'管线沉降',
      value:'value2'
    },{
      name:'水位',
      value:'value3'
    },{
      name:'轴力',
      value:'value4'
    },
  ])
  let rightlabe=ref([])
  let JKData1=ref([])
  let JKData2=ref([])
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // firstecaht()
        // second()
        // thesechart()
        gettablesjk()

  })
  // GetJKMonitorStatistics
  const gettablesjk=async()=>{
    const {data:res}=await gettable('GetJKMonitorStatistics',getform.value)
      // console.log('返回基坑数据',res);
      if (res.code=="1000") {
        rightlabe.value=res.data.ECharts
        JKData1.value=res.data.JKData1[0]
        JKData2.value=res.data.JKData2[0]
      option.value= rightlabe.value.map((item,index)=>{
          return {
            name: item.name,
            value: item.value,
            itemStyle: {
                color: WorkTypecolor[index],
            },
          }
        })
      }
  }
  const getjk=async()=>{
    // const {data:res}=await gettable('GetJKMonitorStatistics',getform.value)

  }


	return{
    option,
    tablelable,
    // option1,
    // option2,
		getform,
		bgcolor,
    rightlabe,
    WorkTypecolor,
    JKData1,
    JKData2,
    topforms,

    gettablesjk,
    getjk,

	}
}
}
</script>

<style lang="scss" scoped>
.foundation{
  .scrollbar{
    grid-row: 1;
    grid-column: 2/span 3;
  }
&-two{
  height: 85%;
  &-top{
    display: grid;
    grid-template-columns: 30% 70%;
    height: 40%;
    &-ecahrts{
      width: 100%;
      height: 100%;
      position: relative;
      // grid-row: 1/4 span;
      .digit{
        position: absolute;
        font-weight: bold;
        letter-spacing:3px;
        top:28%;
        left: 32%;
        z-index: 1;
      }
    }
    
  }
  &-bottom{
      height: 60%;
      display: grid;
      grid-template-columns: repeat(4,25%);
      grid-template-rows: repeat(2,50%);
      align-items: center;
      border: 1px solid #0E9CFF;
      .title{
        font-size: 13px;
        background: rgba(15, 43, 63, 0.6)!important;
        border-bottom: 1px solid #0E9CFF;
      }
      .title1{
        font-size: 12px;
      }
      .tablebg{
        height: 100%;
        width: 100%;
        div{
          height: 50%;
          display: flex;
    align-items: center;
    justify-content: center;
        }
      }
    }
  .lables{
    font-size: 12px;
    &-box{
      width: 12px;
      height: 6px;
      padding: 2px;
      margin-right: 10px;
      border-radius: 10px;
      background: #fff;
    }
  }
 
  &-label{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 3px;
  }
}
}


</style>