<template>
  <div class="echartsid" :id="ids"></div>
</template>

<script>
import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
let myChart
export default {
props:['options','ids','types'],
    setup(porps){
    let form=ref({})
     
    onMounted(()=>{
        // getecahrts()
    })
    const showdelog=()=>{
        form.value=porps.types
        nextTick(()=>{
        getecahrts()

        })
    }
    const getecahrts=()=>{
        let echarts = require('echarts');
        myChart = echarts.getInstanceByDom(document.getElementById(porps.ids))
         if (myChart == null) {
            myChart = echarts.init(document.getElementById(porps.ids));
         }

        let option = {
            tooltip: {
                trigger: 'axis'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
            },
            
            yAxis: {
                type: 'category',
                data: porps.options.map((item)=>{
                        return item.name
                })
            },
            series: {
                data: porps.options.map((item)=>{
                        return item.value
                    }),
                type: form.value.type,
                label: {
                    show: true, // 显示数据标签
                    position: 'right' // 在顶部显示
                },
                itemStyle: {
                    borderRadius: [0, 20, 20, 0],// 上左、上右、下右、下左的半径
                    color:function (params) {
                        return new echarts.graphic.LinearGradient(
                        0, 0, 1, 0, // x1, y1, x2, y2 指定渐变的起点和终点位置
                        [{offset: 0, color: form.value.colorlist[params.dataIndex]?.name},   // 渐变起始颜色
                        {offset: 1, color: form.value.colorlist[params.dataIndex]?.color}   // 渐变结束颜色
                        ]
                        )
                    }
                },
            }
            };

        myChart?.setOption(option)

        window.addEventListener("resize", function() {
          myChart?.resize();
        });
    }
    onBeforeUnmount(()=>{
        myChart?.dispose()

    })
        return{
            form,
            getecahrts,
            showdelog
        }
    }
}
</script>
<style lang="scss" scoped>
.echartsid{
    width: 100%;
    height: 100%;
}
</style>