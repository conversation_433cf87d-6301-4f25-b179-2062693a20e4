"use strict";(self["webpackChunksmart"]=self["webpackChunksmart"]||[]).push([[85],{71085:function(e,t,a){a.d(t,{Z:function(){return M}});var i=a(73396),s=a(87139);const n=["src"],o=["src"],l={class:"padding-text-span"},r={class:"Attendance-two"},c={class:"Attendance-two-top"},u=["onClick"],d={class:"echatr"};function p(e,t,a,p,m,h){const g=(0,i.up)("attecahrt"),f=(0,i.up)("Chamfering"),v=(0,i.up)("delog"),y=(0,i.up)("perdelog");return(0,i.wg)(),(0,i.iD)("div",{class:"Attendance padding",style:(0,s.j5)({color:p.bgcolor.font})},[(0,i._)("div",{class:(0,s.C_)(["Attendance-top","lefticon"]),style:(0,s.j5)({background:"linear-gradient(90deg, "+p.bgcolor.titlecolor+" 0%, rgba(1, 194, 255, 0) 97%)"})},["人员"==a.materialtype.type?((0,i.wg)(),(0,i.iD)("img",{key:0,src:a.materialtype.src},null,8,n)):"物料"==a.materialtype.type?((0,i.wg)(),(0,i.iD)("img",{key:1,src:a.materialtype.src},null,8,o)):(0,i.kq)("",!0),(0,i._)("span",l,(0,s.zw)(a.materialtype.titles),1)],4),(0,i._)("div",r,[(0,i._)("div",c,[((0,i.wg)(!0),(0,i.iD)(i.HY,null,(0,i.Ko)(p.btsount,((e,t)=>((0,i.wg)(),(0,i.iD)("div",{key:t,class:(0,s.C_)(["Attendance-two-btn cursor",{changindex:p.falge==t}]),style:(0,s.j5)([p.falge==t?`background:${p.bgcolor.changcolor};color:#FFF`:`background: linear-gradient(108deg, ${p.bgcolor.bgcolor} 8%,\n         rgba(7, 93, 184, 0.6) 100%);color:#FFF`]),onClick:a=>p.change(e,t)},(0,s.zw)(e),15,u)))),128))]),(0,i._)("div",d,[p.echart?((0,i.wg)(),(0,i.j4)(g,{key:0,ids:"chartsContent",options:p.addteion,distances:260,onPieClick:p.handlePieClick,enableClick:!0},null,8,["options","onPieClick"])):(0,i.kq)("",!0)])]),(0,i.Wm)(f,{homeindex:"1",horn:0}),(0,i.Wm)(v,{ref:"delogs"},null,512),(0,i.Wm)(y,{ref:"perdelog"},null,512)],4)}var m=a(44870),h=a(57597),g=a(24239),f=a(98917),v=a(99734),y=a(72478),b=a(67700),k={props:["homeindex","materialtype"],components:{attecahrt:b.Z,Chamfering:f.Z,delog:v.Z,perdelog:y.Z},setup(e){let t=(0,m.iH)({}),a=(0,m.iH)(0),s=(0,m.iH)(!0),n=(0,m.iH)([]),o=(0,m.iH)({ProjectCode:g.Z.getters.code,Type:"全部"}),l=(0,m.iH)([]),r=["全部人员","管理人员","建筑工人"],c=["全部","本年度","本月度"],u=(0,m.iH)([]),d=(0,m.iH)([]),p=(0,m.iH)(null),f=(0,m.iH)(null);window.addEventListener("setthcolor",(()=>{t.value=JSON.parse(sessionStorage.getItem("themecolor"))})),(0,i.bv)((()=>{t.value=JSON.parse(sessionStorage.getItem("themecolor")),"人员"==e.materialtype.type?(l.value=r,y()):(l.value=c,v())}));const v=async()=>{const{data:e}=await(0,h.rT)("GetReceiveCheck",o.value);s.value=!0,d.value=e.data.CheckList,u.value=d.value,u.value.length>0&&(u.value[0].itemStyle={color:"#00b7f7"},u.value[1].itemStyle={color:"#74f3b5"})},y=async()=>{const{data:e}=await(0,h.rT)("GetAttendanceStatus",o.value);d.value=e.data.KQList,u.value=d.value,u.value.length>0&&(u.value[0].itemStyle={color:"#00b7f7"},u.value[1].itemStyle={color:"#74f3b5"})},b=(t,n)=>{if(a.value=n,s.value=!1,"人员"==e.materialtype.type)switch(n){case 0:(0,i.Y3)((()=>{s.value=!0,u.value=d.value,u.value.length>0&&(u.value[0].itemStyle={color:"#00b7f7"},u.value[1].itemStyle={color:"#74f3b5"})}));break;case 1:(0,i.Y3)((()=>{s.value=!0,u.value=[d.value[0]]}));break;case 2:(0,i.Y3)((()=>{s.value=!0,u.value=[d.value[1]]}));break}else o.value.Type=t,s.value=!1,v()},k=t=>{switch(e.materialtype.titles){case"物料验收记录":"移动验收"==t.name?p.value.showdelog(t,"物料验收记录"):p.value.showdelog(t,"智慧地磅");break;case"在场人数统计":"建筑工人"==t.name?f.value.showdelog(t,"建筑工人"):f.value.showdelog(t,"管理人员");break}};return{btsount:l,falge:a,echart:s,countlist:n,getform:o,bgcolor:t,btnlist:r,addteion:u,Attendslist:d,delogs:p,perdelog:f,getmater:v,change:b,handlePieClick:k}}},S=a(40089);const w=(0,S.Z)(k,[["render",p],["__scopeId","data-v-7895708e"]]);var M=w},67700:function(e,t,a){a.d(t,{Z:function(){return u}});var i=a(73396);const s={class:"container"},n=["id"];function o(e,t,a,o,l,r){return(0,i.wg)(),(0,i.iD)("div",s,[(0,i._)("div",{id:a.ids,style:{width:"100%",height:"100%"}},null,8,n)])}a(57658);var l={props:["ids","options","distances","enableClick"],emits:["pieClick"],setup(e,{emit:t}){let s=!1;const n=()=>{setTimeout((()=>{s=!1}),300)};function o(e,t,a,i,s,n,o){let l=(e+t)/2,r=e*Math.PI*2,c=t*Math.PI*2,u=l*Math.PI*2;1===o&&(r=0),0===e&&1===t&&(a=!1),s=1;let d=a?.1*Math.cos(u):0,p=a?.1*Math.sin(u):0,m=i?1.05:1;return{u:{min:-Math.PI,max:3*Math.PI,step:Math.PI/32},v:{min:0,max:2*Math.PI,step:Math.PI/20},x:function(e,t){return e<r?d+Math.cos(r)*(1+Math.cos(t)*s)*m:e>c?d+Math.cos(c)*(1+Math.cos(t)*s)*m:d+Math.cos(e)*(1+Math.cos(t)*s)*m},y:function(e,t){return e<r?p+Math.sin(r)*(1+Math.cos(t)*s)*m:e>c?p+Math.sin(c)*(1+Math.cos(t)*s)*m:p+Math.sin(e)*(1+Math.cos(t)*s)*m},z:function(e,t){return e<.5*-Math.PI||e>2.5*Math.PI?Math.sin(e):0===o?Math.sin(t)>0?4:1:Math.sin(t)>0?1:-1}}}function l(e,t,a){for(let i=0;i<e.series.length;i++)"surface"===e.series[i].type&&(e.series[i].pieStatus.selected=!1,e.series[i].parametricEquation=o(e.series[i].pieData.startRatio,e.series[i].pieData.endRatio,!1,e.series[i].pieStatus.hovered,e.series[i].pieStatus.k,e.series[i].pieData.value,i));return t>=0&&t<e.series.length&&"surface"===e.series[t].type&&(e.series[t].pieStatus.selected=a,e.series[t].parametricEquation=o(e.series[t].pieData.startRatio,e.series[t].pieData.endRatio,a,e.series[t].pieStatus.hovered,e.series[t].pieStatus.k,10,t)),e}function r(t,a){let i=[],s=0,n=0,l=0,r=[],c="undefined"!==typeof a?(1-a)/(1+a):1/3;for(let e=0;e<t.length;e++){s+=t[e].value;let a={name:"undefined"===typeof t[e].name?`series${e}`:t[e].name,type:"surface",parametric:!0,wireframe:{show:!1},pieData:t[e],pieStatus:{selected:!1,hovered:!1,k:c}};if("undefined"!=typeof t[e].itemStyle){let i={};"undefined"!=typeof t[e].itemStyle.color&&(i.color=t[e].itemStyle.color),"undefined"!=typeof t[e].itemStyle.opacity&&(i.opacity=t[e].itemStyle.opacity),a.itemStyle=i}i.push(a)}for(let e=0;e<i.length;e++)l=n+i[e].pieData.value,i[e].pieData.startRatio=n/s,i[e].pieData.endRatio=l/s,i[e].parametricEquation=o(i[e].pieData.startRatio,i[e].pieData.endRatio,!1,!1,c,10,e),n=l,r.push(i[e].name);let u={tooltip:{formatter:e=>{if("mouseoutSeries"!==e.seriesName&&"pie2d"!==e.seriesName){let t="";return u.series[u.series.length-1].data.forEach((a=>{a.name==e.seriesName&&(t=a.value)})),`${e.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${e.color};"></span>${t}`}}},legend:{data:r,icon:"circle",textStyle:{color:"#fff",fontSize:10}},xAxis3D:{min:-1,max:1},yAxis3D:{min:-1,max:1},zAxis3D:{min:-1,max:1},grid3D:{show:!1,boxHeight:20,bottom:"10%",viewControl:{distance:e.distances,alpha:40,beta:80,autoRotate:!1,damping:1,zoomSensitivity:0,rotateSensitivity:0}},series:i};return u}(0,i.YP)((()=>e.options),((e,t)=>{e&&(0,i.Y3)((()=>{d()}))}),{immediate:!0});let c=null,u=null;const d=()=>{var i=a(30197);let o=i.getInstanceByDom(document.getElementById(e.ids));null==o&&(o=i.init(document.getElementById(e.ids)));let d=r(e.options,0);d.series.push({name:"pie2d",type:"pie",labelLine:{length:30},label:{position:"inner",opacity:1,fontSize:12,lineHeight:20,color:"#fff",formatter:"{b} \n{c}"},startAngle:-40,clockwise:!1,radius:"80%",data:e.options,itemStyle:{opacity:0}}),c=o,u=d,o.setOption(d),!1!==e.enableClick&&(o.off("click"),o.off("legendselectchanged"),o.on("click",(function(a){if(!s&&"series"===a.componentType&&"surface"===a.seriesType&&"mouseoutSeries"!==a.seriesName&&"pie2d"!==a.seriesName){s=!0;const i={name:a.seriesName,seriesIndex:a.seriesIndex,value:a.seriesIndex<e.options.length?e.options[a.seriesIndex].value:null};let r=l(JSON.parse(JSON.stringify(u)),a.seriesIndex,!0);o.setOption(r),u=r,t("pieClick",i),n()}})),o.on("legendselectchanged",(function(a){if(s)return;s=!0;let i=-1;for(let e=0;e<u.series.length;e++)if(u.series[e].name===a.name&&"surface"===u.series[e].type){i=e;break}if(-1!==i){const s={name:a.name,seriesIndex:i,value:i<e.options.length?e.options[i].value:null};let n=l(JSON.parse(JSON.stringify(u)),i,!0);o.setOption(n),u=n,t("pieClick",s)}n()}))),window.addEventListener("resize",(function(){o.resize()}))};return{getshwo:d}}},r=a(40089);const c=(0,r.Z)(l,[["render",o],["__scopeId","data-v-65d8c70a"]]);var u=c}}]);