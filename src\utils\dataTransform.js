/**
 * 将数据转换为标准格式
 * @param {Array} data 原始数据数组
 * @param {Object} options 转换选项
 * @param {string} options.nameField 名称字段，默认为 'WorkerName'
 * @param {string} options.valueField 值字段，默认为 'WorkerName'
 * @returns {Array} 转换后的数据数组
 */
export function transformToStandardFormat(data, options = {}) {
  const { nameField = 'WorkerName', valueField = 'WorkerName' } = options;
  
  return data.map(item => ({
    ...item,
    name: item[nameField],
    value: item[valueField]
  }));
}

// const transformedData = transformToStandardFormat(originalData, {
//     nameField: 'CustomName',
//     valueField: 'CustomValue'
//   });
  
//   // 更新表单列表
//   const updatedFormList = updateFormListFields(
//     formList,
//     ['责任人', '复查人'],
//     data
//   );

/**
 * 更新表单列表中的特定字段
 * @param {Array} formList 表单列表
 * @param {Array} targetFields 目标字段数组
 * @param {Array} data 要转换的数据
 * @param {Object} options 转换选项
 * @returns {Array} 更新后的表单列表
 */
export function updateFormListFields(formList, targetFields, data, options = {}) {
  return formList.map(item => {
    if (targetFields.includes(item.name)) {
      return {
        ...item,
        list: transformToStandardFormat(data, options)
      };
    }
    return item;
  });
} 