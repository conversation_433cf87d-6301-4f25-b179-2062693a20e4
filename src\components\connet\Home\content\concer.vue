<template>
  <div class="concer" @mouseenter="mouseenter()" @mouseleave="mouseleave()">
    
    <img v-for="(item,index) in imglist" @mouseenter="mouseenter(item,index)" @mouseleave="mouseleave(item,index)"
    :key="index" class="imgpoting cursor" v-show="amplifyindex==0?item.XPosition>'20'&&item.XPosition<'80':item"
     :src="falge==index?item.src1:item.src" 
    :style="`top:${item.YPosition}%;left:${item.XPosition}%`" alt="" srcset="" @click="open(item)">
    
    <el-button v-if="showcontent==1"  class="btn" type="primary" @click="opencouts()">数据模型</el-button>
    
<el-dialog v-model="dialogTableVisible" class="homewqmit delogss" :width="showfalge==0?'30%':'60%'" title="设备信息">
    
    <div class="heaereq-one">
        <div class="toplist" :style="counts.length>7&&showfalge!=0?'justify-content: center;':'justify-content: flex-start'">
            <div  v-for="(item,index) in counts" :key="index">
                <el-icon class="iconone cursor toplist-icon" v-if="updata&&showfalge!=0" @click="upchang(item,index)"><DArrowLeft /></el-icon>
                <div :class="['heaereq cursor']" v-if="showfalge==0?index==0:item.indexs==0" 
                :style="[showfalge==0?`background:rgba(${bgcolor.delogcolor},0.35)`:
                changfalge==index?`background:linear-gradient(0deg,#075DB8 0%,${bgcolor.changcolor} 89%);
                color:${bgcolor.font}`:`background:rgba(0, 48, 70,0.35);color:#fff`]" @click="handchang(item,index)">
                
                    <div class="icontop1 bor" :style="[`border:1px solid ${bgcolor.titlecolor}`]" ></div>
                    <div class="icontop" :style="[`border:1px solid ${bgcolor.titlecolor}`]"></div>
                    <div class="icontop2 bor" :style="[`border:1px solid ${bgcolor.titlecolor}`]"></div>
                    <p class="titlefont" v-if="showfalge!=0">{{item.EquipName}}</p>
                </div>
                <el-icon class="iconone cursor toplist-icon1" v-if="nextdata&&showfalge!=0" @click="nextclck(item,index)"><DArrowRight /></el-icon>
            </div>

        </div>
        
        <div class="close cursor" :style="[`background: radial-gradient(50% 50% at 50% 50%,
         rgba(3, 251, 255, 0.17) 0%, ${bgcolor.hovercor} 100%);left:${showfalge==0?'97%':'99%'}`]" @click="close()">
            <el-icon class="closeicon"><CloseBold /></el-icon>
        </div>
    </div>
    <div class="homewqmit-body bodybottom" v-if="showfalge==0">
        <div class="homewqmit-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
         rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
         <div class="homewqmit-body-one">
            <img src="@/assets/img/home/<USER>" alt="">
            <p>设备信息</p>
         </div>
         <div class="homewqmit-nut">
            <p v-if="showcontent==1&&falgetitle=='塔式起重机'" class="cursor" @click="opennut()">螺母检测</p>
            <p class="cursor" @click="more()">更多</p>
         </div>
        </div>
        <div  class="fontcolor" :style="`color:${bgcolor.font}`">
            <div v-show="falgetitle!='监控'" v-for="(item,index) in form" :key="index" class="fontcolor-one">
                <span>{{item.name}}：{{delogform[item.value]}}</span>
            </div>
            <div class="fontcolor" v-if="falgetitle!='施工升降机'&&falgetitle!='监控'">
                <div class="fontcolor-one" v-for="(item,index) in tower" :key="index"
                 v-show="falgetitle=='塔式起重机'?index<=3:(falgetitle=='卸料平台'?index>3:'')">
                    <span>{{item.name}}：{{delogform[item.value]}}</span>
                </div>
            </div>
            <div  class="fontcolors" v-else-if="falgetitle=='施工升降机'">
                <div v-for="(item,inde) in 2" :key="inde" class="fontcolors-one">
                    <div class="iconall" :style="`background:${bgcolor.hovercor};box-shadow: 0px 4px 10px 0px ${bgcolor.hovercor};`">
                        <p>{{inde==1?'左':'右'}}</p>
                    </div>
                    <div class="fontcolorall-one" v-for="(items,index) in tower" :key="index"
                        v-show="falgetitle=='施工升降机'?index<=3:''">
                        <span class="fontcolorall-one-span">{{items.name}}：{{delogform[inde==1?items.leftvalue:items.rightvalue]}}</span>
                    </div>
                </div>
            </div>
            <div  v-else-if="falgetitle=='监控'">
                <div class="fontcolorall-one" v-for="(item,index) in morint" :key="index">
                    <p>{{item.name}}：{{delogform[item.value]}}</p>
                    <div v-if="index==1" class="play" id="play"></div>
                </div>
            </div>
        </div>
    </div>
    <div v-if="showfalge==1" class="bodybottom">
        <deloglist  ref="deloglists" :formlist="getform"  :changname="twoname" :datatable="falgetitle" ></deloglist>
    </div>

    </el-dialog>
    <!-- 数据模型 -->
    <datamodel ref="datamodel"></datamodel>
    <el-dialog v-model="dialogTableVisible1" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]" 
    class="homewqmit" width="60%" >
        <div class="nutcount">
            <div class="nutcount-one" v-for="(item,index) in nutlsit" :key="index"
            :style="`border:2px solid ${bgcolor.titlecolor}`"
            >
                <span class="nutcount-one-p" :style="`border-bottom:2px solid ${bgcolor.titlecolor}`">{{item.NutNum}}</span>
                <span  :style="[`border-right:2px solid ${bgcolor.titlecolor}`,yukline(item)]">{{item.offStatus}}</span>
                <span :style="yukline(item)">{{item.ErrorStatus}}</span>
            </div>
        </div>
    
    </el-dialog>
  </div>
</template>

<script>
import { computed, onMounted, reactive, ref,watch } from 'vue'
import deloglist from "@/components/connet/Home/delog/deloglist.vue";
import datamodel from "@/components/connet/devicelist/content/datamodel.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";

export default {
props:['showcontent'],

components:{
        deloglist,
        datamodel
    },
setup(props,ctx){
    let imglist=ref([])
    let falge=ref(-1)
    let bgcolor=ref({})
    let showfalge=ref(0)//更多0塔机
    let falgetitle=ref('')
    let delogform=ref({})
    let dialogTableVisible=ref(false)
    let dialogTableVisible1=ref(false)
    let showicon=ref(false)
    let getform=ref({
        ProjectCode:store.getters.code,
        UsingPage:'首页',
        IconType:'',
        DetialType:'',
        EquipCode:''
    })
    let form=ref([
        {
            name:'设备类型',
            value:'EquipType'
        },
        {
            name:'设备型号',
            value:'Specifications'
        },{
            name:'设备名称',
            value:'EquipName'
        },{
            name:'设备编号',
            value:'EquipCode'
        },
    ])
    let tower=ref([
        {
            name:'操作人员',
            value:'CraneOperator',
            leftvalue:'LCraneOperator',
            rightvalue:'RCraneOperator'
        },{
            name:'手机号码',
            value:'CellPhone',
            leftvalue:'RCellPhone',
            rightvalue:'LCellPhone'
        },{
            name:'证件号码',
            value:'CertificateNumber',
            leftvalue:'LCertificateNumber',
            rightvalue:'RCertificateNumber'
        },{
            name:'黑匣子状态',
            value:'CraneState'
        },{
            name:'额定限载',
            value:''
        },{
            name:'实时载重',
            value:''
        },{
            name:'实时倾角',
            value:''
        },{
            name:'黑匣子状态',
            value:'CraneState',
            leftvalue:'',
            rightvalue:''
        },
    ])
    let morint=ref([
        {
        name:'监控名称',
        value:'MonitorName'
        },
        {
        name:'监控类型',
        value:'MonitorType'
        },

    ])
    let counts=ref([
        {
            name:'7号塔式起重机',
            indexs:0
        },
    ])
    let changfalge=ref(0)
    let twoname=ref('')
    let amplifyindex=ref(0)
    let datamodel=ref(null)
    let nutlsit=ref([])
    let token=ref('')
    let titlei1=ref('')
    // let topset=ref(['设备名称','安装信息','实时数据','实时数据','实时数据'])
    window.addEventListener('setthcolor', ()=> {
            // console.log('导航');
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--dialog-bg-color', bgcolor.value.delogcolor);
    
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--dialog-bg-color', bgcolor.value.delogcolor);
    
        getlocation()
    })
    const yukline=(val)=>{
        return val.offStatus=='预警'||val.offStatus=='离线'?'color:red':'color:#0066FF'
    }
    // 获取弹窗信息
    const geticonty=async(val)=>{
        // console.log('提交',getform.value,showfalge.value);
        let GUID={
            IconType:val.IconType,
            EquipCode:val.EquipCode,
            ProjectCode:store.getters.code
        }
        // showfalge.value=0
        getform.value.IconType=val.IconType
        getform.value.EquipCode=val.EquipCode
        const {data:res}=await gettable('GetHomePageEquipInfo',GUID)
        // console.log('获取小弹窗',res);
        if (res.code=="1000") {
            delogform.value=res.data
            if (falgetitle.value=='监控') {
            gettoken(res.data)
                
            }

        }
    }
    const getlocation=async()=>{
        let loction=[]
        const {data:res}=await gettable('GetHomePageIconPosition',getform.value)
        if (res.code=="1000") {
            loction=res.data
            // loction
            loction.forEach((item,index)=>{
                switch (item.IconType) {
                    case '塔式起重机':
                        item.src=require('@/assets/img/home/<USER>/tower.png')
                        item.src1=require('@/assets/img/home/<USER>/tower1.png')

                        break;
                    case '监控':
                        item.src=require('@/assets/img/home/<USER>/monitor.png')
                        item.src1=require('@/assets/img/home/<USER>/monitor1.png')

                        break;
                    case '施工升降机':
                        item.src=require('@/assets/img/home/<USER>/lift.png')
                        item.src1=require('@/assets/img/home/<USER>/lift1.png')

                        break;
                    case '卸料平台':
                        item.src=require('@/assets/img/home/<USER>/discharge.png')
                        item.src1=require('@/assets/img/home/<USER>/discharge1.png')

                        break;
                }
                // item.
            });
            imglist.value=loction
        }
    }
    // 获取详情
    const getlsittable=async()=>{
        const {data:res}=await gettable('GetEquipNameByType',getform.value)
        // console.log('获取详情',res);
        
        if (res.code=="1000") {
            counts.value=res.data
            counts.value.map((item,index)=>{
            item.indexs=0
            // changfalge.value=
            if (getform.value.EquipCode==item.EquipCode) {
                changfalge.value=index
                twoname.value=item.EquipName

            }
            })
            // twoname.value=counts.value[0].EquipName
        }

    }
    // // 获取点击后的详情
    // const getdetil=async()=>{
    //     const {data:res}=await gettable('GetEquipDetailMore',getform.value)

    // }
    const updata=computed(()=>{
        // counts.findIndex(item.indexs==0)
    let first= counts.value.findIndex(function(x) {
        // console.log('第一次出现的位置',x);
      return x.indexs == 0;
    })
        if (counts.value.length<=7) {
            first=0
        }else{
            first=first+1

        }
        // console.log('计算属性',first);
        
        return first
    })
    const nextdata=computed(()=>{
        let end= counts.value.findLastIndex(function(x) {
        // console.log('第一次出现的位置',x);
        return x.indexs == 0;
        })
        if (counts.value.length<=7) {
            end=0
        }else{
            end=end+1

        }
        // console.log('计算属性',first);
        return end
    })
    const gettoken=async(val)=>{
        // console.log('获取token',store.getters.username);
        
        let gettablesmodes={
            InUserName:store.getters.username,
            Type:''
        }
		let Type=store.getters.username
			
		if (Type.indexOf('新盛')!=-1) {
			// console.log('获取账号',Type);
			gettablesmodes.Type='新盛监控'
		}else if (Type.indexOf('国丰')!=-1||Type.indexOf('协力')!=-1) {
			gettablesmodes.Type='国丰监控'
			
		}else{
			gettablesmodes.Type='扬尘监控'

        }
        // console.log('提交');
        
        const {data:res}=await gettable('GetElevatorMonitoringToken',gettablesmodes)
        // console.log('获取token',res);
        
        if (res.code=="1000") {
            token.value=res.data.token
            playvioce(val)
        }

    }
    // 监控
    const playvioce=(val)=>{
        // console.log('播放',val);
        
        let play =  new EZUIKit.EZUIKitPlayer({
				  autoplay: true,
				//   id:showlist.value[i].MonitorCode,
				  id:'play',
				  accessToken:token.value,
				  url:val.FluentLink,
                  width:400,
                  height:200,
				  template: "simple", // simple - 极简版;standard-标准版;security - 安防版(预览回放);voice-语音版；
                  handleError:(res)=>{
                        console.log('播放错误回调',res);
                }
                });
    }
    const open=(item)=>{
        // console.log('打开',item);
        geticonty(item)
        falgetitle.value=item.IconType
        showfalge.value=0
        // geticonty(item)

        dialogTableVisible.value=true
            
        // }
    }
    // 螺母检测
    const opennut=()=>{
        getnut()
        dialogTableVisible1.value=true
    }
    const close=()=>{
        if (titlei1.value=='设备统计') {
            ctx.emit("close", 0,'设备统计');
            // amplifyindex.value=0
            
        }
        dialogTableVisible.value=false

    }
    const getmore=()=>{

    }
    const mouseenter=(e,index)=>{
        if (e) {
            falge.value=index
            
        }else{
            console.log('移入',amplifyindex.value);
            
            if (amplifyindex.value==0) {
                showicon.value=true
                
            }
        }
    }

    const mouseleave=(e,index)=>{
        if (e) {
        falge.value=-1
            
        }else{
            if (amplifyindex.value==0) {
                showicon.value=false
                
            }
            // showicon.value=false

        }

    }
    // 更多
    const more=(val)=>{
        if (falgetitle.value!="监控") {
            if (val) {
                titlei1.value='设备统计'
                getform.value.IconType=val.IconType
                getform.value.EquipCode=val.EquipCode
                dialogTableVisible.value=true
            }
        showfalge.value=1

        }
        getlsittable()
    }
    // 点击切换
    const handchang=(val,index)=>{
        // console.log('点击切换',val);
        getform.value.EquipCode=val.EquipCode
        changfalge.value=index

        twoname.value=val.EquipName
    }
    const amplifyopen=(val)=>{
        amplifyindex.value=val
        showicon.value=false

        // emit.
        ctx.emit("getamplify", val);
    }
    const upchang=()=>{
    let first= counts.value.findIndex(function(x) {
        // console.log('第一次出现的位置',x);
      return x.indexs == 0;
    })
    if (first>0&&counts.value.length>=7) {
        // console.log('上一个');
        
        counts.value[first-1].indexs=0
        counts.value[first+6].indexs=1
    }
    }
    const nextclck=()=>{
    let end= counts.value.findLastIndex(function(x) {
        // console.log('第一次出现的位置',x);
        return x.indexs == 0;
        })
    if (end<counts.value.length-1&&counts.value.length>=7) {
    // console.log('下一次');
        counts.value[end-6].indexs=1
        counts.value[end+1].indexs=0
    }
    }
    const opencouts=()=>{
        datamodel.value.showdelog()
    }
    // 获取螺母信息
    const getnut=async()=>{
        const {data:res}=await gettable('GetSmartNutStatus',getform.value)
        // console.log('获取',res);
        nutlsit.value=res.data
    }
    return{
        getform,
        dialogTableVisible,
        dialogTableVisible1,
        amplifyindex,
        nutlsit,
        bgcolor,
        showfalge,
        imglist,
        delogform,
        falge,
        form,
        tower,
        token,
        falgetitle,
        morint,
        changfalge,
        counts,
        updata,
        nextdata,
        twoname,
        datamodel,
        showicon,
        // topset,
        mouseenter,
        mouseleave,
        getmore,
        close,
        open,
        more,
        handchang,
        upchang,
        nextclck,
        amplifyopen,
        opencouts,
        getlocation,
        opennut,
        getnut,
        yukline,
        geticonty,
        playvioce,
        gettoken,
        getlsittable,
        // getdetil
        
    }
}
}
</script>
<style lang="scss">
.homewqmit{
    margin-top: 15vh!important;
}
</style>
<style lang="scss" scoped>
.concer{
    width: 100%;
    height: 100%;
    position: relative;
    .btn{
    margin: 10px;
    width: 20%;
    font-size: 20px;
    font-weight: bold;
    }
}
.iconfont{
    font-size: 45px!important;
    position: absolute;
    left: 2%;
}
.imgpoting{
    position: absolute;
    width: 65px;
    height: 75px;
}
.tower:hover{
    background-image: url('@/assets/img/home/<USER>/tower1.png');
    width: 65px;
    height: 75px;
}
.nutcount{
    display: grid;
    grid-template-columns: repeat(10,10%);
    &-one{
        width: 90px;
        height: 70px;
        margin: 10px;
        padding: 5px;
        font-weight: bold;
        // background: #000;
        display:grid;
        grid-template-columns: repeat(2,50%);
        align-items: center;
        color: #fff;
        &-p{
            grid-column: 1/span  2;
            // bottom: ;
        }
        span{
            height: 100%;
        }
    }
}
.homewqmit{
    &-nut{
        p{
            display: inline-block;
            margin:0 5px;
        }
    }
    .fontcolor{
        // display: flex;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        &-one{
        width: 40%;
        text-align: start;
        margin: 10px 0;
        }
    }

    &-header{
        // margin: 20px;
        width: 93%;
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        p{
            font-size: 18px;
            font-weight: bold;
        }
    }
    &-body-one{
        display: flex;
        align-items: center;
        
        }
.iconall{
    width: 80px;
    height: 80px;
    border-radius: 100%;
}
.fontcolors{
    width: 100%;
    &-one{
    display: grid;
    grid-template-columns: 15% 42% 30%;
    grid-template-rows: 50% 50%;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    }
}
.iconall{
grid-row: 1 / 4;
display:flex;
justify-content: center;
align-items: center;
p{
    font-size: 35px;
    font-weight: bold;
    color: #fff;
}
}
.fontcolorall-one{
    text-align: start;
    margin: 10px 0;
    &-span{
        display: inline-block;
        width: 100%;
    }
}
.play{
    width: 260px;
    height: 150px;
    border: 1px solid #ccc;
}

}
</style>
