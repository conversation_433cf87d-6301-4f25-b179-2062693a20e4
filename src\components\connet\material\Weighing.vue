<template>
  <!-- 磅房信息 -->
  <div class="Weighing padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"></Chamfering>
    <div class="Weighing-two">
      <div class="Weighing-two-left" v-for="(item,index) in Weighinglable" :key="index">
        <p>{{item.name}}:{{addform[item.value]}}</p>
      </div>
      <img :src="addform.HeadImage" alt="" class="cursor" style="width:100%;height:100%">
    </div>
    <picimg ref="picimg"></picimg>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
components:{
  picimg,
  Chamfering
},
setup(){

  let bgcolor=ref({})
//   let echart=ref(true)
//   let countlist=ref([])
  let getform=ref({
      ProjectCode:store.getters.code,

    })
  let picimg=ref(null)
  let Weighinglable=ref([
        {
            name:'磅房名称',
            value:'WaagName'
        },{
            name:'磅房编码',
            value:'WaagCode'
        },{
            name:'材料员',
            value:'Receiver'
        },{
            name:'手机号码',
            value:'CellPhone'
        },{
            name:'最近过磅时间',
            value:'ReceiveTime'
        }
    ])
  let addform=ref({})
  let topforms=ref({
    url:require('@/assets/img/material/Weighing.png'),
    name:"磅房信息"
  })
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getlabe()
  })
  const getlabe=async()=>{  
      const {data:res}=await gettable('GetLastWaagRecord',getform.value)
      if (res.code=="1000") {
        addform.value=res.data
      }
  }


 

	return{
		getform,
		bgcolor,
    addform,
    Weighinglable,
    picimg,
    topforms,
    getlabe

	// getWeighing,
	}
}
}
</script>

<style lang="scss" scoped>
.Weighing{
&-two{
  height: 86%;
  padding: 10px;
  display: grid;
  grid-template-columns: 60% 40%;
  align-items: center;
    img{
        grid-row: 1/span 4;
        grid-column: 2;
    }
    p{
        text-align: start;
    }
    div:nth-child(5){
      grid-column: 1/2 span;
    }
}
}



</style>