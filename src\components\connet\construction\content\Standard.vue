<template>
  <div class="Standard">
    <div class="bysmoiter" id="bysmoiter"></div>
    <div class="">
      <titiesbg :titles="'温度近24小时变化'"></titiesbg>
      <div  class="wdbh" id="wdbh"></div>
    </div>
    <div class="shows" >
        <div v-for="(bg,i) in bglable" :key="i" class="Standard-bg center" :style="`background-image:url(${bg.src})`">
            <p>{{twoform[bg.value]}}</p>
            <p>{{bg.name}}</p>
        </div>
    </div>
    
    <div>
      <titiesbg :titles="'湿度近24小时变化'"></titiesbg>
      <div class="sdbh" id="sdbh"></div>
    </div>
    <div class="bysyj" style="height:200px">
      <titiesbg :titles="'标养室预警'"></titiesbg>
        <div class="bysyj">
            <div class="echartsyj" id="echartsyj"></div>
            <div class="sorcllcount">
                <div class="sorcltable theaders">
                    <span v-for="(item,index) in theader" :key="index">{{item}}</span>
                </div>
                <swiper class="swiperline"  :slides-per-view="6"  :direction="'vertical'"
                :autoplay="{ delay: 2000, disableOnInteraction: false }" loop :modules="modules"
                    v-if="tabledata.length>0">
                    <swiper-slide class="sorcltable" v-for="(item, index) in tabledata" :key="index">
                        <div class="types">{{item.typename}}</div>
                        <span>{{item.standard}}</span>
                        <span class="times">{{item.localetime}}</span>
                    </swiper-slide>
                </swiper>
            </div>

        </div>
    </div>
    <div>
      <titiesbg :titles="'混凝土试块养护比例'"></titiesbg>
      <bires :WorkTypecolor="WorkTypecolor"></bires>
    </div>
    <div class="histable">
      <titiesbg :titles="'混凝土试块标养进出记录'" :forms="forms" @Entry="Entrys()"></titiesbg>
      <el-table :data="tablejl" ref="myTable" :style="['width: 100%',`color:${bgcolor.font};
        --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
        :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
        empty-text="暂无数据" max-height="200px"
        >   
            <template #empty>
                <el-empty  v-loading="loading"></el-empty>
            </template>
            <el-table-column v-for="(item,index) in lables" :key="index" 
			:label="item.name" align="center" :prop="item.value" :width="item.widhts">
			</el-table-column>
        </el-table>
    </div>
    <el-dialog append-to-body title="混凝土进出场记录" class="jldialg" :close-on-click-modal="false" v-model="dialogVisible1" width="70%">
        <el-table :data="tablejl" 
        :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
        empty-text="暂无数据" max-height="400px">   
            <template #empty>
                <el-empty  v-loading="loading"></el-empty>
            </template>
            <el-table-column v-for="(item,index) in lables" :key="index" 
			:label="item.name" align="center" :prop="item.value" :width="item.widhts">
			</el-table-column>
        </el-table>
        <el-pagination  @size-change="handleSizeChange" @current-change="handleCurrentChange"
		 	v-model:current-page="getform.page" :page-size="getform.count" :page-sizes="[5, 10, 20, 30]"
		 	layout="total, sizes, prev, pager, next, jumper" :total="Total">
	    </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import { onBeforeMount, onBeforeUnmount, onMounted, ref,getCurrentInstance, nextTick } from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import titiesbg from "@/components/connet/Common/titiesbg.vue";
import bires from "./bires.vue";
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
export default {
components:{
    titiesbg,
    bires,
    // ecahrtline,
    Swiper,
    SwiperSlide,
    },
setup(){
  const $labelist = getCurrentInstance().appContext.config.globalProperties.$labelist
    let bgcolor=ref({})
    let token=ref('')
    let player=ref(null)
    let url=ref('')
    let accessTokens=ref('')
    let getform=ref({
        ProjectCode:store.getters.code,
        InUserName:store.getters.username,
        type:'掌勤扬尘',
        PouringPosition:'',
        MaintenMethod:'',
        CheckDate:'',
		IsQianTai:"前台",
		page:1,
		count:10
    })
    let WorkTypecolor=["#407fff",'#1F9DF5','#21F5D6','#5c2223','#eea2a4',
	"#a682e6",'#b598a1','#c08eaf','#813c85','#806d9e',
	"#e15d68",'#5e616d','#3170a7','#8fb2c9','#c3d7df',
	"#f29961",'#12a182','#737c7b','#92b3a5','#1a6840',
	"#00cccd",'#bec936','#373834','#5bae23','#e4bf11',
	"#dedede",'#b78d12','#f0d695','#b4a992','#fa5d19',
	"#FE8463",'#de7622','#f1908c','#207f4c','#22a2c3',
	"#9BCA63",'#815c94','#e16c96','#12a182','#bec936',
	'#D7504B', '#C6E579', '#F4E001', '#F0805A', '#26C0C0',
	'#FFB7DD', '#660077', '#FFCCCC', '#FFC8B4', '#550088',
	'#FFFFBB', '#FFAA33', '#99FFFF', '#CC00CC', '#FF77FF',
	'#C63300', '#9955FF', '#66FF66', '#129393', '#395203',
	'#C1232B', '#B5C334', '#FCCE10', '#E87C25', '#27727B',
	'#FAD860', '#F3A43B', '#60C0DD', '#0D7CAA']
    let uerlist=['华东工程674','新盛建设618']
    let bglable=ref([
        {
            name:'实时温度',
            value:'temperature',
            values1:'',
            src:require('@/assets/img/construction/001.svg')
        },{
            name:'实时湿度',
            value:'humidity',
            values1:'',
            src:require('@/assets/img/construction/001.svg')
        },
    ])
    let theader=['类型','实际值','时间']
    let twoform=ref({})
    let tabledata=ref([])
    let WarningEcharts=ref([])
    let countSum=ref(0)
    let forms=ref({
        titles:'更多记录',
        left:'right'
    })
    let lables=ref($labelist('混凝土试块标养进出记录'))
    let loading=ref(false)
    let tablejl=ref([])
    let times=null
    let myTable=ref(null)
    let dialogVisible1=ref(false)
    let Total=ref(0)
    window.addEventListener('setthcolor', ()=> {
            // console.log('导航');
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        
        gettoken()
        geteddata()
        // getwddata()
        getwdsd()
        gettablewd()
        getjltbale()
        
        // startAutoScroll()
    })
    const gettoken=async()=>{
        const {data:res}=await gettable('GetElevatorMonitoringToken',getform.value)
		// console.log('返回',res);
		if (res.code=="1000") {
			accessTokens.value = res.data.token
			if (uerlist.includes(getform.value.InUserName)) {
			// getmonitor()
			}
		}
    }
    const getjltbale=async()=>{
        tablejl.value=[]
        const {data:res}=await gettable('GetTestBlockTable',getform.value)
        tablejl.value=res.data
        Total.value=res.Total
        if (res.code=="1000") {
            nextTick(()=>{
             if (!dialogVisible1.value) {
                 startAutoScroll()

             }

            })
            
        }

    }
    // 监控
    const getmonitor=()=>{
        player.value =  new EZUIKit.EZUIKitPlayer({
            autoplay: true,
            id: "bysmoiter",
            accessToken:accessTokens.value,
            url:'ezopen://open.ys7.com/*********/20.hd.live',
            width: 640,
            height: 360,
            template: "simple", // simple - 极简版;standard-标准版;security - 安防版(预览回放);voice-语音版；
            handleError:(res)=>{
                console.log('播放错误回调',res);
            }
            });	
    }
    const gettablewd=async()=>{
        // this.loading=true
        tabledata.value=[]
        const {data:res}=await gettable('GetCRRWarninglistInfo',getform.value)
        // console.log('获取',res);
        
        if (res.code=="1000") {
            res.data.Warninglist.map((item,index)=>{
                if (item.typename=='温度预警') {
                item.type=0
                }
                if (item.typename=='湿度预警') {
                item.type=1
                }
            });
			WarningEcharts.value=res.data.WarningEcharts
            tabledata.value=res.data.Warninglist
            countSum.value=res.data.countSum
			getworing()
            }


    }
    const getworing=()=>{
            var echarts = require('echarts');
			// var myecharts = echarts.init(document.getElementById('Alarmstatistics'));
			let myChart = echarts.getInstanceByDom(document.getElementById("echartsyj"))
            if (myChart == null) {
                myChart = echarts.init(document.getElementById('echartsyj'));
                }
            let colorList=WorkTypecolor
            let option = {
				title: [{
					text: '预警总数量',
					textStyle: {
						color: "#fff",
						fontSize: 14,
					},
					itemGap: 10,
					left: "center",
					top: "50%",
				},
				{
					text:countSum.value,
					textStyle: {
						color: "#fff",
						fontSize: 14,
						fontWeight: "normal",
					},
					itemGap: 10,
					left: "center",
					top: "35%",
				},
				],
				tooltip: {
					trigger: "item",
				},
				series: [{
					hoverAnimation: false,
					type: "pie",
					center: ["50%", "50%"],
					radius: ["60%", "90%"],
					clockwise: true,
					avoidLabelOverlap: true,
					hoverOffset: 15,
					itemStyle: {
						normal: {
							color: function(params) {
								return colorList[params.dataIndex];
							},
						},
					},
					label: {
						show: false,
					},
					labelLine: {},
					data: WarningEcharts.value,
				}, ],
				};
				myChart.setOption(option);
				window.addEventListener("resize", function() {
					myChart.resize();
				});
        }
    // 获取温度
    const geteddata=async()=>{
        const {data:res}=await gettable('GetCuringRoomRealDataInfo',getform.value)
        // console.log('获取温度',res);
        if (res.code=="1000") {
            twoform.value=res.data
        }
    }
    // 获取24实时数据
    const getwdsd=async()=>{
        const {data:res}=await gettable('GetCuringRoomRealDatalistInfo',getform.value)
        // console.log('获取24小时数据',res)
        
        if (res.code=="1000") {
            getwddata(res.data[0].TemperatureECharts)
            getsddat(res.data[0].HumidityECharts)
        }
    }
    const getwddata=(val)=>{
        let names=[]

        if (val.length>0) {
            // console.log('温度数据',val);
           names= val.map((item,index)=>{
            // console.log('获取');
            return item.name
           })
        }
        let ids='wdbh'
        myChart2(ids,'温度（°C）',names,val,'当前时间段平均温度')
    }
    const getsddat=(val)=>{
        let names=[]
        if (val.length>0) {
            // console.log('温度数据',val);
           names= val.map((item,index)=>{
            // console.log('获取');
            return item.name
           })
        }
        let ids='sdbh'
        myChart2(ids,'湿度（%RH）',names,val,'当前时间段平均湿度')

    }
    const myChart2=(val,tiles,names,value,topsname)=> {
		// console.log('提交',this.addform);
		let echarts = require('echarts');
        let myChart = echarts.getInstanceByDom(document.getElementById(val))
            if (myChart == null) {
         myChart = echarts.init(document.getElementById(val));
        }
		let option = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					label: {
						show: true,
						backgroundColor: '#fff',
						color: '#556677',
						borderColor: 'rgba(0,0,0,0)',
						shadowColor: 'rgba(0,0,0,0)',
						shadowOffsetY: 0
					},
					lineStyle: {
						width: 0
					}
				},
				backgroundColor: '#012259',
				textStyle: {
					color: '#ffffff'
				},
				padding: [10, 10],
				extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)'
			},
			grid: {
				left: '3%',
				right: '5%',
				bottom: '3%',
				top: '20%',
				containLabel: true
			},
			xAxis: [{
				type: 'category',
				name: '时间',
				nameTextStyle: {
					color: "rgba(255,255,255,1)",
					fontSize: 14,
				},
				data: names,
				axisLine: {
					lineStyle: {
						color: '#002860'
					}
				},
				axisTick: {
					show: false
				},
				axisLabel: {
					interval: 2,
					textStyle: {
						color: '#ffffff'
					},
					// 默认x轴字体大小
					fontSize: 12,
					// margin:文字到x轴的距离
					margin: 15
				},
				axisPointer: {},
				boundaryGap: false,
				// numberFunction:5
				// interval:auto
			}
			
			],
			yAxis: [{
				type: 'value',
				name: tiles,
				nameTextStyle: {
					color: "rgba(255,255,255,1)",
					fontSize: 14,
				},
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#002860'
					}
				},
				axisLabel: {
					textStyle: {
						color: '#ffffff'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						color: '#3B7489'
					}
				}
			}
			
			],
			series: [{
				name: topsname,
				type: 'line',
				data:value,
				symbolSize: 1,
				symbol: 'circle',
				// smooth: true,
				yAxisIndex: 0,
				showSymbol: false,
				lineStyle: {
					width: 2,
					color: "#00aff0",
					shadowColor: 'rgba(158,135,255, 0.3)',
				},
				itemStyle: {
					normal: {
						color: "#00aff0",
					}
				},
				animationDuration: 800,
				animationEasing: "cubicInOut",
			}, ]

		};
		myChart.setOption(option);
		window.addEventListener("resize", function() {
			myChart.resize();
		});
	}
    const startAutoScroll = () => {
        // console.log('返回',myTable.value);
        
      times = setInterval(() => {
        // const tableBodyWrapper = myTable.value.$el.querySelector('.el-table__body-wrapper');
        const tableBodyWrapper = myTable.value.$el.querySelector('.el-scrollbar__wrap');
        // console.log('获取',tableBodyWrapper);
        if (tableBodyWrapper) {
            // console.log('获取',tableBodyWrapper.scrollTop);el-scrollbar__view
          tableBodyWrapper.scrollTop += 50; // 每次滚动1像素
          if (tableBodyWrapper.scrollTop >= tableBodyWrapper.scrollHeight - tableBodyWrapper.clientHeight) {
            tableBodyWrapper.scrollTop = 0; // 回到顶部重新开始滚动
          }
        }
      }, 1000); // 每50毫秒滚动一次
    }
    const Entrys=()=>{
        dialogVisible1.value=true
        getjltbale()
    }
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
    const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        getjltbale()
        }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        getform.value.page=val
        getjltbale()
        }
    onBeforeUnmount(()=>{
        player.value?.stop()
        clearInterval(times)
    })
    return{
        token,
        player,
        bgcolor,
        url,
        accessTokens,
        getform,
        uerlist,
        bglable,
        twoform,
        getwdsd,
        getsddat,
        modules: [Autoplay, Pagination, Navigation],
        tabledata,
        theader,
        WorkTypecolor,
        forms,
        loading,
        tablejl,
        lables,
        times,
        myTable,
        dialogVisible1,
        Total,

        gettoken,
        getmonitor,
        getwddata,
        myChart2,
        getworing,
        tableRowClassName,
        startAutoScroll,
        handleCurrentChange,
        handleSizeChange,
        Entrys

    }
}
}
</script>
<style lang="scss">
.Standard{
    .el-table__body-wrapper {
        overflow-y: auto!important;
        // overflow:auto!important;
        max-height: 200px;
        height: 200px;
    }
}
.jldialg{
.el-pagination{
    --el-pagination-bg-color:transparent !important;
    --el-pagination-text-color:#000!important;
    --el-pagination-button-color:#000!important;
    --el-pagination-button-disabled-bg-color:transparent !important;
    }
    .el-pagination__total{
        color: #000!important;
    }

    .el-pagination__jump{
        color: #000!important;

    }
}

</style>
<style lang="scss" scoped>
.Standard{
    display: grid;
    grid-template-columns: 30% 10% 45% 15%;
    color: #fff;
    // grid-template-rows: ;
}
.shows{
    height: 260px;
    display: flex;
    flex-direction: column;
	// flex-wrap: wrap;
	align-items: center;
}
.Standard-bg{
    margin: 10px;
    width: 70%;
    height: 90px;
    background-size: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    p{
        font-size: 20px;
    }
}
.echartsyj{
    width: 100%;
    height: 150px;
}
.wdbh,.sdbh{
    width: 100%;
    height: 200px;
}
.sorcltable{
display: grid;
grid-template-columns: 25% 30% 45%;
justify-items: center;
}
.theaders{
    // display: grid;
    // grid-template-columns: 20% 30% 50%;
    // justify-items: center;
    span{
        margin: 3px 0;
    }
}
.bysyj{
    grid-column: 4;
    grid-row: 2/span 3;
}
.histable{
    grid-row: 3;
    grid-column: 2/span 2;
}
.bysmoiter{
height: 260px;
width: 100%;
grid-column: 1/span 2;
grid-row: 1/span 2;
}
</style>