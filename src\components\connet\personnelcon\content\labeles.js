export function labelist(params) {
    // 表单
    let lablist=[]
    let formcout=[
        {
         name:'人员管理顶部',
         value:[{
            name:'人员库',
            value:'icon-renyuan-',
            },{
            name:'劳务合同',
            value:'icon-fenbaolaowuhetong',
            },{
            name:'三级教育',
            value:'icon-anquanjiaoyu',
            },{
            name:'身份证',
            value:'icon-shenfenzhengrenzheng',
            },{
            name:'特种作业证',
            value:'icon-winfo-icon-zhengshu',
            },{
            name:'人员培训',
            value:'icon-icon',
            },{
            name:'人员定位',
            value:'icon-renyuandingwei',
            },{
            name:'人员户籍地',
            value:'icon-zhongguo',
            },{
            name:'黑名单人员',
            value:'icon-heimingdan1',
            },
         ]
        },{
            name:'人员库',
            value:[{
               name:'序号',
               value:'rowNum',
               widths:'50'
               },{
               name:'姓名',
               value:'Name',
               widths:'80'
               },{
               name:'工人类型',
               value:'WorkerType',
               },{
               name:'身份证号码',
               value:'IDCardNumber',
               },{
               name:'工种',
               value:'GNAME',
               },{
               name:'手机号码',
               value:'CellPhone',
               },{
               name:'最早进场日期',
               value:'FirstDate',
               },{
               name:'最晚离场日期',
               value:'NextExitDate',
               },{
               name:'状态',
               value:'Status',
               widths:'60'
               },
            ]
        },{
            name:'劳务合同',
            value:[{
               name:'序号',
               value:'rowNum',
               widths:'50'
               },{
                name:'员工姓名',
                value:'WorkerName',
                widths:'100'
                },{
                name:'参建单位',
                value:'CorpName',
                widths:''
                },{
                name:'工种',
                value:'GNAME',
                widths:''
                },{
                name:'班组',
                value:'TeamName',
                widths:''
                },{
                name:'合同级别',
                value:'Grade',
                widths:''
                },{
                name:'劳务合同',
                value:'Participatingunits',
                widths:''
                },
            ]
        },{
            name:'三级教育',
            value:[{
                name:'序号',
                value:'rowNum',
                widths:''
            },
            {
                name:'员工姓名',
                value:'WorkerName',
                width:''
            },{
                name:'参建单位',
                value:'CorpName',
                width:''
            },{
                name:'工种',
                value:'GNAME',
                width:''
            },{
                name:'班组',
                value:'TeamName',
                width:''
            },{
                name:'三级教育',
                value:'Participatingunits',
                width:''
            },
            ]
        },{
            name:'身份证',
            value:[{
                name:'序号',
                value:'rowNum',
                widths:''
            },
            {
                name:'姓名',
                value:'WorkerName',
                width:''
            },{
                name:'性别',
                value:'Gender',
                width:''
            },{
                name:'籍贯',
                value:'GrantOrg',
                width:''
            },{
                name:'手机号码',
                value:'CellPhone',
                width:''
            },{
                name:'身份证有效期',
                value:'StartDate',
                width:''
            },{
                name:'身份证失效日期',
                value:'ExpiryDate',
                width:''
            },{
                name:'身份证状态',
                value:'Status',
                width:''
            },
            ]
        },{
            name:'特种作业证',
            value:[{
                name:'序号',
                value:'rowNum',
                widths:'60'
            },
            {
                name:'员工姓名',
                value:'WorkerName',
                widths:'90'
            },{
                name:'证件类型',
                value:'CertificateTypeName',
                width:''
            },{
                name:'证件类型名称',
                value:'CertificateName',
                width:''
            },{
                name:'证书状态',
                value:'CertificateState',
                widths:'100'
            },
            {
                name:'特种作业证',
                value:'Participatingunits',
                widths:'100'
            },
            ]
        },{
            name:'特种作业证详情',
            value:[
            {
                name:'证件类型',
                value:'CertificateType',
                type:''
            },{
                name:'证件类型名称',
                value:'CertificateTypeName',
                type:''
            },{
                name:'证书名称',
                value:'CertificateName',
                type:''
            },
            {
                name:'证书编号',
                value:'CertificateCode',
                type:''
            },{
                name:'证书有效期',
                value:'CertificateTerm',
                type:''
            },{
                name:'岗位',
                value:'Position',
                type:''
            },{
                name:'证书照片',
                value:'CertificatePhoto',
                type:''
            },
            ]
        },{
            name:'人员培训',
            value:[
            {
                name:'序号',
                value:'rowNum',
                 widths:'50'
            },
            {
                name:'培训名称',
                value:'TrainName',
            },{
                name:'培训类型',
                value:'TrainType',
            },{
                name:'培训日期',
                value:'TrainDate',
            },
            {
                name:'人员培训',
                value:'Participatingunits',
                widths:'100'
            }
            ]
        },{
            name:'人员培训详情',
            value:[
            {
                name:'培训名称',
                value:'TrainName'
            },{
                name:'培训类型',
                value:'TypeName'
            },{
                name:'培训人',
                value:'TrainPerson'
            },{
                name:'培训对象',
                value:'TargetDetail'
            },{
                name:'培训日期',
                value:'TrainDate'
            },{
                name:'培训时长',
                value:'TrainDuration',
                values1:'DurationUnit'
            },{
                name:'培训内容',
                value:'TrainContent'
            },{
                name:'培训照片',
                value:'TrainPhoto'
            },{
                name:'附件',
                value:'TrainAppendix'
            },
            ]
        },{
            name:'黑名单人员',
            value:[
            {
                 name:'序号',
                 value:'rowNum',
                 widths:'50'
             },
             {
                name:'员工姓名',
                value:'WorkerName',
                widths:'80'
            },
            {
                name:'身份证号码',
                value:'IDCardNumber',
                width:''
            },{
                name:'事件类型',
                value:'EventType',
                width:''
            },{
                name:'差评记录分类',
                value:'Severity',
                width:''
            },
             {
                name:'状态',
                value:'Status',
                width:''
            },{
                name:'发生时间',
                value:'HappenDate',
                width:''
            },
            ]
        },{
        name:'人员户籍地',
        value:[
           {
               name:'序号',
               value:'rowNum',
               widths:'60'
           },{
               name:'姓名',
               value:'Name',
               widths:'80'
           },{
               name:'人员类型',
               value:'WorkerType',
               widths:'95'
           },{
               name:'年龄',
               value:'Age',
               widths:'55'
           },{
               name:'性别',
               value:'Gender',
               widths:'55'
           },{
               name:'所属参建单位',
               value:'CorpName',
               widths:''
           },{
               name:'工种',
               value:'GNAME',
               widths:''
           },{
               name:'班组',
               value:'TeamName',
               widths:''
           },{
               name:'最近进场日期',
               value:'pInDate',
               widths:'110'
           }
          ]
        },
        {
        // 设备管理
        name:'设备维保记录',
        value:[
          { name:'序号',
           value:'rowNum',
           widths:'60'
           },{
           name:'维保日期',
           value:'OperateDate',
           },{
           name:'设备类型',
           value:'EquipType',
           },{
           name:'设备名称',
           value:'EquipName',
           },{
           name:'维保类型',
           value:'MaintainType',
           },{
           name:'维保人',
           value:'Submitter',
           },{
           name:'维保内容',
           value:'',
           }
              ]
        },{
        // 设备管理搜索
        name:'更多维保搜索',
        value:[{
           name:'维保日期',
           value:'OperateDate',
           type:3
           },{
           name:'设备类型',
           value:'EquipType',
           type:2,
           list:[]
           },{
           name:'设备名称',
           value:'EquipName',
           type:2,
           list:[]
           },{
           name:'维保类型',
           value:'MaintainType',
           type:2,
           list:[
            {
            name:'全部',
            value:''
            },{
            name:'系统预警',
            value:'系统预警'
            },{
            name:'违章操作',
            value:'违章操作'
            },{
            name:'传感器报警',
            value:'传感器报警'
            },{
            name:'系统报警',
            value:'系统报警'
            },
           ]
           }
              ]
        },{
        // 环境管理
        name:'环境告警',
        value:[
          { name:'序号',
           value:'rowNum',
           widths:'60'
           },{
           name:'开始时间',
           value:'STARTTIME',
           },{
           name:'预警告警',
           value:'TYPE',
           },{
           name:'均值 μg/m³',
           value:'TSPAVG',
           },{
           name:'峰值 μg/m³',
           value:'TSPMAX',
           },{
           name:'持续时间(分钟)',
           value:'CXSJ',
           },{
           name:'结束时间',
           value:'ENDTIME',
           }
        ]
        },{
         // 混凝土试块标养进出记录
         name:'混凝土试块标养进出记录',
         value:[
            {
                name:'试块浇筑部位',
                value:'PouringPosition',
                widhts:''
            },{
                name:'设计强度等级',
                value:'StrengthGrade',
                widhts:''
            },{
                name:'浇筑日期',
                value:'PouringDate',
                widhts:''
            },{
                name:'养护方式',
                value:'MaintenMethod',
                widhts:'80'
            },{
                name:'送检日期',
                value:'CheckDate',
                widhts:''
            },{
                name:'报告编号',
                value:'ReportNo',
                widhts:''
            },{
                name:'样品编号',
                value:'SampleNum',
                widhts:''
            },{
                name:'生产厂家',
                value:'Manufacturer',
                widhts:''
            },{
                name:'结果',
                value:'Result',
                widhts:'60'
            },
         ]
         },{
            // 非实体工程材料重复利用
            name:'非实体工程材料重复利用',
            value:[
               {
                   name:'',
                   value:'NoEntityMaterial',
                   widhts:''
               },{
                   name:'单位：',
                   value:'MaterialUnit',
                   widhts:''
               },{
                   name:'进场量：',
                   value:'EntryVolume',
                   widhts:''
               },{
                   name:'出场量：',
                   value:'ExitVolume',
                   widhts:''
               },{
                   name:'可重复利用率：',
                   value:'ReuseRate',
                   widhts:''
               },{
                   name:'用途：',
                   value:'Purpose',
                   widhts:''
               }
            ]
        },{
            // 项目用水记录及工程用水总量汇总表
            name:'项目用水记录及工程用水总量汇总表',
            value:[
               {
                   name:'',
                   value:'AreaName',
                   widhts:''
               },{
                   name:'水表数量：',
                   value:'WaterEquipNum',
                   widhts:''
               },{
                   name:'',
                   value:'UsingWaterTotal',
                   widhts:''
               },{
                   name:'本次抄表：',
                   value:'ThisRead',
                   widhts:''
               },{
                   name:'',
                   value:'HBdown',
                   widhts:''
               }
            ]
        },{
            // 节水设备使用统计表
            name:'节水设备使用统计表',
            value:[{
                   name:'',
                   value:'EquipName',
                   widhts:''
               },{
                   name:'品牌型号：',
                   value:'EquipModel',
                   widhts:''
               },{
                   name:'额定功率/消耗：',
                   value:'EquipPower',
                   widhts:''
               },{
                   name:'使用部位：',
                   value:'UsingPosition',
                   widhts:''
               },{
                   name:'产生效益：',
                   value:'GenerateBenefits',
                   widhts:''
                }
            ]
        },{
            name:'基坑降水收集记录表',
            value:[{
                   name:'',
                   value:'Name',
                   widhts:''
               },{
                   name:'',
                   value:'NewPrecipitate',
                   widhts:''
               },{
                   name:'',
                   value:'TotalNum',
                   widhts:''
               },{
                   name:'',
                   value:'HBPumpCapacity',
                   widhts:''
               }
            ]
        },{
            name:'中水回用记录表',
            value:[{
                   name:'',
                   value:'Name',
                   widhts:''
               },{
                   name:'',
                   value:'MonthWater',
                   widhts:''
               },{
                   name:'',
                   value:'TotalUsingWater',
                   widhts:''
               },{
                   name:'',
                   value:'YearWater',
                   widhts:''
               }
            ]
        },{
            name:'雨水回用记录表',
            value:[{
                   name:'',
                   value:'Name',
                   widhts:''
               },{
                   name:'近期收集：',
                   value:'NewRainWater',
                   widhts:''
               },{
                   name:'',
                   value:'TotalRainWater',
                   widhts:''
               },{
                   name:'',
                   value:'HBRainWater',
                   widhts:''
               }
            ]
        },{
            name:'雨水回用记录表图表',
            value:[{
                name:'#006600',
                color:'#66FF00',
                type:'bar',
                i:0
                },{
                name:'#9900FF',
                color:'#FFCCFF',
                type:'bar',
                i:0
                },{
                name:'#0000FF',
                color:'#00FFFF',
                type:'bar',
                i:0
                },{
                name:'#66FF00',
                color:'',
                type:'line',
                i:1
                },{
                name:'#FFCCFF',
                color:'',
                type:'line',
                i:1
                },{
                name:'#00FFFF',
                color:'',
                type:'line',
                i:1
                },
            ]
        },{
            name:'施工用电记录表及工程用电总量汇总表',
            value:[{
                   name:'',
                   value:'AreaName',
                   widhts:''
               },{
                   name:'电表数量：',
                   value:'WaterEquipNum',
                   widhts:''
               },{
                   name:'',
                   value:'UsingWaterTotal',
                   widhts:''
               },{
                   name:'本次用电：',
                   value:'ThisRead',
                   widhts:''
               },{
                    name:'',
                    value:'HBdown',
                    widhts:''
            }
            ]
        },{
            name:'设备总体耗能计划',
            value:[{
                   name:'',
                   value:'EquipName',
                   widhts:''
               },{
                   name:'品牌型号：',
                   value:'EquipModel',
                   widhts:''
               },{
                   name:'额定功率（kw）：',
                   value:'RatedPower',
                   widhts:''
               },{
                   name:'用于施工部位：',
                   value:'ConstructionSite',
                   widhts:''
               },{
                    name:'投入时间：',
                    value:'UsingDate',
                    widhts:''
            }
            ]
        },{
            name:'节能设备及太阳能、风能、空气能设备配备情况登记表',
            value:[{
                name:'',
                value:'EquipName',
                widhts:''
            },{
                name:'品牌型号：',
                value:'EquipModel',
                widhts:''
            },{
                name:'额定功率/消耗：',
                value:'EquipPower',
                widhts:''
            },{
                name:'使用部位：',
                value:'UsingPosition',
                widhts:''
            },{
                name:'产生效益：',
                value:'SaveEnergyEffect',
                widhts:''
             }
            ]
        },{
            name:'大型机械保养记录表',
            value:[{
                name:'',
                value:'MaintainName',
                widhts:''
            },{
                name:'近期维保次数：',
                value:'MaxDateMaintainNum',
                widhts:''
            },{
                name:'',
                value:'MaintainCount',
                widhts:''
            },{
                name:'已拆卸塔机：',
                value:'OffEquipNum',
                widhts:''
            }
            ]
        },{
            name:'三阶段场布规划图',
            value:[{
                name:'',
                value:'Block',
                widhts:''
            },{
                name:'占地面积（㎡）',
                value:'',
                widhts:''
            },{
                name:'',
                value:'AreaCovered',
                widhts:''
            },{
                name:'使用面积（㎡）',
                value:'',
                widhts:''
            },{
                name:'',
                value:'UsingArea',
                widhts:''
            }
            ]
        },{
            name:'现场临时用房、硬化、植草砖铺装等各临建建设面积台账',
            value:[{
                name:'',
                value:'Area',
                widhts:''
            },{
                name:'面积（㎡）：',
                value:'SquareMeter',
                widhts:''
            },{
                name:'面积占比：',
                value:'SquareMeterRate',
                widhts:''
            },{
                name:'位置：',
                value:'Position',
                widhts:''
            },{
                name:'用途：',
                value:'Purpose',
                widhts:''
            }
            ]
        },{
            name:'保护用地措施',
            value:[
                {
                    name:'施工阶段',
                    value:'ConstructionStage',
                },{
                    name:'取（弃）土',
                    value:'BorrowSuil',
                },{
                    name:'方量（m³）',
                    value:'Volume',
                },{
                    name:'取（弃）土场地',
                    value:'BorrowSite',
                },{
                    name:'累计方量（m³）',
                    value:'SumVolume',
                }
            ]
        },{
            name:'施工现场人员实名制登记表',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'80'
                },{
                    name:'姓名',
                    value:'WorkerName',
                    widths:'100'
                },{
                    name:'性别',
                    value:'Sex',
                    widths:'80'
                },{
                    name:'工种',
                    value:'GNAME',
                },{
                    name:'身份证号码',
                    value:'IDCardNumber',
                },{
                    name:'进场时间',
                    value:'pInDate',
                },{
                    name:'出场时间',
                    value:'ExitDate',
                },{
                    name:'状态',
                    value:'Status',
                    widths:'80'
                }
            ]
        },{
            name:'施工现场人员实名制登记表查看',
            value:[
                {
                    name:'姓名：',
                    value:'Name',
                    widths:''
                },
                {
                    name:'性别：',
                    value:'Gender',
                    widths:''
                },{
                    name:'民族：',
                    value:'Nation',
                    widths:''
                },{
                    name:'出生日期：',
                    value:'Birthday',
                },{
                    name:'政治面貌：',
                    value:'PoliticsType',
                },{
                    name:'文化程度：',
                    value:'CultureLevelType',
                },{
                    name:'婚姻状况：',
                    value:'MaritalStatus',
                },{
                    name:'证件类型：',
                    value:'IDCardType',
                },{
                    name:'证件号码：',
                    value:'IDCardNumber',
                },{
                    name:'户口性质：',
                    value:'CensusType',
                },{
                    name:'住址：',
                    value:'Address',
                },{
                    name:'证件开始日期：',
                    value:'StartDate',
                },{
                    name:'证件结束日期：',
                    value:'ExpiryDate',
                },{
                    name:'发证机关：',
                    value:'GrantOrg',
                },{
                    name:'手机号码：',
                    value:'CellPhone',
                },{
                    name:'现住址：',
                    value:'CurrentAddress',
                },{
                    name:'是否有重大病史：',
                    value:'HasBadMedicalHistory',
                },{
                    name:'加入公会时间：',
                    value:'JoinedTime',
                },{
                    name:'芯片卡号：',
                    value:'CardNumber',
                },{
                    name:'紧急联系人：',
                    value:'UrgentLinkMan',
                },{
                    name:'联系方式：',
                    value:'UrgentLinkManPhone',
                }
            ]
        },{
            name:'食堂从业人员健康证明登记表',
            value:[{
                    name:'姓名',
                    value:'WorkerName',
                    widths:'80'
                },{
                    name:'性别',
                    value:'Gender',
                    widths:'80'
                },{
                    name:'工种',
                    value:'Post',
                    widths:'80'
                },{
                    name:'身份证号码',
                    value:'IDCardNumber',
                },{
                    name:'健康证号码',
                    value:'HealthNum',
                },{
                    name:'健康证有效期至',
                    value:'HeathTerm',
                },{
                    name:'体检日期',
                    value:'ExamDate',
                    widths:'120'
                },{
                    name:'进场时间',
                    value:'InTime',
                    widths:'120'
                },{
                    name:'出场时间',
                    value:'OutTime',
                    widths:'120'
                }
            ]
        },{
            name:'特种作业人员登记表',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'80'
                },{
                    name:'姓名',
                    value:'WorkerName',
                    widths:''
                },{
                    name:'性别',
                    value:'Sex',
                    widths:'80'
                },{
                    name:'工种',
                    value:'GNAME',
                    widths:''
                },{
                    name:'身份证号码',
                    value:'IDCardNumber',
                },{
                    name:'进场时间',
                    value:'pInDate',
                    widths:''
                },{
                    name:'出场时间',
                    value:'ExitDate',
                    widths:''
                },{
                    name:'状态',
                    value:'CertificateState',
                    widths:''
                }
            ]
        },{
            name:'严重污染天气记录表',
            value:[
                {
                    name:'空气质量指数',
                    value:'QualityIndex',
                    widths:''
                },{
                    name:'污染程度',
                    value:'PollutionLevel',
                    widths:''
                },{
                    name:'代表颜色',
                    value:'Color',
                    widths:''
                },{
                    name:'污染程度次数',
                    value:'PollutionLevelNum',
                    widths:''
                },{
                    name:'污染程度占比',
                    value:'PollutionLevelRate',
                },{
                    name:'对健康影响情况',
                    value:'HealthImpact',
                    widths:''
                }
            ]
        },{
            name:'职业病防治体检登记表',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'80'
                },{
                    name:'姓名',
                    value:'WorkerName',
                    widths:''
                },{
                    name:'性别',
                    value:'Gender',
                    widths:'80'
                },{
                    name:'工种',
                    value:'WorkerType',
                    widths:''
                },{
                    name:'身份证号码',
                    value:'IDCardNumber',
                },{
                    name:'主要排查职业病',
                    value:'Troubleshoot',
                    widths:''
                },{
                    name:'体检日期',
                    value:'ExamDate',
                    widths:''
                },{
                    name:'体检结果',
                    value:'ExamResult',
                    widths:''
                }
            ]
        },{
            name:'施工现场卫生保洁责任表',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'80'
                },{
                    name:'星期',
                    value:'Week',
                    widths:''
                },{
                    name:'卫生保洁责任区域',
                    value:'CleanResponseArea',
                    widths:''
                },{
                    name:'责任人',
                    value:'ResponsePerson',
                    widths:''
                },{
                    name:'卫生巡查人',
                    value:'HealthInspector',
                },{
                    name:'备注',
                    value:'Remark',
                    widths:''
                }
            ]
        },{
            name:'洒水记录表',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'80'
                },{
                    name:'日期',
                    value:'Dates',
                    widths:''
                },{
                    name:'时段',
                    value:'Times',
                    widths:''
                },{
                    name:'生活区',
                    value:'LivingQuarters',
                    widths:''
                },{
                    name:'办公区',
                    value:'OfficeZone',
                },{
                    name:'生产区',
                    value:'ProductionArea',
                    widths:''
                },{
                    name:'检查人',
                    value:'Checker',
                    widths:''
                }
            ]
        },{
            name:'餐具消毒记录表',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'60'
                },{
                    name:'餐具名称',
                    value:'TableWareName',
                    widths:'90'
                }
            ]
        },{
            name:'施工现场消毒记录表',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'80'
                },{
                    name:'消毒时间',
                    value:'SterilizationTime',
                    widths:''
                },{
                    name:'厕所',
                    value:'Toilet',
                    widths:''
                },{
                    name:'排水沟',
                    value:'AdministrativeArea',
                    widths:''
                },{
                    name:'食堂',
                    value:'Canteen',
                },{
                    name:'检查人',
                    value:'Checker',
                    widths:''
                }
            ]
        },{
            name:'劳动力计划表顶部',
            value:[
                {
                    name:'',
                    value:'Dates',
                    widths:''
                },
                {
                    name:'劳动力计划人数：',
                    value:'PlanNum',
                    widths:''
                },{
                    name:'劳动力台账人数：',
                    value:'LedgerNum',
                    widths:''
                },{
                    name:'工种数量：',
                    value:'WorkTypeNum',
                    widths:''
                },{
                    name:'劳动力节约百分比：',
                    value:'TotalSaveRate',
                    widths:''
                }
            ]
        },{
            name:'劳动力计划表',
            value:[
                {
                    name:'',
                    value:'WorkTypeName',
                    widths:''
                },
                {
                    name:'劳动力计划人数：',
                    value:'PlanCount',
                    widths:''
                },{
                    name:'劳动力台账人数：',
                    value:'LedgerCount',
                    widths:''
                },{
                    name:'',
                    value:'SaveRate',
                    widths:''
                }
            ]
        },{
            name:'培训计划',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'80'
                },{
                    name:'计划培训日期',
                    value:'TrainPlanDate',
                    widths:''
                },{
                    name:'培训施工阶段',
                    value:'TrainConstruction',
                    widths:''
                },{
                    name:'培训主题内容',
                    value:'TrainContent',
                    widths:''
                },{
                    name:'拟培训人',
                    value:'TrainPerson',
                }
            ]
        },{
            name:'培训台账',
            value:[
                {
                    name:'序号',
                    value:'rowNum',
                    widths:'70'
                },{
                    name:'培训名称',
                    value:'TrainName',
                    widths:'150'
                },{
                    name:'地点',
                    value:'TrainPlace',
                    widths:'110'
                },{
                    name:'主讲人',
                    value:'Speaker',
                    widths:'100'
                },{
                    name:'日期',
                    value:'TrainDate',
                    widths:'120'
                },{
                    name:'记录人',
                    value:'Recorder',
                    widths:'100'
                },{
                    name:'人数',
                    value:'Persons',
                    widths:'100'
                },{
                    name:'主要培训内容摘要',
                    value:'TrainContent',
                }
            ]
        },{
        name:'工种考勤分析',
        value:[
            {
                name:'序号',
                value:'rowNum',
                widths:'70'
            },{
                name:'工种类型',
                value:'GName',
                widths:''
            },{
                name:'在场人数',
                value:'zcrs',
                widths:''
            },{
                name:'今日考勤人数',
                value:'AttendNum',
                widths:''
            },{
                name:'考勤率',
                value:'AttendRate',
                widths:''
            }
        ]
        },{
        name:'工种考勤详情',
        value:[
            {
                name:'序号',
                value:'rowNum',
                widths:'70'
            },{
                name:'姓名',
                value:'Name',
                widths:''
            },{
                name:'班组',
                value:'TeamName',
                widths:''
            },{
                name:'最近进场日期',
                value:'pInDate',
                widths:''
            },{
                name:'最近离场日期',
                value:'ExitDate',
                widths:''
            },{
                name:'最早进场日期',
                value:'FirstDate',
                widths:''
            },{
                name:'今日是否考勤',
                value:'IsAttend',
                widths:''
            }
        ]
        },{
        name:'班组考勤统计',
        value:[
            {
                name:'序号',
                value:'rowNum',
                widths:'70'
            },{
                name:'班组',
                value:'TeamName',
                widths:''
            },{
                name:'所属单位',
                value:'CorpName',
                widths:''
            },{
                name:'班组长',
                value:'TeamLeaderName',
                widths:''
            },{
                name:'在场人数',
                value:'zcrs',
                widths:''
            },{
                name:'今日考勤人数',
                value:'AttendNum',
                widths:''
            },{
                name:'考勤率',
                value:'AttendRate',
                widths:''
            }
        ]
        },{
        name:'在场人员统计',
        value:[
            {
                name:'序号',
                value:'rowNum',
                widths:'70'
            },{
                name:'姓名',
                value:'Name',
                widths:''
            },{
                name:'人员类型',
                value:'WorkerType',
                widths:''
            },{
                name:'工种',
                value:'GNAME',
                widths:''
            },{
                name:'班组',
                value:'TeamName',
                widths:''
            },{
                name:'最近进场日期',
                value:'pInDate',
                widths:''
            },{
                name:'最早进场日期',
                value:'FirstDate',
                widths:''
            }
        ]
        },{
        name:'考勤人数',
        value:[
        {
            name:'序号',
            value:'rowNum',
            widths:'70'
        },{
            name:'姓名',
            value:'Name',
            widths:''
        },{
            name:'人员类型',
            value:'WorkerType',
            widths:''
        },{
            name:'工种',
            value:'GNAME',
            widths:''
        },{
            name:'班组',
            value:'TeamName',
            widths:''
        },{
            name:'状态',
            value:'Status',
            widths:''
        },{
            name:'最近打卡时间',
            value:'LastTime',
            widths:''
        },{
            name:'首次打卡时间',
            value:'FirstTime',
            widths:''
        }
        ]
        },{
        name:'人员年龄',
        value:[
        {
            name:'序号',
            value:'rowNum',
            widths:'70'
        },{
            name:'姓名',
            value:'Name',
            widths:'100'
        },{
            name:'人员类型',
            value:'WorkerType',
            widths:''
        },{
            name:'年龄',
            value:'Age',
            widths:'80'
        },{
            name:'性别',
            value:'Gender',
            widths:'80'
        },{
            name:'所属参建单位',
            value:'CorpName',
            widths:''
        },{
            name:'工种',
            value:'GNAME',
            widths:''
        },{
            name:'班组',
            value:'TeamName',
            widths:''
        }
        ]
        }
    ]


    formcout.forEach((item,index)=>{
        if (item.name==params) {
            lablist=item.value
            
        }
    })
    // console.log('获取',lablist);
    
    return lablist
}