<template>
  <div class="container">
        <div :id="ids" style="width:100%;height:100%;"></div>
  </div>
</template>

<script>
import { onMounted,watch,nextTick, ref, onBeforeUnmount } from 'vue'
let myChart
export default {
props:['ids','options','show'],
setup(props){
    // onMounted
 watch(()=>props.options, (newVal, oldVal) => {
        // console.log('监听数据',newVal);
        if (newVal) {
            nextTick(()=>{
                getecharts()
            })
        }
    },
{immediate: true})

let namelist=ref([])
let shows=ref(true)
let fonts=ref('#fff')
const getecharts=()=>{
    let selectedIndex = '';
let hoveredIndex = '';
var echarts = require('echarts');
    myChart = echarts.getInstanceByDom(document.getElementById(props.ids));
    if (myChart == null) {
    myChart = echarts.init(document.getElementById(props.ids));
}
        //  var myChart = echarts.init(document.getElementById(props.ids));
// let options= getPie3D(props.options,0);
let color=['#f77b66','#3edce0','#f94e76','#018ef1',]
 namelist.value= props.options.map((item)=>{
    return item.name
    })
if (props.show==1) {
    shows.value=false
    // fonts.value='#000'
}
// console.log('获取技术管理数据',props.options);

let option = getPie3D(props.options, 0.59);


option.series.push({
		name: 'pie2d',
		type: 'pie',
		labelLine:{
			length:30,
			length2:50
		},
        
        label: {
          opacity: 1,
          fontSize: 13,
          lineHeight: 10,
        },
		startAngle: -20 , //起始角度，支持范围[0, 360]。
		clockwise: false,//饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
		radius: ['40%', '60%'],
		center: ['50%', '40%'],
		data:props.options,
		itemStyle:{
			opacity:0
		}
		});

// 生成扇形的曲面参数方程
function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
    // 计算
    const midRatio = (startRatio + endRatio) / 2;

    const startRadian = startRatio * Math.PI * 2;
    const endRadian = endRatio * Math.PI * 2;
    const midRadian = midRatio * Math.PI * 2;

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
        // eslint-disable-next-line no-param-reassign
        isSelected = false;
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    // eslint-disable-next-line no-param-reassign
    k = typeof k !== 'undefined' ? k : 1 / 3;

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
    const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    const hoverRate = isHovered ? 1.05 : 1;

    // 返回曲面参数方程
    return {
        u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32,
        },

        v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
        },

        x(u, v) {
            if (u < startRadian) {
                return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
                return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        y(u, v) {
            if (u < startRadian) {
                return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
                return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        z(u, v) {
            if (u < -Math.PI * 0.5) {
                return Math.sin(u);
            }
            if (u > Math.PI * 2.5) {
                return Math.sin(u) * h * 0.1;
            }
            // 当前图形的高度是Z根据h（每个value的值决定的）
            return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
        },
    };
}
// 生成模拟 3D 饼图的配置项
function getPie3D(pieData, internalDiameterRatio) {
    const series = [];
    // 总和
    let sumValue = 0;
    let startValue = 0;
    let endValue = 0;
    const legendData = [];
    const k =
        typeof internalDiameterRatio !== 'undefined'
            ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
            : 1 / 3;

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i += 1) {
        sumValue += pieData[i].value;

        const seriesItem = {
            name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
            type: 'surface',
            parametric: true,
            wireframe: {
                show: false,
            },
            pieData: pieData[i],
            pieStatus: {
                selected: false,
                hovered: false,
                k,
            },
        };

        if (typeof pieData[i].itemStyle !== 'undefined') {
            const { itemStyle } = pieData[i];

            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.color !== 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null;
            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.opacity !== 'undefined'
                ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
                : null;

            seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
    }
    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    // console.log(series);
    for (let i = 0; i < series.length; i += 1) {
        endValue = startValue + series[i].pieData.value;

        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            false,
            k,
            // 我这里做了一个处理，使除了第一个之外的值都是10
            series[i].pieData.value === series[0].pieData.value ? 10 : 10
        );

        startValue = endValue;

        legendData.push(series[i].name);
    }

    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    const option = {
        // animation: false,
        tooltip: {
            trigger: "item",
            position: 'bottom',
            formatter: (params) => {
                if (params.seriesName !== 'mouseoutSeries'&&params.seriesName!='pie2d') {
                    // console.log('获取鼠标显示',params.seriesName);
                    return `${ params.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                        params.color
                    };"></span>${option.series[params.seriesIndex].pieData?.value??0} `;
                }else{
                    // show:false
                return '';

                }
                return '';
            },
        },
        label: {
            show: true,
            // color:'#fff',
            color: color[1],
            position:'right',
            // distance:-10,
            // top:'-15%',
            offset:[0, 1] ,
            formatter: [
            "{b|{b}}",
            // "————",
            "{d|{d}%}",
            ].join("\n"), // 用\n来换行
            rich: {
              b: {
                color: fonts.value,
                padding: [0, -40, 4, -40]
              },
              c: {
                fontSize: 18,
                color: fonts.value,
                padding: [0, 0, 4, -40]
              },
              d: {
                color:fonts.value,
                padding: [4, -40, 0, -40]
              },
            },
        },
        legend: {
            show:shows.value,
            top: 10,
            left: 'left',
            orient: 'vertical',
            // data: ['cc', 'aa', 'bb', 'ee']
            // y: '85%', //延Y轴居中
            // x: '5%',
            // position:'left',
            itemWidth: 12, // 设置宽度
            itemHeight: 12, // 设置高度
            textStyle: {
                color: '#ffffff',
                fontSize: 12,
            },
            data: namelist.value,
            // orient: 'vertical',
            itemGap: 10,
            textStyle: {
                color: '#A1E2FF',
            },

        },
        // tooltip: {
        //     show:false
        // },
        xAxis3D: {
            min: -1,
            max: 1,
        },
        yAxis3D: {
            min: -1,
            max: 1,
        },
        zAxis3D: {
            min: -1,
            max: 1,
        },
        grid3D: {
            show: false,
            boxHeight: 10,
            top: '-15%',
            // bottom: '10%',
            viewControl: {
                // 3d效果可以放大、旋转等，请自己去查看官方配置
                alpha: 33,
                // beta: 30,
                rotateSensitivity: 1,
                zoomSensitivity: 0,
                panSensitivity: 0,
                autoRotate: false,
                distance: 150,
            },
        },
        series,
    };
    return option;
}
myChart.setOption(option)

    window.addEventListener("resize", function() {
      myChart?.resize();
    });
}
onBeforeUnmount(()=>{
    myChart?.dispose()
})
    return{
        shows,
        fonts,

        namelist,
        getecharts
    }
}
}
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    height: 90%;
            // background-color: #000000;
    // position: relative;
    }
    #organization{
    z-index: 1!important;
    }
    #disclosure{
    z-index: 1!important;

    }
    #weathersechart{
    z-index: 1!important;

    }
    
</style>