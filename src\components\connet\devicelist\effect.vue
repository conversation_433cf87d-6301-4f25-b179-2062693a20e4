<template>
  <!-- 设备功效分析 -->
  <div class="effect padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"></Chamfering>
    <div class="effect-two">
      <div class="echatr">
       <el-table :data="gridData"  :style="['width: 100%',`color:${bgcolor.font};
        --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
        :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
        empty-text="暂无数据"
        >
            <el-table-column width="" property="EquipName" label="设备名称" />
            <el-table-column width="" property="EquipCode" label="设备编号" />
            <el-table-column width="" property="WorkingHours" label="工作时间（h）" />
            <el-table-column width="" property="PowerRate" label="实时功效" />
        </el-table>
      </div>
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>

  </div>
</template>

<script>
// import echartsAttendance from "@/components/connet/Common/echartscom.vue";
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import workecharts from "@/components/connet/personnelcon/echarts3d/workerkq.vue";
// import attecahrt from "@/components/connet/personnelcon/echarts3d/attecahrt.vue";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
props:['homeindex'],
components:{
// echartsAttendance
workecharts,
Chamfering
// echartsss
},
setup(){

  let bgcolor=ref({})
  let falge=ref(0)
  let echart=ref(true)
  let countlist=ref([])
  let loading=ref(false)
  let getform=ref({
      ProjectCode:store.getters.code,

    })
  let gridData=ref([
    {
      date:'415'
    },
    {
      date:'415'
    },
    {
      date:'415'
    }
  ])
  let topforms=ref({
    url:require('@/assets/img/device/effect.png'),
    name:"设备功效分析"
  })
  // let btnlist=['全部人员','管理人员','建筑工人']
  let addteion=ref([])
  let Attendslist=ref([])
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

      // 检查浏览器是否支持 WEBGL_lose_context 扩展
    // let loseContextExtension = gl.getExtension('WEBGL_lose_context');
    // if (loseContextExtension) {
    //   // 释放当前 WebGL 上下文
    //   loseContextExtension.loseContext();
    // }
    // getAttendance()
	  getsunm()
    // getmangen()
    // getconstruction()
  })
  const getsunm=async()=>{
    const {data:res}=await gettable('GetCranePowerAnalysis',getform.value)
    // console.log('功效',res);
    gridData.value=res.data
    
  }
 const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }

	return{
		falge,
    echart,
		countlist,
		getform,
		bgcolor,
    loading,
    // btnlist,
    addteion,
    gridData,
    topforms,

  tableRowClassName
	}
}
}
</script>
<style lang="scss">
.effect{
.el-table{
    --el-table-tr-bg-color:transparent!important;
    // border:1px solid transparent!important;
    // --el-table-border-color:transparent;
    --el-table-row-hover-bg-color:rgba(1, 194, 255, 0.6);
    --el-table-bg-color:transparent;
}
.el-table .warning-row {
//   --el-table-tr-bg-color: #000 !important;
  background: rgba(15, 43, 63, 0.6)!important;
}

}
</style>
<style lang="scss" scoped>
// :deep(.heighttop){
//     height: 7.1%!important;
// }
.effect{
&-two{
  height: 90%;
  .lables{
    display: flex;
    flex-wrap: wrap;
  }
  
  &-label{
    display: flex;
    align-items: center;
    padding: 5px;
    .label-center{
      width: 10px;
      height: 10px;
      border-radius: 100%;
      border: 1px solid #000;
    }
    .line{
        width: 5px;
        height: 2px;
        background: #000;
      }
  &-one{
    display: flex;
    align-items: center;
    // position: relative;
  }
  }
  &-top{
    height: 27%;
    font-size: 12px;
  }
  &-btn{
    padding: 10px;
    width: 80%;
    position: relative;
    border: 2px solid #03558F;
    // margin: 10px;
  }
  .bgcolorcz{
        background: #F29961;

    }
    .bgcolorsj{
          background: #4582ff;

    }
}
}
.echatr{
    margin-top: 10px;
}

</style>