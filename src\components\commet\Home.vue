<template>
  <div class="model" v-if="destorys">
    <div class="leftmodel left wid" v-show="amplify==0">
        <fault class="Homebgco" v-if="showname('faults').name=='faults'" 
        :style="`order:${showname('faults').index};${bgstyleleft()}`" :homeindex="showname('faults').index"></fault>
        <weather class="Homebgco" v-if="showname('weather').name=='weather'" 
        :style="`order:${showname('weather').index};${bgstyleleft()}`"  :homeindex="showname('weather').index"></weather>

        <Attendance  class="Homebgco"  v-if="showname('Attendance').name=='Attendance'" 
        :style="`order:${showname('Attendance').index};${bgstyleleft()}`"  :homeindex="showname('Attendance').index"></Attendance>
        <Equipment  class="Homebgco" v-if="showname('Equipment').name=='Equipment'" 
        :style="`order:${showname('Equipment').index};${bgstyleleft()}`"  :homeindex="showname('Equipment').index"></Equipment>
        <Imageprogress  class="Homebgco" v-if="showname('Imageprogress').name=='Imageprogress'"
         :style="`order:${showname('Imageprogress').index};${bgstyleleft()}`"  :homeindex="showname('Imageprogress').index"></Imageprogress>
        <produce class="Homebgco" v-if="showname('produce').name=='produce'"
         :style="`order:${showname('produce').index};${bgstyleleft()}`"  :homeindex="showname('produce').index"></produce>
        <project class="Homebgco" v-if="showname('project').name=='project'"
         :style="`order:${showname('project').index};${bgstyleleft()}`"  :homeindex="showname('project').index"></project>
        <quality class="Homebgco" v-if="showname('quality').name=='quality'" 
        :style="`order:${showname('quality').index};${bgstyleleft()}`"  :homeindex="showname('quality').index"></quality>
        <scene class="Homebgco" v-if="showname('scene').name=='scene'" 
        :style="`order:${showname('scene').index};${bgstyleleft()}`"  :homeindex="showname('scene').index"></scene>
        <secure class="Homebgco" v-if="showname('secure').name=='secure'"
         :style="`order:${showname('secure').index};${bgstyleleft()}`"  :homeindex="showname('secure').index"></secure>
        
    </div>
    <div class="homecontent" :style="amplify==0?'width:60%':'width:100%'">
      <concer @getamplify="getamplify"></concer>
    </div>
    <div class="rightmodel right wid" v-show="amplify==0">
      <fault class="Homeright" v-if="showright('faults').name=='faults'" 
        :style="`order:${showright('faults').index};${bgstltleright()}`" 
         :homeindex="showright('faults').index+3"></fault>
      <weather class="Homeright" v-if="showright('weather').name=='weather'" 
        :style="`order:${showright('weather').index};${bgstltleright()}`"  
        :homeindex="showright('weather').index+3"></weather>
      <Attendance class="Homeright" v-if="showright('Attendance').name=='Attendance'" 
        :style="`order:${showright('Attendance').index};${bgstltleright()}`"   
        :homeindex="showright('Attendance').index+3"></Attendance>
      <Equipment class="Homeright" v-if="showright('Equipment').name=='Equipment'" 
        :style="`order:${showright('Equipment').index};${bgstltleright()}`"  
        :homeindex="showright('Equipment').index+3"></Equipment>
      <Imageprogress class="Homeright" v-if="showright('Imageprogress').name=='Imageprogress'" 
        :style="`order:${showright('Imageprogress').index};${bgstltleright()}`"  
        :homeindex="showright('Imageprogress').index+3"></Imageprogress>
      <produce class="Homeright" v-if="showright('produce').name=='produce'" 
        :style="`order:${showright('produce').index};${bgstltleright()}`" 
         :homeindex="showright('produce').index+3"></produce>
      <project class="Homeright" v-if="showright('project').name=='project'" 
        :style="`order:${showright('project').index};${bgstltleright()}`"  
        :homeindex="showright('project').index+3"></project>
      <quality class="Homeright" v-if="showright('quality').name=='quality'" 
        :style="`order:${showright('quality').index};${bgstltleright()}`"  
        :homeindex="showright('quality').index+3"></quality>
      <scene class="Homeright" v-if="showright('scene').name=='scene'" 
        :style="`order:${showright('scene').index};${bgstltleright()}`" 
         :homeindex="showright('scene').index+3"></scene>
      <secure class="Homeright" v-if="showright('secure').name=='secure'" 
        :style="`order:${showright('secure').index};${bgstltleright()}`" 
         :homeindex="showright('secure').index+3"></secure>
    </div>

  </div>
</template>

<script>
import fault from "@/components/connet/Home/faults.vue";
import Attendance from "@/components/connet/Home/Attendance.vue";
import Equipment from "@/components/connet/Home/Equipment.vue";
import Imageprogress from "@/components/connet/Home/Imageprogress.vue";
import produce from "@/components/connet/Home/produce.vue";
import project from "@/components/connet/Home/project.vue";
import quality from "@/components/connet/Home/quality.vue";
import scene from "@/components/connet/Home/scene.vue";
import secure from "@/components/connet/Home/secure.vue";
import weather from "@/components/connet/Home/weather.vue";
import concer from "@/components/connet/Home/content/concer.vue";
import { onMounted, ref ,computed, onBeforeUnmount, nextTick} from 'vue';

export default {
  components:{
    fault,
    Attendance,
    Equipment,
    Imageprogress,
    produce,
    project,
    quality,
    scene,
    secure,
    weather,
    concer
  },  
  setup(){
    let bgcolor=ref({})
    let amplify=ref(0)
    let destorys=ref(true)
    window.addEventListener('setItem', ()=> {
      // console.log('项目引导');
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
   })
   window.addEventListener('setthcolor', ()=> {
      // console.log('主题切换');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      nextTick(()=>{
        bgstyleleft()
      bgstltleright()
      // console.log('获取',bgstyleleft());

      })
      
      
   })
    onMounted(()=>{
      destorys.value=true
      // let divs =document.querySelector('.leftmodel')
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      // console.log('主题',themelist.value);
      
    
      // console.log('计算竖向',showname('weather')[0].name);

    })
    const bgstyleleft=()=>{
      // console.log('调用',bgcolor);
      
      return `background:linear-gradient(90deg, ${bgcolor.value.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`
    }
    const bgstltleright=()=>{
      return `background:linear-gradient(90deg,
         rgba(1, 194, 255, 0) 0%,${bgcolor.value.bgcolor} 97%)`
    }
    const getamplify=(val)=>{
      // console.log('放大',val);
      
      amplify.value=val
    }
    const showname=(val)=>{
      let name=[]
      // themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      // console.log('控制',themelist.value);
      
      if (themelist.value) {
      themelist.value.filter((item,index)=>{
        if (index<3) {
          if (item.name==val) {
          // console.log('符合条件',item);
            name=item
          }
        }

      })
      }

    return name
    }
    // 右侧项目
    const showright=(val)=>{
      let name=[]
      // themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      if (themelist.value) {

      themelist.value.filter((item,index)=>{
        if (index>=3) {
          if (item.name==val) {
            // console.log('显示',item);
            name=item
          }
        }
      })
      }

    return name
    }
      
    let themelist=ref([])
   const  open=()=>{
      // this.$store.dispatch('openmask',1)
      // this.$sseion('statemask', 1);
      // sseion()
    }
    onBeforeUnmount(()=>{
      // console.log('首页销毁');
      destorys.value=false
    })

    // let listlable=ref(['<fault></fault>'])
    return{
      bgstyleleft,
      bgstltleright,
      destorys,
      amplify,
      bgcolor,
      themelist,
      showname,
      showright,
      open,
      getamplify
    }
  },
}
</script>
<style lang="scss">


@mixin homeflex($deg) {
  height: 31.3%;
  width: 95%;
  opacity: 1;
  margin:7px 10px;
  background: linear-gradient($deg, #0096C7 7%, rgba(0,52,75,0.00) 97%);
}
@mixin leftdjx($left,$right,$top,$bottom,) {
  position: absolute;
  content: '';
  display: block;
  left: $left;
  top: $top;
  right: $right;
  bottom: $bottom;
  width: 10.06px;
  height: 10.84px;
  opacity: 1;
}

.Homebgco{
  position: relative;
  @include homeflex(90deg)
  }
.Homeright{
  position: relative;
  @include homeflex(-90deg);

}
</style>
<style lang="scss" scoped>
.model{
  width: 100%;
  height: 100%;
  display: flex;
  .wid{
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .homecontent{
    width: 60%;
    height: 100%;
    // position: relative;
  }
}
</style>