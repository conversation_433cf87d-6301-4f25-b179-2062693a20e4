<template>
  <!-- 项目进度 -->
  <div class="Imageprogress padding" :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="topforms"></Chamfering>
    <div class="Imageprogress-two">
      <el-carousel >
        <el-carousel-item v-for="item in imglist" :key="item" >
          <img class="imageprimg" :src="item.ImgUrl" alt="Sample image" style="width:100%;height:100%">
        </el-carousel-item>
      </el-carousel>
    </div>
     <Chamfering :homeindex="homeindex" :horn="0"></Chamfering>
  </div>
</template>

<script>
import { onMounted, ref } from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
props:['homeindex'],
components:{
  Chamfering
},
setup(){
   let bgcolor=ref({})
   let getform=ref({
    ProjectCode:store.getters.code,
    PageModular:'首页宣传图',
    PageName:'首页',
    IsShow:'1',
    page:1,
    count:1000,
    ReleaseDate:'',

   })
   let imglist=ref([])
  let topforms=ref({
    url:require('@/assets/img/home/<USER>'),
    name:'形象进度照片'
  })
window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
   onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getimglist()
   })
   const getimglist=async ()=>{
        const {data:res}=await gettable('GetForegroundData',getform.value)
        // console.log('返回数据',res);
        
        if (res.code=="1000") {
          imglist.value=res.PublicityMap

        }
   }
   return{
    getform,
    imglist,
      bgcolor,
    topforms,
      getimglist
   }
}
}
</script>

<style lang="scss" scoped>
.Imageprogress{
  &-two{
    height: 85%;
    padding-top: 10px;
  }
}
.carimg{
  // position: relative;
  // overflow: hidden;
}

.imageprimg{
}
.el-carousel{
  height: 100%;
}
:deep(.el-carousel__container){
  height: 100%!important;
}
</style>