﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="25px" height="20px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip43">
      <path d="M 5 0  L 17 0  L 18.999999999999996 0.8  L 25 10  L 18.999999999999996 19.2  L 17 20  L 5 20  C 2.1999999999999997 20  0 17.8  0 15  L 0 5  C 0 2.1999999999999997  2.1999999999999997 0  5 0  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -814 -1384 )">
    <path d="M 5 0  L 17 0  L 18.999999999999996 0.8  L 25 10  L 18.999999999999996 19.2  L 17 20  L 5 20  C 2.1999999999999997 20  0 17.8  0 15  L 0 5  C 0 2.1999999999999997  2.1999999999999997 0  5 0  Z " fill-rule="nonzero" fill="rgba(232, 100, 82, 1)" stroke="none" transform="matrix(1 0 0 1 814 1384 )" class="fill" />
    <path d="M 5 0  L 17 0  L 18.999999999999996 0.8  L 25 10  L 18.999999999999996 19.2  L 17 20  L 5 20  C 2.1999999999999997 20  0 17.8  0 15  L 0 5  C 0 2.1999999999999997  2.1999999999999997 0  5 0  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(2, 166, 181, 0.14901960784313725)" fill="none" transform="matrix(1 0 0 1 814 1384 )" class="stroke" mask="url(#Clip43)" />
  </g>
</svg>