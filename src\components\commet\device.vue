<template>
  <div class="model" >
    <div class="leftmodel left wid"  v-show="amplify==0">
        <Equipment  class="Homebgco" 
        :style="getstyle(bgcolor.bgcolor,'rgba(0,52,75,0.00)')"  :homeindex="'1'"></Equipment>
        <teamslistss  class="Homebgco" :style="getstyle(bgcolor.bgcolor,'rgba(0,52,75,0.00)')" :teamtype="eqment"></teamslistss>
        <teamslistss  class="Homebgco" :style="getstyle(bgcolor.bgcolor,'rgba(0,52,75,0.00)')" :teamtype="eqment1"></teamslistss>
    </div>
    <div class="homecontent"  :style="amplify==0?'width:60%':'width:100%'">
      <concer  @getamplify="getamplify2" :showcontent="1"></concer>
    </div>
    <div class="rightmodel right wid"  v-show="amplify==0">
      <monitor class="Homeright" 
        :style="getstyle('rgba(1, 194, 255, 0)',bgcolor.bgcolor)" ></monitor>
      <effect class="Homeright" 
        :style="getstyle('rgba(1, 194, 255, 0)',bgcolor.bgcolor)"  ></effect>
      <maintenance class="Homeright" 
        :style="getstyle('rgba(1, 194, 255, 0)',bgcolor.bgcolor)" ></maintenance>
    </div>

  </div>
</template>

<script>
import Equipment from "@/components/connet/Home/Equipment.vue";
// import analysis from "@/components/connet/devicelist/analysis.vue";
// import historywaing from "@/components/connet/devicelist/historywaing.vue";
import monitor from "@/components/connet/devicelist/monitor.vue";
import effect from "@/components/connet/devicelist/effect.vue";
import maintenance from "@/components/connet/devicelist/maintenance.vue";
// import content from "@/components/connet/devicelist/content/content.vue";
import teamslistss from "@/components/connet/personnelcon/teamslist.vue";

import concer from "@/components/connet/Home/content/concer.vue";

import { onMounted, ref ,computed} from 'vue';

export default {
components:{
        Equipment,
        // analysis,
        // historywaing,
        monitor,
        effect,
        maintenance,
        teamslistss,
        concer
    },
setup(){
    let themelist=ref([])

    let bgcolor=ref({})
    let amplify=ref(0)
    let eqment=ref({
      src:require('@/assets/img/device/analysis.png'),
      titles:'告警类型统计分析',
      type:'设备',
      ids:'Typeworkcharts'
    })
    let eqment1=ref({
      src:require('@/assets/img/device/historywaing.png'),
      titles:'设备告警统计分析',
      type:'设备',
      ids:'historywaingcharts'
    })
    window.addEventListener('setItem', ()=> {
      // console.log('项目引导');
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
   })
   window.addEventListener('setthcolor', ()=> {
      // console.log('主题切换');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(()=>{
      // let divs =document.querySelector('.leftmodel')
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    const getstyle=(val,value)=>{
      return `background:linear-gradient(90deg,${val} 0%,${value} 97%)`
    }
    const getamplify2=(val)=>{
      // console.log('放大',val);
      
      amplify.value=val
    }
    return{
        bgcolor,
        eqment,
        eqment1,
        themelist,
        amplify,

        getstyle,
        getamplify2
    }
}
}
</script>
<style lang="scss" scoped>
@mixin style($width,$height){
  width:$width;
  height:$height
}

@mixin homeflex($deg) {
  @include style(95%,31.3%);
  opacity: 1;
  margin:7px 10px;
  background: linear-gradient($deg, #0096C7 7%, rgba(0,52,75,0.00) 97%);
}
@mixin leftdjx($left,$right,$top,$bottom,) {
  position: absolute;
  content: '';
  display: block;
  left: $left;
  top: $top;
  right: $right;
  bottom: $bottom;
  @include style(10.06px,10.84px);
  opacity: 1;
}

.Homebgco{
  position: relative;
  @include homeflex(90deg)
  }
.Homeright{
  position: relative;
  @include homeflex(-90deg);

}
.model{
  @include style(100%,100%);
  display: flex;
  .wid{
    @include style(20%,100%);
    display: flex;
    flex-direction: column;
  }
  .homecontent{
    @include style(60%,100%);
  }
}
</style>