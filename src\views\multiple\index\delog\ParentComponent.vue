<template>
  <div class="parent-container">
    <h2>父组件示例</h2>
    
    <!-- 显示从子组件接收到的数据 -->
    <div v-if="receivedData" class="received-data">
      <h3>从地图组件接收到的数据：</h3>
      <pre>{{ JSON.stringify(receivedData, null, 2) }}</pre>
    </div>
    
    <!-- 地图组件 -->
    <MapLocation 
      ref="mapRef"
      @back="handleBack"
      @close="handleClose"
      @locationChange="handleLocationChange"
      @markerClick="handleMarkerClick"
      @mapReady="handleMapReady"
    />
    
    <!-- 控制按钮 -->
    <div class="controls">
      <button @click="showLocation">显示指定位置</button>
      <button @click="clearData">清除数据</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MapLocation from './maploaction.vue'

const mapRef = ref(null)
const receivedData = ref(null)

// 处理返回事件
const handleBack = () => {
  console.log('父组件：接收到返回事件')
  receivedData.value = {
    event: 'back',
    timestamp: Date.now(),
    message: '子组件触发了返回事件'
  }
  
  // 在这里处理返回逻辑，比如：
  // - 隐藏地图组件
  // - 显示其他组件
  // - 路由跳转等
}

// 处理关闭事件（带参数）
const handleClose = (data) => {
  console.log('父组件：接收到关闭事件', data)
  receivedData.value = {
    event: 'close',
    data: data,
    timestamp: Date.now()
  }
}

// 处理位置变化事件
const handleLocationChange = (locationData) => {
  console.log('父组件：接收到位置变化事件', locationData)
  receivedData.value = {
    event: 'locationChange',
    data: locationData,
    timestamp: Date.now()
  }
}

// 处理标记点击事件
const handleMarkerClick = (markerData) => {
  console.log('父组件：接收到标记点击事件', markerData)
  receivedData.value = {
    event: 'markerClick',
    data: markerData,
    timestamp: Date.now()
  }
}

// 处理地图准备就绪事件
const handleMapReady = (mapData) => {
  console.log('父组件：地图准备就绪', mapData)
  receivedData.value = {
    event: 'mapReady',
    data: mapData,
    timestamp: Date.now()
  }
}

// 调用子组件方法
const showLocation = () => {
  if (mapRef.value) {
    // 调用子组件的方法
    mapRef.value.showdelog({
      longitude: 120.161693,
      lat: 30.279429,
      projectName: '测试项目',
      status: '1',
      operationCompanyName: '测试公司',
      devicePoleName: '测试设备',
      mnCode: 'TEST001',
      districtName: '西湖区'
    })
  }
}

// 清除显示的数据
const clearData = () => {
  receivedData.value = null
}
</script>

<style scoped>
.parent-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.received-data {
  background: #f5f5f5;
  padding: 15px;
  margin: 10px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.received-data h3 {
  margin-top: 0;
  color: #409eff;
}

.received-data pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.controls {
  display: flex;
  gap: 10px;
  padding: 10px;
  background: #f9f9f9;
}

.controls button {
  padding: 8px 16px;
  border: 1px solid #409eff;
  background: #409eff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.controls button:hover {
  background: #66b1ff;
}
</style>
