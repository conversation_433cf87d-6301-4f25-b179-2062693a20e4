<template>
  <!-- 人员年龄 -->
  <div class="Agepersonnel padding" :style="{color:bgcolor.font}" >
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"></Chamfering>

    <div class="Agepersonnel-two">
      <div class="Agepersonnel-two-top">
        <div class="Agepersonnel-two-top1">
          <span class="Agepersonnel-two-top1-span">在场人数：{{forms.zcrs}}例</span>
          <span class="Agepersonnel-two-top1-span">男性员工：{{forms.nanCount}}</span>
          <span class="Agepersonnel-two-top1-span"> 女性员工：{{forms.nvCount}}</span>
        </div>
      </div>
      <div class="echatr">
        <div v-if="falge" id="echartsperson" ></div>
      </div>
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    <perdelog ref="perdelog"></perdelog>
  </div>
</template>

<script>
import baseCharts from "@/components/connet/Common/baseCharts.vue";
import doubleecaharts from "@/components/connet/Common/echartscom.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import { onMounted, ref,getCurrentInstance, onBeforeUnmount,shallowRef } from 'vue';
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import perdelog from "@/components/connet/personnelcon/content/perdelog.vue";
export default {
props:['homeindex'],
components:{
  baseCharts,
  doubleecaharts,
  Chamfering,
  perdelog
},
setup(){
    const baseChartsRef = ref(null)
    const baseChartsRefPlus = ref(null)
    let options=ref({})
    let bgcolor=ref({})
    let falge=ref(true)
    let getform=ref({
      ProjectCode:store.getters.code,
      InUserName:store.getters.username
    })
    let agelist=ref([])
    let forms=ref({})
    let topforms=ref({
      url:require('@/assets/img/personnelcon/Agepersonnel.png'),
      name:"人员年龄分析"
    })
    let perdelog=ref(null)  // 添加 perdelog 的 ref 声明
    let myChart=shallowRef(null)  // shallowRef更容易处理echarts复杂的问题不会破坏结构
    let resizeHandler=shallowRef(null)  // 使用shallowRef才能正常展示tooltip 一开始用的ref,就是不行
    window.addEventListener('setthcolor', ()=> {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(() => {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      // console.log('人员管理');
      getagedata()
    })

    // 组件销毁前清理echarts实例
    onBeforeUnmount(() => {
      // 销毁echarts实例
      if (myChart.value) {
        myChart.value.dispose(); // 销毁echarts实例
        myChart.value = null;
      }
      // 移除窗口resize事件监听器
      if (resizeHandler.value) {
        window.removeEventListener("resize", resizeHandler.value);
        resizeHandler.value = null;
      }
    })
    const getagedata=async()=>{
      // falge.value=false

        const {data:res}=await gettable('GetWorkerByGenderAge',getform.value)
        // console.log('获取年龄',res);
        
        // agelist.value=res.data
        forms.value=res.data
        // console.log('获取年龄',res);
        getEchart()
    }
    const getEchart=()=>{
      // console.log('触发');
    var echarts = require('echarts');
    if (document.getElementById('echartsperson')) {

    let chartInstance = echarts.getInstanceByDom(document.getElementById('echartsperson'));
      if (chartInstance == null) {
        chartInstance = echarts.init(document.getElementById('echartsperson'));
      }
      // 将实例保存到ref中
      myChart.value = chartInstance;
      // 绑定点击事件
      myChart.value.on('click', function(params) {
        // console.log("点击事件触发，原始参数:", params);

        // 只处理bar类型的系列点击，忽略装饰性的pictorialBar
        if (params.seriesType === 'bar') {
          // console.log("有效的bar点击:", params);
          perdelog.value.showdelog(params,'人员年龄');
        }
      });
          // var option;
      var xData = forms.value.name;
      var data0 = [1, 1, 1, 1, 1, 1];
      var data1 = forms.value.value1; //上衣
      var data4 = forms.value.value2; //裤子
      var getvalue1 = data1; // 裙子
var data3 = [];
var data5 = [];
for (let i = 0; i < data1.length; i++) {
    data3.push(data1[i] + data4[i]);
}
for (let i = 0; i < data1.length; i++) {
    data5.push(data1[i]);
}
var maxnum1 = Math.max.apply(null, getvalue1);
var maxlen1 = Math.pow(10, String(Math.ceil(maxnum1)).length - 2);
if (maxnum1 >= 5) {
    var max1 = Math.ceil(maxnum1 / (10 * maxlen1)) * maxlen1 * 10;
} else {
    var max1 = 5;
}
let option = {
    // backgroundColor: '#000E1A', //背景色
    tooltip: {
        trigger: 'axis',
        borderColor: 'rgba(255,255,255,.3)',
        backgroundColor: 'rgba(13,5,30,.6)',
        textStyle: {
            color: 'white', //设置文字颜色
        },
        borderWidth: 1,
        padding: 5,
        formatter: function (parms) {
            var str =
                '年龄：' +
                parms[0].axisValue +
                '</br>' +
                '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#06fbfe;"></span>' +
                '男：' +
                parms[0].value +
                '</br>' +
                parms[1].marker +
                '女：' +
                parms[1].value +
                '</br>'
            return str;
        },
    },
    textStyle: {
        color: '#C9C9C9',
    },

    // color: ['#fbc292', '#06fbfe',  '#f06e91'],
    legend: {
        show:false,
    },
    grid: {
        containLabel: true,
        left: '2%',
        top: '2%',
        bottom: '2%',
        right: '2%',
    },
    xAxis: {
        type: 'category',
        data: xData,
        axisLine: {
            show: false,
            lineStyle: {
                color: '#B5B5B5',
            },
        },
        axisTick: {
            show: false,
        },
        axisLabel: {
          interval:0,
            // margin: 20, //刻度标签与轴线之间的距离。
            textStyle: {
                fontFamily: 'Microsoft YaHei',
                color: '#FFF',
            },
            fontSize: 14,
            fontStyle: 'bold',
        },
    },
    yAxis: [
        {
            type: 'value',
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#B5B5B5',
                },
            },
            splitLine: {
                show: false,
            },
            axisLabel: {
                show: false,
                textStyle: {
                    fontFamily: 'Microsoft YaHei',
                    color: '#FFF',
                },
                fontSize: 14,
            },
        },
    ],
    series: [
        {
            type: 'bar',
            name: '女',
            data: data1,
            stack: 'zs',
            barMaxWidth: 'auto',
            barWidth: 20, // 修改为20
            // 确保这是可点击的系列
            silent: false,
            itemStyle: {
                color: {
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    type: 'linear',
                    global: false,
                    colorStops: [
                        {
                            offset: 0,
                            color: '#017ebb',
                        },
                        {
                            offset: 1,
                            color: '#06fbfe',
                        },
                    ],
                },
            },
        },

        {
            name: '男',
            type: 'bar',
            data: data4,
            stack: 'zs',
            barMaxWidth: 'auto',
            barWidth: 20, // 修改为20
            // 确保这是可点击的系列
            silent: false,
            itemStyle: {
                color: {
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    type: 'linear',
                    global: false,
                    colorStops: [
                        {
                            offset: 0,
                            color: '#fbc292',
                        },
                        {
                            offset: 1,
                            color: '#f06e91',
                        },
                    ],
                },
            },
        },
        {
            data: data0,
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbol: 'diamond',
            symbolOffset: [0, '50%'],
            symbolSize: [20, 5], // 修改第一个参数为20
            zlevel: 2,
            silent: true, // 禁用点击事件
            itemStyle: {
                normal: {
                    color: '#06fbfe',
                },
            },
        },
        {
            data: data1,
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [20, 5], // 修改第一个参数为20
            zlevel: 2,
            silent: true, // 禁用点击事件
        },
        {
            data: data1,
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [20, 5], // 修改第一个参数为20
            zlevel: 2,
            silent: true, // 禁用点击事件
        },
        {
            data: data5,
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [0, 5],
            zlevel: 2,
            silent: true, // 禁用点击事件
        },
        {
            data: data5,
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [20, 5], // 修改第一个参数为20
            zlevel: 2,
            silent: true, // 禁用点击事件
            itemStyle: {
                normal: {
                    color: '#06fbfe',
                },
            },
        },
        {
            data: data3,
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [20, 5], // 修改第一个参数为20
            zlevel: 2,
            silent: true, // 禁用点击事件
            itemStyle: {
                normal: {
                    color: '#fbc292',
                },
            },
        },
    ],
};
myChart.value.setOption(option);

  // 创建resize事件处理器
  resizeHandler.value = function() {
    if (myChart.value) {
      myChart.value.resize();
    }
  };

  // 添加resize事件监听器
  window.addEventListener("resize", resizeHandler.value);
}       
}

    
    return {
      agelist,
      bgcolor,
      options,
      falge,
      forms,
      topforms,
      perdelog,
      myChart,
      resizeHandler,

      getEchart,
      getagedata,
      baseChartsRef,
      baseChartsRefPlus,
      // getbars,
    };
}
}
</script>
<style lang="scss" scoped>
.Agepersonnel{
&-two{
    height: 85%;
    &-top1{
      display: flex;
      // flex-wrap: wrap;
      font-size: 12px;
      color: #A8D6FF;
      margin: 5px;
      &-span{
        display: inline-block;
        width: 33.3%;
        margin: 5px;
      }
    }
    .bgcolorcz{
          background: #00DEEE;

    }
    .bgcolorsj{
          background: #4582ff;

    }
  }
}
.echatr{
  width: 100%;
  height: 80%;
}
#echartsperson{
 width: 100%;
 height: 100%; 
}
.schart-box {
  display: inline-block;
  margin: 20px;
}
.content-title {
  clear: both;
  font-weight: 400;
  line-height: 50px;
  margin: 10px 0;
  font-size: 22px;
  color: #1f2f3d;
}
</style>