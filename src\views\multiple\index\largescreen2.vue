<template>
    <div class="large-bg">
        <el-container>
            <el-header>
                <dv-decoration-8 class="abs abs1" style="width:25%;height:50px;" />
                <h2 class="title">安全云积分驾驶舱</h2>
                <dv-decoration-5 style="width:500px;height:40px;" />
                <dv-decoration-8 class="abs abs2" :reverse="true" style="width:25%;height:50px;" />
            </el-header>
            <el-container>
                <el-aside width="20%" class="padding" :style="{ height: asideHeight+'px' }">
                    <div class="star-title">
                        <DiamondDecoration color="#00ffff" :size="8" :middleSize="10" />
                        <p>月安全之星</p>
                    </div>
                    <div class="month">
                        <div v-for="(item,index) in forms" :key="index" class="month-item" :style="`color:${color[index]}`">
                            <h4>{{item.name}}</h4>
                            <h4>{{item.value}}</h4>
                            <div :class="`icon`" :style="`${getcolor(index)}`">
                                <i :class="`iconfont icon-jiangbei`"
                                 :style="getstyle(index)"></i>
                            </div>
                        </div>
                    </div>
                    <div class="star-title ranking">
                        <DiamondDecoration color="#00ffff" :size="8" :middleSize="10" />
                        <p>人员实时积分排行</p>
                        <el-button v-for="(item,index) in leftlable" :key="index" :class="`btns${index}`" link  @click="changbtn(item,index,0)"
                         :type="getform.RankType == item.value ? 'success' : 'primary'">{{item.name}}</el-button>
                    </div>
                    <div class="shop-content">
                        <span v-for="(item,index) in pageturning" :key="index" :style="`color:${item.color}`">{{ item.name }}</span>
                        <div class="counter-container " v-for="(item,i) in pageturning" :key="i">
                            <template v-for="(digit, index) in formattedCounts[item.value]" :key="index">
                                <div class="digit-box" :style="{'background-color':item.bgcolor}">
                                    <div :class="['digit-flip-container', animationStates[item.value] && animationStates[item.value][index] ? 'flip-animate' : '']">
                                        <div class="digit-current">{{ digit }}</div>
                                        <div class="digit-next">{{ getNextDigit(index, item.value) }}</div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="center-section-top" :style="{height:asideboard}">
                        <ScrollTable ref="scrollTableRef" class="scroll-ranking"
                        :style="`height: ${scrollleft}px;`" :config="config" @resize="handleResize"
                        >
                            <template #cell="{ row, columnIndex }">
                                <div v-if="columnIndex == 0" class="ranking0" :style="updateRankStyles(row.index)">
                                    <span>{{ row.index}}</span>
                                </div>
                                <span v-else-if="columnIndex == 1">{{row.name}}</span>
                                <div v-else-if="columnIndex == 2" class="ranking2">
                                    <div class="ranking2-info" :style="getwidth(row)"></div>
                                </div>
                            </template>
                        </ScrollTable>
                    </div>
                </el-aside>
                <el-main class="padding" :style="{ height: asideHeight+'px' }">
                    <div class="scroll-text-container">
                        <div class="scroll-text" :style="gettext()">
                            {{scrolltitle.SloganContent}}
                        </div>
                    </div>
                    <div class="content-one">
                        <div v-for="(item,index) in goodsType" :key="index" class="content-one-item" :style="{
                            width:  '135px',height: '165px'
                        }">
                            <i :class="`fonts iconfont ${icons[index]}`" :style="`color:${$colorlist()[index]}`"></i>
                            <p class="text-p" :style="`color:${$colorlist()[index]}`">{{item.Name}}</p>
                            <h2 class="texts" :style="`--shadow-color: ${$colorlist()[index]};}`">{{item.Count}}</h2>
                        </div>
                    </div>
                    <div class="content-two" :style="{ height: mainboard }">
                        <div class="content-two-box">
                            <div class="star-title">
                                <DiamondDecoration color="#00ffff" :size="8" :middleSize="10" />
                                <p>积分获取来源</p>
                            </div>
                            <div class="box">
                                <el-button v-for="(item,index) in labels"  :key="index" link 
                                 @click="changbtn(item,index,1)"  :type="getform.Type == item.value ? 'success' : 'primary'">{{item.name}}</el-button>
                            </div>
                            <div class="pie1" id="pie1"></div>
                            <ScrollTable
                                ref="scrollTableRef" class="scroll-table" :style="`height: ${scrollheight}px;`"
                                :config="configs" @resize="handleResize">
                                <template #cell="{ row, columnIndex }">
                                    <div v-if="columnIndex == 0" class="column0">
                                        <div class="icons" :style="`background-color:${$colorlist()[row?.index]}`"></div>
                                        <span>{{ row?.ScoreType ?? '' }}</span>
                                    </div>
                                    <div v-else :class="`custom-column-${columnIndex+1}`">
                                        {{ row ? (row[Object.keys(row)[columnIndex+1]] ?? '') : '' }}
                                    </div>
                                </template>
                            </ScrollTable>
                        </div>

                        <div class="content-two-box">
                            <div class="star-title">
                                <DiamondDecoration color="#00ffff" :size="8" :middleSize="10" />
                                <p>安全隐患随手拍统计</p>
                            </div>
                            <div class="box">
                                <el-button v-for="(item,index) in labels"  :key="index" link 
                                 @click="changbtn(item,index,3)"  :type="getform.HazardType == item.value ? 'success' : 'primary'">{{item.name}}</el-button>
                            </div>
                            <div class="pie1" id="pie2"></div>
                            <ScrollTable
                                ref="scrollTableRef" class="scroll-table" :style="`height: ${scrollheight}px;`"
                                :config="configs1" @resize="handleResize"
                            >
                                <template #cell="{ row, columnIndex }">
                                    <div v-if="columnIndex == 0" class="column0">
                                        <div class="icons" :style="`background-color:${$colorlist()[row?.index]}`"></div>
                                        <span>{{ row?.ScoreType ?? '' }}</span>
                                    </div>
                                    <div v-else :class="`custom-column-${columnIndex+1}`">
                                        {{ row ? (row[Object.keys(row)[columnIndex+1]] ?? '') : '' }}
                                    </div>
                                </template>
                            </ScrollTable>
                        </div>
                    </div>
                    <div class="content-three">
                        <div class="star-title">
                            <DiamondDecoration color="#00ffff" :size="8" :middleSize="10" />
                            <p>商品兑换记录</p>
                            <el-button v-for="(item,index) in labels" :key="index" :class="`btn${index}`" link  
                            @click="changbtn(item,index,2)" :type="getform.RedeemType == item.value ? 'success' : 'primary'">{{item.name}}</el-button>
                            <div class="star-title">
                                <DiamondDecoration color="#00ffff" :size="8" :middleSize="10" />
                                <p>积分变化记录</p>
                            </div>
                        </div>
                        <div class="bar echarts" id="bar1"></div>
                        <div class="content-two-box1">
                            <ScrollTable
                                ref="scrollTableRef"
                                class="scroll-table "
                                :config="config2"
                                :style="`height:${scrollleft2}px`"
                                @resize="handleResize"
                            >
                            </ScrollTable>
                        </div>
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>

<script setup>
import DiamondDecoration from '@/views/multiple/index/delog/decoration.vue'
import { ref, watch, nextTick, onBeforeUnmount,computed, onMounted ,getCurrentInstance} from 'vue'
import { gettable, setdatca, deldata } from "@/network/api/requestnet";
import { useStore } from 'vuex'
import ScrollTable from '@/components/connet/Common/ScrollTable.vue'
const store = useStore()
const $colorlist = getCurrentInstance().appContext.config.globalProperties.$colorlist

// 添加全屏变化事件监听器
let getform=ref({
    ProjectCode:'',
    Type:0,
    RankType:1,
    HazardType:0,
    RedeemType:0
})
let forms =ref()
let color=['#ccc','#FFD700','#B87333']
let config=ref({
    data:[],
    rowNum:10,
    oddRowBGC: 'transparent',
    // rowHeight: 35,
    // waitTime: 600000,
    columnWidth: [ 40, 50, 200,60],
    evenRowBGC: 'transparent',
    list:['index','name','value1','value'],
})
let configs=ref({
    header:['获取来源','获取占比','积分数量'],
    headerBGC:'transparent',
    data: [],
    oddRowBGC: 'transparent',
    evenRowBGC: 'transparent',
    waitTime: 3000,
    rowHeight: 35,
    rowNum: 7,
    columnWidth: [ 150, 100, 100],
    hoverPause: true,
    index: false,
    list:['ScoreType','ScoreRate','ScoreCount']
})
let configs1=ref({
    header:['隐患类型','隐患占比','隐患数量'],
    headerBGC:'transparent',
    data: [],
    oddRowBGC: 'transparent',
    evenRowBGC: 'transparent',
    waitTime: 3000,
    rowHeight: 35,
    rowNum: 7,
    columnWidth: [ 150, 100, 100],
    hoverPause: true,
    index: false,
    list:['ScoreType','ScoreRate','ScoreCount']
})
let config2=ref({
    header:['#','时间','姓名','变动原因','积分'],
    data: [],
    oddRowBGC: '#003B51',
    evenRowBGC: '#0A2732',
    waitTime: 3000,
    rowHeight: 35,
    rowNum: 5,
    columnWidth: [ 50,160,80,200,80],
    hoverPause: true,
    index: false,
    list:['rowNum','InDate','WorkerName','ScoreSource','ScoreChange']
})
let goodsType=ref([])
let labels=[
    {
    name:'本周',
    value:3
    },
    {
    name:'本月',
    value:1
    },
    {
    name:'本年',
    value:2
    },{
    name:'总计',
    value:0
    },
    

]
let leftlable=[
    {
    name:'实时',
    value:0
    },{
    name:'本周',
    value:3
    },{
    name:'本月',
    value:1
    },
]
const animationStates = ref({}); // 用于存储每个数字位的动画状态
let unit=['件','个','个','条','分','件','条','条','本']
let icons=['icon-wodeshangpin','icon-banzu1','icon-a-yonghuxinxiyonghu',
'icon-shipin1','icon-viivajifen-zongjifen','icon-duihuan1','icon-shitiku','icon-suishoupai',
'icon-ziliaoku']
const counts = ref({
    ScorePerson: 0,         // 在场人员数量
    ScoreTotal: 0,      // 今日商品售出数量
});
let pageturning=[
    {
        name:'获得积分人数',
        bgcolor:'rgba(7, 166, 255, 0.3)',
        color:'#07a6ff',
        value:'ScorePerson'

    },
    {
        name:'获得总积分',
        bgcolor:'rgba(0, 255, 255, 0.3)',
        color:'#00fffc',
        value:'ScoreTotal'

    }
]
let falge=ref(0)
let list=ref([])
let chartInstance = ref(null)
let total=ref(0)
let tabledata=ref([])

let pielist=ref([])
// 图表实例管理
const chartInstances = ref({
    pie1: null,
    pies0: null,
    // pies1: null,
    // pies2: null,
    // pies3: null,
    bar1: null
});
let asideHeight=ref(850)
let asideboard=ref('50%')
let mainboard=ref('40%')
let scrollheight=ref('300')
let scrollleft=ref('450')
let scrollleft2=ref('250')
const previousCounts = ref({...counts.value}); 
const formattedCounts = computed(() => {
    const result = {};
    for (const key in counts.value) {
        result[key] = counts.value[key].toString().padStart(5, '0');
    }
    return result;
});
let myChart
let sum=ref(0)
let scrolltitle=ref('')
let times=ref(null)

watch(() => store.getters.code, async (val) => {
    // console.log('监听数据', val);
    getform.value.ProjectCode = val;
    await nextTick();
    for (const key in counts.value) {
        animationStates.value[key] = formattedCounts.value[key].split('').map(() => false);
        // console.log('数据',animationStates.value[key]);
        
    }
    getRank();
    getranking();
    getGoodsType();
    getSource()
    // getechartsdata()
    getpoints()
    getbar()
    getscrolltitle()
    getdangers()
    times.value=setInterval(()=>{
        getscrolltitle()
    },30000)
},
{ immediate: true })
const getstyle = (index) => {
    return {
        fontSize: `${index==0?70:index==1?80:50}px`
    }
}
const getcolor=(val)=>{
    const heights = [80, 120, 50]
    if(val === 0) { // 80px高度：左上角是弧形，右边没有边线
        return `height:${heights[val]}px;border:2px solid ${color[val]};
        border-right: none;
        border-radius: 10px 0 0 0;`
    } else if(val === 1) { // 120px高度：左上角和右上角是弧形
        return `height:${heights[val]}px;border:2px solid ${color[val]};
        border-radius: 10px 10px 0 0;`
    } else { // 50px高度：右上角是弧形，左边没有边线
        return `height:${heights[val]}px;border:2px solid ${color[val]};
        border-left: none;
        border-radius: 0 10px 0 0;`
    }
}
const getwidth=(val)=>{
    let widt=(val.value/sum.value)*100
    return `width:${widt}%`
}
const getNextDigit = (index, field) => {
    if (!field) {
        return previousCounts.value.toString().padStart(5, '0')[index] || '';
    }
    
    return previousCounts.value[field].toString().padStart(5, '0')[index] || '';
};
const resizeHandler = () => {
    let heights=window.innerHeight;
    // console.log('监听屏幕',heights);
    if (heights<=1000) {
        asideHeight.value=850;
        asideboard.value='55%';
        mainboard.value='40%';
        scrollheight.value='300'
        scrollleft.value='450'
        // scrollleft2.value='250'
    }else if (heights<=1100) {
        asideHeight.value=980;
        asideboard.value='61%';
        mainboard.value='44%';
        scrollheight.value='350'
        scrollleft.value='590'
        // scrollleft2.value='300'
    }else{
        asideHeight.value=850;
        asideboard.value='55%';
        mainboard.value='40%';
        scrollheight.value='300'
        scrollleft.value='450'
        // scrollleft2.value='250'
    }
    // asideHeight.value=heights-60;
};
const triggerDigitFlip = () => {
    setTimeout(() => {
        for (const key in counts.value) {
            const newFormatted = formattedCounts.value[key];
            const oldFormatted = previousCounts.value[key].toString().padStart(5, '0');
            
            newFormatted.split('').forEach((digit, index) => {
                if (index < oldFormatted.length && digit !== oldFormatted[index]) {
                    if (!animationStates.value[key]) {
                        animationStates.value[key] = [];
                    }
                    animationStates.value[key][index] = true;
                    
                    setTimeout(() => {
                        animationStates.value[key][index] = false;
                    }, 600);
                }
            });
        }
    }, 0);
};
const gettext=()=>{
    // console.log('滚动标题',scrolltitle.value.FontColor);
    let color=`color:${scrolltitle.value.FontColor};font-size:${scrolltitle.value.FontSize}px`
    return color
}
// 获取排行榜
const getRank = async() => {
    const {data:res}=await gettable('GetThisMonthSafetyStar',getform.value)
    
    if (res.code=="1000") {
        forms.value=res.data
        if (forms.value.length >= 2) {
            [forms.value[0], forms.value[1]] = [forms.value[1], forms.value[0]]
        }
    }
}
// 获取滚动标题
const getscrolltitle=async()=>{
    const {data:res}=await gettable('GetScrollingSloganByLED',getform.value)
    if(res.code=='1000'){
        scrolltitle.value=res.data
    }
}
const updateRankStyles = (index) => {
    // console.log('更新排行榜样式', index);
    // 确保index有效，如果无效则使用默认值4
    const validIndex = (index && index > 0 && index <= 3) ? index : 4;
    // 返回背景图片样式
    return `background-image:url(${require('@/assets/img/all/max' + validIndex + '.svg')})`;
}

// 获取积分排行
const getranking=async()=>{
    let form={}
    const {data:res}=await gettable('GetJFScoreByPerson',getform.value)
    // console.log('获取排名',res);
    // if(res.code=='1000'){
        previousCounts.value = {...counts.value};
        
        // 检查数据是否有变化
        let hasChanges = false;
        
        // 更新所有相关字段
        for (const key in counts.value) {
            if (res.data[key] !== undefined && counts.value[key] !== res.data[key]) {
                counts.value[key] = res.data[key];
                hasChanges = true;
            }
        }
        
        // 只有数据有变化时才触发翻页动画
        if (hasChanges) {
            // 为每个字段重置动画状态
            for (const key in counts.value) {
                animationStates.value[key] = formattedCounts.value[key].split('').map(() => false);
            }
            
            // 触发动画
            triggerDigitFlip();
        }
        sum.value=res.data.MaxScore

        config.value = {
            ...config.value,
            data: res.data?.ScoreList.map((item,index) => ({
                name: item.name,
                value: item.value,
                value1: '',
                index: index+1
            }))
        }
        
    // }else{
        
    //     previousCounts.value = counts.value;
    //     console.log('获取积分排行失败',previousCounts.value);

    // }
}
// 获取商品类型
const getGoodsType=async()=>{
    const {data:res}=await gettable('GetRealCountByProject',getform.value)
    // console.log('获取商品类型',res);
    if(res.code=='1000'){
        goodsType.value=res.data
        goodsType.value[0].Count=378
    }
}
const changbtn=(val,index,item)=>{
    // falge.value=index
    switch (item) {
        case 0:
        getform.value.RankType=val.value
        getranking()
            break;
        case 1:
        getform.value.Type=val.value
        getSource()
            break;
        case 2:
        getform.value.RedeemType=val.value
        getbar()
            break;
        case 3:
        getform.value.HazardType=val.value
        getdangers()
            break;
            
    }
    
}
// 积分变化记录
const getpoints=async()=>{
    const {data:res}=await gettable('GetScoreRecordTableByProject',getform.value)
    tabledata.value=res.data
    // console.log('积分变化记录',res);
    
    if(res.code=='1000'){
        // 数据验证和清理
        tabledata.value=res.data
        config2.value = {
            ...config2.value,
            data: tabledata.value,
        }
    }
}
// 获取积分来源
const getSource=async()=>{
    const {data:res}=await gettable('GetJFScoreByDate',getform.value)
    // if(res.code=='1000'){
        // 数据验证和清理
        const cleanedData = res.data.EchartTable?.map((item, index) => ({
            index: index ,
            ...item,
            ScoreType: item.ScoreType || '',
        }))
        
        list.value = res.data
        configs.value = {
            ...configs.value,
            data: cleanedData,
        }
        nextTick(()=>{
            domecharts(res.data.EchartList,'pie1')
        })
    // }
}
// 获取安全隐患统计分析
const getdangers=async()=>{
    const {data:res}=await gettable('GetHazardTypeStatistics',getform.value)
    // if(res.code=='1000'){
        // 数据验证和清理
        const cleanedData = res.data.EchartTable?.map((item, index) => ({
            index: index ,
            ...item,
            ScoreType: item.ScoreType || '',
        }))
        
        // list.value = res.data
        configs1.value = {
            ...configs1.value,
            data: cleanedData,
        }
        nextTick(()=>{
            domecharts(res.data.EchartList,'pie2')
        })
    // }
}

const initChart = (containerId, retryCount = 0) => {
    const container = document.getElementById(containerId);
    if (!container) {
        // console.warn(`Chart container ${containerId} not found, retry count: ${retryCount}`);
        if (retryCount < 3) {
            setTimeout(() => initChart(containerId, retryCount + 1), 200);
            return null;
        }
        return null;
    }
    
    const { width, height } = container.getBoundingClientRect();
    if (width === 0 || height === 0) {
        // console.warn(`Chart container ${containerId} has zero size: ${width}x${height}, retry count: ${retryCount}`);
        if (retryCount < 3) {
            setTimeout(() => initChart(containerId, retryCount + 1), 200);
            return null;
        }
        return null;
    }

    try {
        let echarts = require('echarts');
        let instance = echarts.getInstanceByDom(container);
        if (instance == null) {
            instance = echarts.init(container, null, {
                renderer: 'canvas',
                useDirtyRect: true
            });
            // console.log(`Successfully initialized chart ${containerId}`);
        }
        
        chartInstances.value[containerId] = instance;
        return instance;
    } catch (error) {
        // console.error(`Error initializing chart ${containerId}:`, error);
        if (retryCount < 3) {
            setTimeout(() => initChart(containerId, retryCount + 1), 200);
            return null;
        }
        return null;
    }
}

// 更新图表数据
const updateChart = (containerId, option) => {
    const instance = chartInstances.value[containerId];
    if (!instance) return;
    
    instance.setOption(option);
}

// 更新饼图数据
const domecharts = (data,id) => {
    if (!chartInstances.value[id]) return;
    
    let option = {
        tooltip: {
            trigger: 'item'
        },
        series: [
            {
                name: '数据来源',
                type: 'pie',
                radius: '70%',
                data: data ?? [],
                color: $colorlist(),
                label: {
                    show: true,
                    position: 'inside',
                    formatter: '{c}%',
                    color: '#fff',
                    fontSize: 12,
                    fontWeight: 'bold'
                }
            }
        ]
    }
    
    updateChart(id, option);
}

// 更新柱状图数据
const updateBarChart = (data) => {
    if (!chartInstances.value.bar1) return;
    // console.log('柱状图显示',chartInstances.value.bar1);
    
    let option = {
            tooltip: {
                trigger: 'item',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function(params) {
                    return `<span style="display:inline-block;margin-right:5px;
                    border-radius:10px;width:10px;height:10px;background-color:${params.color};\"></span>${params.name}：${params.value}`
                }
            },
            "grid": {
                "top": "5%",
                "left": "2%",
                "bottom": "1%",
                "right": "2%",
                "containLabel": true
            },
            "xAxis": [{
                "type": "category",
                "data": data?.map(item=>item.name),
                "nameTextStyle": {
                    "color": "#fff"
                },
                "axisLine": {
                    "lineStyle": {
                        "color": "#82b0ec"
                    }
                },
                axisLabel: {
                    interval: 0,
                    // rotate : 270,
                    formatter:function(val){
                        return val.slice(0, 2)
                    },
                    "textStyle": {
                        "color": "#fff"
                    }
                }
            }],
            "yAxis": [{
                "type": "value",
                "axisLabel": {
                    "textStyle": {
                        "color": "#fff"
                    },
                    "formatter": "{value}"
                },
                "splitLine": {
                    "lineStyle": {
                        "color": "#0c2c5a"
                    }
                },
                "axisLine": {
                    "show": false
                }
            }],
            "series": [{
                    "name": "",
                    type: 'pictorialBar',
                    symbolSize: [20, 10],
                    symbolOffset: [0, -5],
                    symbolPosition: 'end',
                    z: 12,
                    label: {
                        normal: {
                            show: true,
                            position: "inside",
                            align: 'center',
                            formatter: "{c}"
                        }
                    },
                    data: data?.map(item => ({
                        ...item,
                        itemStyle: {
                            color: item.itemStyle?.color || '#07a6ff'
                        }
                    }))
                },
                {
                    type: 'bar',
                    itemStyle: {
                        normal: {
                            opacity: .7
                        }
                    },
                    barWidth: "20",
                    data: data?.map(item => ({
                        ...item,
                        itemStyle: {
                            color: item.itemStyle?.color || '#07a6ff'
                        }
                    }))
                }
            ]
        }
    
    updateChart('bar1', option);
}

// 更新第二个饼图数据
const updatePieChart = (data, index) => {
    // console.log('几个饼状图',index);
    
    const chartId = `pies${index}`;
    if (!chartInstances.value[chartId]) {
        // console.warn(`Chart instance ${chartId} not found, initializing...`);
        initChart(chartId);
    }
    
    let option = {
        series: [
            {
                type: 'gauge',
                startAngle: 90,
                endAngle: -270,
                pointer: {
                    show: false
                },
                progress: {
                    show: true,
                    overlap: false,
                    roundCap: true,
                    clip: false,
                    itemStyle: {
                        color: '#1AC9FF',
                    }
                },
                axisLine: {
                    lineStyle: {
                        width: 8,
                        color:[[1,'#0A5681']]
                    }
                },
                splitLine: {
                    show: false,
                    distance: 0,
                    length: 10
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    show: false,
                    distance: 0
                },
                data: data,
                title: {
                    show: false,
                    fontSize: 20
                },
                detail: {
                    show: true,
                    width: 50,
                    height: 14,
                    fontSize: 20,
                    color: '#fff',
                    offsetCenter: [0, 0],
                    formatter: function(value) {
                        return value+'%';
                    }
                }
            }
        ]
    };
    
    updateChart(chartId, option);
}

// 获取柱状图数据
const getbar = async() => {
    const {data:res} = await gettable('GetRedeemGoodsRecordEcharts', getform.value);
    // if (res.code === '1000') {
        pielist.value = res.data.RedeemEchartsList;
        
        // 确保颜色列表存在
        const colors = $colorlist();

        let datas = res.data.RedeemTreeList.map((item, index) => ({
            name: item.name || '',
            value: item.value || 0,
            itemStyle: {
                color: colors[index % colors.length]
            }
        }));
        
        updateBarChart(datas);
        
        // 等待 DOM 更新后再初始化饼图
        await nextTick();
        
        // 更新每个饼图
        pielist.value.forEach((item, index) => {
            const chartId = `pies${index}`;
            const container = document.getElementById(chartId);
            
            if (container) {
                // 确保容器可见
                container.style.display = 'block';
                container.style.visibility = 'visible';
                
                // 初始化图表
                const instance = initChart(chartId);
                if (instance) {
                    const chartData = [{
                        ...item.RedeemEcharts[0],
                        title: {
                            offsetCenter: ['50%', '50%']
                        }
                    }];
                    updatePieChart(chartData, index);
                } else {
                    // console.warn(`Failed to initialize chart ${chartId}`);
                }
            } else {
                // console.warn(`Container ${chartId} not found`);
            }
        });
    }
// }

// 组件挂载时初始化所有图表
onMounted(async () => {
    // 等待 DOM 更新
    await nextTick();
    
    // 确保容器存在且有尺寸
    const ensureContainerSize = (containerId) => {
        const container = document.getElementById(containerId);
        if (container) {
            const style = window.getComputedStyle(container);
            if (style.display === 'none' || style.visibility === 'hidden') {
                container.style.display = 'block';
                container.style.visibility = 'visible';
            }
        }
    };

    // 只初始化固定的图表
    ['pie1', 'bar1','pie2'].forEach(id => {
        ensureContainerSize(id);
        const instance = initChart(id);
        if (!instance) {
            // console.warn(`Failed to initialize chart ${id}, will retry automatically`);
        }
    });

    resizeHandler();
    window.addEventListener("resize", resizeHandler);
});

// 组件卸载前清理所有图表
onBeforeUnmount(() => {
    // 移除 resize 事件监听
    window.removeEventListener("resize", resizeHandler);
    clearInterval(times.value)
    Object.entries(chartInstances.value).forEach(([id, instance]) => {
        if (instance && instance.dispose) {
            try {
                instance.dispose();
            } catch (error) {
                // console.error(`Error disposing chart ${id}:`, error);
            }
        }
    });
    chartInstances.value = {
        pie1: null,
        pies0: null,
        pies1: null,
        pies2: null,
        bar1: null
    };
    if(myChart){
        myChart.dispose();

    }
});

const handleResize = ({ width, height }) => {
    // 处理大小变化
    // console.log('Table size changed:', width, height)
}

</script>
<style lang="scss">
.scroll-ranking .rows .row-item{
    // align-items: center;
    // position: relative;
    .ceil{
        padding: 0!important;
    // position: relative;

    }
}
</style>
<style lang="scss" scoped>
.is-vertical{
    height: 100%!important;
}
.el-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding-top: 22px;
    // height: 100px;
    .title {
        position: absolute;
        top: 10px;
        color: #fff;
        font-size: 24px;
        margin: 0;
    }
}
.abs{
    position: absolute;
    top: 5px;
}
.abs1{
    left: 0;
}
.abs2{
    right: 0;
}
.padding {
    // padding: 20px;
    padding: 5px 20px!important;

}

.ranking0{
    height: 30px;
    background-size: 100% 100%;
    position: relative;

    top: 20%;
    // margin-top: 10%;
    font-size: 18px;
    color: #fff;
    font-weight: bold;
    line-height: 30px;
}
.ranking2{
    height: 10px;
    position: relative;
    top:45%;
    border-radius: 140px;
    background-color: rgba(0, 204, 255, 0.1);
    &-info{
        height: 8px!important;
        border-radius: 140px;
        background-color: #00ccff!important;
        box-sizing: border-box;
    }
}
.month {
    // margin-top: 10px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    // gap: 20px;
    &-item{
        width: 100%;
        text-align: center;
        transition: transform 0.3s ease;
        
        &:hover {
            transform: translateY(-5px);
        }
        
        h4 {
            margin: 5px 0;
            font-size: 14px;
            font-weight: bold;
        }
    }
}
.content-one{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    // margin-top: 2%;
    &-item{
        background: rgba(39,92,138, 0.2);
        border-radius: 10px;
        margin: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    .texts{
        // text-align: end;
        margin-top: 5px;
        text-shadow: 
            0 0 10px var(--shadow-color),
            0 0 20px var(--shadow-color),
            0 0 30px var(--shadow-color),
            0 0 40px var(--shadow-color);
    }
    &-item1{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        h2{
            margin-right: 10px;
        }
    }
    .fonts{
        font-size: 60px!important;
    }
    .text-p{
        font-weight: bold;
        font-size: 12px;
        margin-top: 10px;
    }
}
.center-section-top{
    height: 50%;
}
.ranking{
        margin-top: 17px!important;
    }
.content-two{
    display: grid;
    grid-template-columns: repeat(2,48%);
    grid-template-rows: 100%;
    align-items: start;
    gap: 20px;
    height: 40%;
    &-box{
        display: grid;
        height: 100%;
        width: 100%;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 30px auto;
        align-items: flex-end;
    }
    // &-box1{
    //     .star-title{
    //         margin-bottom: 10px!important;
    //     }
    // }
    
    .box{
        margin-bottom: 5px;
        margin-right: 10%;
        text-align: end;
    }
    .pie1{
        width: 100%;
        height: 100%;
        min-height: 200px;
    }
    .column0{
        display: flex;
        justify-content: start;
    }
    // .hydrograph{
    //     width: 100%;
    //     height: 100%;
    //     grid-column: 1/span 2;
    //     grid-row: 2/span 3;
    // }
    .Cumulative{
        grid-row: 2;
        grid-column: 1/span 2;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
    }
    .hyd{
        grid-column: 1/span 2;
        grid-row: 1;
        justify-content: center;
    }
    .count{
        margin: 0 5px;
        color:#04D7EC;
    }
}
.content-three{
    display: grid;
    grid-template-columns: 1fr 1fr;
    height: 33%;
    grid-template-rows: 30px 1fr;
    .star-title{
    grid-column: 1/span 2;
    // margin-top: 10px;
    }
    .echarts{
        width: 100%;
        height: 100%;
    }
    .pie{
        display: grid;
        grid-template-columns: repeat(3,33.3%);
    }
    .echarts1{
    width: 100%;
    height: 90%;
    margin-top: 10px;
    }
    .btn0{
        margin-left: 25%;
    }
    .btn3{
        margin-right: 5%;
    }
}
.content-two-box1{
    height: 90%;
}
.icon{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-top: 10px;
    transition: all 0.3s ease;
    
    &:hover {
        transform: scale(1.05);
    }
}
.el-aside{
    margin-left: 10px;
    overflow-y: auto;
    transition: all 0.3s ease;
    height: v-bind(asideHeight);
}
.scroll-table{
height: 300px;
}
.btns0{
    margin-left: auto;
}
.star-title {
    display: flex;
    align-items: center;
    min-height: 30px;
    // margin-top: 10px;
    
    p {
        color: #fff;
        font-size: 16px;
        margin: 0;
    }
}

.scroll-text-container {
    width: 100%;
    height: 28px;
    overflow: hidden;
    white-space: nowrap;
}

.scroll-text {
    display: inline-block;
    color: #fff;
    font-size: 18px;
    animation: scroll-left 20s linear infinite;
    padding-right: 50px;
}

@keyframes scroll-left {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}
.icons{
    width: 10px;
    height: 10px;
    border-radius: 100%;
}
.column0{
    display: flex;
    align-items: center;
    justify-content: start;
}
.bottoms{
    font-size: 14px;
}
.shop-content{
        margin: 26px 0;
        width: 100%;
        display: grid;
        grid-template-columns: 1fr 1fr;
        justify-content: space-between;
        .title{
            height: 20px;
        }
        .counter-container {
            width: 100%;
            display: flex;
            align-items: center;
            margin-top: 10px;
            // grid-column: 1 / -1;
            justify-content: center;
            gap: 4px;
        }
        .digit-box {
            width: 25px;
            height: 40px;
            border-radius: 5px;
            border: 1px solid rgba(7, 166, 255, 0.5);
            background-color: rgba(7, 166, 255, 0.3);
            box-sizing: border-box;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: "DIN", sans-serif;
            font-size: 20px;
            font-weight: bold;
            box-shadow: inset 0 -5px 10px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            perspective: 300px;
        }
        
        .digit-flip-container {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
            transition: all 0.5s ease;
        }
        
        .digit-current, .digit-next {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            // text-shadow: 0 0 8px rgba(0, 204, 255, 0.7);
        }
        
        .digit-next {
            transform: rotateX(180deg);
        }
        
        .flip-animate {
            transform: rotateX(180deg);
        }
        .separator {
            // color: white;
            font-size: 32px;
            margin: 0 2px;
            font-weight: bold;
        }
        .indicator {
            color: #00ccff;
            font-size: 16px;
            margin-left: 5px;
            align-self: flex-end;
            margin-bottom: 15px;
        }
        .shop-digit{
            width: 50px;
            height: 60px;
            padding: 2px 2px 2px 2px;
            border-radius: 5px;
            border: 1px solid rgba(7, 166, 255, 0.5);
            background-color: rgba(7, 166, 255, 0.3);
            box-sizing: border-box;
            font-family: "DIN", sans-serif;
            font-weight: 700;
            color: #ffffff;
            text-align: center;
            line-height: normal;
        }
    }
</style>

