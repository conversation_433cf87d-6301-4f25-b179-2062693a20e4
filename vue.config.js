const { defineConfig } = require('@vue/cli-service')
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false,
  productionSourceMap: false,
  publicPath: './',
  outputDir: 'smakt',
	pwa: {
		iconPaths: {
			favicon32: 'favicon.ico',
			favicon16: 'favicon.ico',
			appleTouchIcon: 'favicon.ico',
			maskIcon: 'favicon.ico',
			msTileImage: 'favicon.ico'
		}
	},
	configureWebpack: {
		plugins: [new NodePolyfillPlugin()]
	},
	
	chainWebpack: config => {
		config
			.plugin('html')
			.tap(args => {
				args[0].title = '掌勤智慧工地平台'
				return args
			})
		// config.entry('main').add('babel-polyfill')
	},
  devServer: {
		proxy: { //配置跨域
			'/aiot': {
				target: 'https://api.zqface.com', //本地调试
				ws: true,
				changOrigin: true, 
				// secure: true,
				pathRewrite: {
					'^/aiot': '' 
				}
			},
			'/ai': {
				target: 'https://ai.zqface.com', //本地调试
				ws: true,
				changOrigin: true, 
				// secure: true,
				pathRewrite: {
					'^/ai': '' 
				}
			}

		},
		client: {
			overlay: false,
		  },
	},
})
