export function labelist(params) {
    // 表单
    let lablist=[]
    let formcout=[
        {
         name:'质量巡检',
         value:[{
            name:'序号',
            value:'rowNum',
            widths:'',
            index:0
            },{
            name:'检查日期',
            value:'CheckDate',
            widths:'',
            index:6
            },{
            name:'检查人',
            value:'CheckName',
            widths:'',
            index:7
            },{
            name:'地点',
            value:'QualityMatterPlace',
            widths:'',
            index:3
            },{
            name:'问题类型',
            value:'QualityPatType',
            widths:'',
            index:4
            },{
            name:'问题描述',
            value:'MatterDescribe',
            widths:'',
            index:5
            },{
            name:'问题状态',
            value:'ReformStatic',
            widths:'',
            },{
            name:'整改时间',
            value:'OperateDate',
            widths:''
            },{
            name:'整改人',
            value:'Operator',
            widths:''
            }
         ]
        },
        {
        name:'质量巡检详情',
        value:[{
            name:'质检信息',
            type:0,
            value:''
            },{
            name:'质检员：',
            value:'CheckName'
            },{
            name:'质检时间：',
            value:'CheckDate'
            },{
            name:'所属项目：',
            value:'ProjectName'
            },{
            name:'问题类别：',
            value:'QualityPatType',
            value1:'SafeCheckType'
            },{
            name:'问题地点：',
            value:'QualityMatterPlace',
            value1:'SafeCheckPlace'
            },{
            name:'紧急程度：',
            value:'DegreeUrgency'
            },{
            name:'问题描述：',
            value:'MatterDescribe'
            },{
            name:'问题照片：',
            value1:'CheckPhoto',
            type:1
            },{
            name:'整改情况',
            type:0,
            value:''
            },{
            name:'整改人：',
            value:'Operator'
            },{
            name:'整改时间：',
            value:'OperateDate'
            },{
            name:'整改描述：',
            value:'OperateDescribe'
            },{
            name:'整改照片：',
            value1:'OperatePhoto',
            type:1
            },{
            name:'复查结果',
            type:0,
            value:''
            },{
            name:'检查人：',
            value:'Operator2'
            },{
            name:'复查时间：',
            value:'OperateDate2'
            },{
            name:'是否合格：',
            value:'IsQualified'
            },
            ]
           },
    ]


    formcout.forEach((item,index)=>{
        if (item.name==params) {
            lablist=item.value
            
        }
    })
    // console.log('获取',lablist);
    
    return lablist
}