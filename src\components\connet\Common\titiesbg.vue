<template>
<div class="headerbg" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
      rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
    <div class="headerbg-one">
        <img src="@/assets/img/home/<USER>" alt="">
        <p>{{titles}}</p>
        <el-button type="success" link @click="btns()">{{forms?.titles}}</el-button>
    </div>
</div>
</template>

<script>
import { ref,onMounted } from 'vue'
export default {
props:['titles','forms'],
setup(porps,emits){
    // let titles=ref('')
    let bgcolor=ref({})
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    })
    const btns=()=>{
        emits.emit('entry',true)
    }
    return{
        // titles,
        bgcolor,
        btns

    }
}
}
</script>
<style lang="scss" scoped>
.headerbg-one{
    display: flex;
    align-items: center;
    padding: 10px;
    p{
        font-size: 20px;
        font-weight: bold;
    }
}
.el-button{
    margin-left: auto;
}
</style>