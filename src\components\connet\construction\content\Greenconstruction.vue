<template>
  <div class="Greenconstruction">
    <div v-for="(item,index) in menu" :key="index" class="bgimg cursor" :style="getbgimg(item)" @click="opens(item)">
        <svg class="iconfont svg"> 
            <use :xlink:href="`#${item.icon}`"></use> 
        </svg> 
        <span>{{item.name}}</span>
    </div>
    <environment ref="environment" ></environment>
  </div>
</template>

<script>
import environment from "@/components/connet/construction/content/greendelog/environment.vue";
import { ref } from 'vue';
export default {
components:{
    environment
},
setup(){
    let environment=ref(null)
    let menu=[{
        name:'相关文件',
        url:require('@/assets/img/construction/bgimg/001.svg'),
        icon:'icon-huanbaojilu',
        list:['施工许可证', '绿色施工关键指标','绿色施工宣传资料',
                '包含绿色施工关键指标要求的劳务、分包合同']
    },{
        name:'环境保护',
        url:require('@/assets/img/construction/bgimg/002.svg'),
        icon:'icon-shengtaihuanbao',
        list:['扬尘监控测量记录表','PM2.5、PM10监控测量记录表',
                '噪声监控测量记录表','污水监控测量记录表',
                '第三方水质检测报告','现场扬尘控制洒水记录表','隔油池清理记录表','化粪池清理记录表',
                '施工现场移动厕所清理记录表','可回收建筑垃圾管理记录表','生活垃圾外运记录表','有毒有害垃圾管理记录表','回填检测报告','环境保护管理制度']
    },{
        name:'节材与材料资源利用',
        url:require('@/assets/img/construction/bgimg/003.svg'),
        icon:'icon-huanbaobaozhuang',
        list:['主要材料采购记录表','材料、机具进出场台账','材料进场计划',
                '主要建筑材料损耗率','非实体工程材料重复利用','节材与材料资源利用管理制度']
    },{
        name:'节水与水资源利用',
        url:require('@/assets/img/construction/bgimg/004.svg'),
        icon:'icon-jieyueyongshui',
        list:['节水指标纳入合同','项目用水记录及工程用水总量汇总表','节水设备使用统计表',
                '基坑降水收集记录表','中水回用记录表','雨水回用记录表','节水与水资源利用管理制度']
    },{
        name:'节能与能源利用',
        url:require('@/assets/img/construction/bgimg/005.svg'),
        icon:'icon-jieyuenengyuan',
        list:['节能指标纳入合同','施工用电记录表及工程用电总量汇总表','设备总体耗能计划',
                '节能设备及太阳能、风能、空气能设备配备情况登记表',
                '大型机械保养记录表','石化气燃料使用台账表','节能与能源利用管理制度']
    },{
        name:'节地与土地资源利用',
        url:require('@/assets/img/construction/bgimg/006.svg'),
        icon:'icon-juzhushengtai',
        list:['三阶段场布规划图','临时用地借地协议',
                '活动板房建立验收资料','活动板房进场合格证、防火材料证明',
                '现场临时用房、硬化、植草砖铺装等各临建建设面积台账','保护用地措施','节地与土地资源利用管理制度']
    },{
        name:'人力资源节约与职业健康',
        url:require('@/assets/img/construction/bgimg/007.svg'),
        icon:'icon-shengtaixitong',
        list:['施工现场人员实名制登记表','食堂从业人员健康证明登记表',
                '特种作业人员登记表','严重污染天气记录表',
                '职业病防治体检登记表','施工现场卫生保洁责任表','生活、办公垃圾外运记录台账','洒水记录表',
                '餐具消毒记录表','施工现场消毒记录表','劳动力计划表、劳动力使用台账','培训计划','培训台账']
    },
    ]

    const getbgimg=(url)=>{

        return `background-image:url(${url.url})`
    }
    const opens=(val)=>{
        environment.value.showdelog(val)
    }

    return{
        menu,
        // titllist,
        environment,

        getbgimg,
        opens

    }
}
}
</script>
<style lang="scss" scoped>
.Greenconstruction{
    color: #fff;
    display: grid;
    grid-template-columns: repeat(3,33.3%);
    justify-items: center;
}
.iconfont{
    margin: 0 20px;
}
.bgimg{
    height: 120px;
    // line-height: 120px;
    width: 60%;
    background-size: 100%!important;
    margin: 10px;
    background: #2A84DD;
    display: flex;
    align-items: center;
    // justify-content: start;
    span{
        font-size: 16px;
        font-weight: bold;
    }
}
.svg{
    width: 40px;
    height: 40px;
}
// .iconfont {
//     color: var(--icon-color) !important; /* 确保图标颜色 */
//   }
</style>