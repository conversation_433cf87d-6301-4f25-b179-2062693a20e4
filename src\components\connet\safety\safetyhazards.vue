<template>
  <!-- 安全隐患 -->
  <div class="teamslist padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"></Chamfering>

    <div class="teamslist-two">
      <div class="teamslist-two-top">
        <swiper class="topline"  :slides-per-view="3"  :direction="'vertical'" @swiper="onSwiper"
         :autoplay="{ delay: 2000, disableOnInteraction: false }" :modules="modules" >
            <swiper-slide v-for="(row, index) in groups()" :key="index">
              <div class="row-content">
                <div v-for="(item,i) in row" :key="i" class="teamslist-two-label cursor"  @click="changtype(item,index)">
                  <div class="teamslist-two-label-one" >
                    <div class="label-center-before line" :style="`background:${item.itemStyle.color}`"></div>
                    <div class="label-center" :style="`border-color:${item.itemStyle.color}`"></div>
                    <div class="label-center-after line" :style="`background:${item.itemStyle.color}`"></div>
                  </div>
                  <div>{{item.name}}</div>
                </div>
              </div>
            </swiper-slide>
        </swiper>
      </div>
      <div class="echatr">
        <span class="digit">{{teamcout}}</span>
        <workecharts  :ids="'hiddechart'" :options="addteions" @opendelog="changtype"></workecharts>
      </div>
      <delog ref="delogtbale"></delog>
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    
  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import workecharts from "@/components/connet/personnelcon/echarts3d/workerkq.vue";
import { Swiper, SwiperSlide } from 'swiper/vue';
import Chamfering from "@/components/connet/Common/Chamfering.vue";

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import delog from "@/components/connet/safety/content/delog.vue";
export default {
props:['homeindex','teamtype'],
components:{
// echartsAttendance
workecharts,
Swiper,
SwiperSlide,
delog,
Chamfering
},
setup(props){

  let bgcolor=ref({})
  let falge=ref(0)
  let echart=ref(true)
  let countlist=ref([])
  let getform=ref({
      ProjectCode:store.getters.code,

    })
  let delogtbale=ref(null)
  let teamcout=ref(0)
  let WorkTypecolor=[
					"#407fff", "#a682e6", "#e15d68", "#f29961", "#00cccd",
          "#dedede", "#FE8463", "#9BCA63", '#23A9F2', "#A6E22E",
					'#D7504B', '#C6E579', '#F4E001', '#F0805A', '#26C0C0',
					'#FFB7DD', '#660077', '#FFCCCC', '#FFC8B4', '#550088',
					'#FFFFBB', '#FFAA33', '#99FFFF', '#CC00CC', '#FF77FF',
					'#C63300', '#9955FF', '#66FF66', '#129393', '#395203',
					'#C1232B', '#B5C334', '#FCCE10', '#E87C25', '#27727B',
					'#FAD860', '#F3A43B', '#60C0DD', '#0D7CAA', '#31FAFC'
				]
  let gridData=ref([
  ])
  // let btnlist=['全部人员','管理人员','建筑工人']
  let addteions=ref([])
  let Attendslist=ref([])
  let topforms=ref({
      url:require('@/assets/img/safety/hiddendangers.png'),
      name:"安全隐患类型分析"
    })
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      // if (props.teamtype.) {
        
      // }
	    getsunm()
  })
  const getsunm=async()=>{
    // const {data:res}=await gettable('SafePatTypeShow',getform.value)
    let WorkerTeam=ref([])
    const {data:res}=await gettable('SafePatTypeShow',getform.value)
      // console.log('质量问题类型分析',res);
      WorkerTeam.value=res.data
      if (res.code=="1000") {
        addteions.value = WorkerTeam.value.map((item,index)=>{
        teamcout.value =res.Total
        return{
          name:item.SafeCheckType,
          value:parseInt(item.count),
          value1:0,
          itemStyle:{
            color:WorkTypecolor[index]
          }
        }
      })
      }
    
  }
  const isLongText = (text) => {
      const containerWidthPx = 340;
      const averageCharWidth = 12; // 假设每个字符平均16px宽
      const textWidth = text.length * averageCharWidth;
      return textWidth > containerWidthPx; // 如果文本宽度超过容器40%则视为长文本
    };
  // 346
  const groups=(val)=>{
    // console.log('截取数据',addteions.value);
      const result = [];
      let currentRow = [];
      let currentRowWidth = 0;
      const maxRowWidth = 340 - 60; // 考虑padding和间距
      // slids.value=false
      addteions.value.forEach(item => {
        const textWidth = item.name.length * 12; // 估算文本宽度
        const itemMargin = 20; // 项目间距
        const itemTotalWidth = textWidth + itemMargin;

        if (currentRowWidth + itemTotalWidth > maxRowWidth) {
          if (currentRow.length > 0) {
            result.push([...currentRow]);
            currentRow = [];
            currentRowWidth = 0;
          }
          currentRow.push(item);
          currentRowWidth = itemTotalWidth;
        } else {
          currentRow.push(item);
          currentRowWidth += itemTotalWidth;
        }
      });
      
      if (currentRow.length > 0) {
        result.push(currentRow);
      }
      // slids.value=true

      // swiper.value.autoplay.start();
      return result;
  }
  const onSwiper = (swiper) => {
    // console.log('自动启动滚动',swiper);
      swiper.autoplay.start();
    };
  const changtype=(val,index)=>{
    console.log('点击',val,index);
    // falgess.value=true
    nextTick(()=>{
    //   switch (titles.value) {
    //   // case '材料种类验收分析':
    //   //   tablelist.value.showdelog('材料验收记录',val)
    //   //   break;
    //   // case '供货商验收统计':
    //   //   tablelist.value.showdelog('供货商验收记录',val)
    //   //   break;
    //   case '质量问题类型分析':
        delogtbale.value.showdelog('安全隐患记录',1,'',val.name)
    //     break;
    // }
    })
    
    // tablelist.value.showdelog('材料验收记录',val)
  }
   const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }

	return{
    teamcout,
		falge,
    echart,
		countlist,
		getform,
		bgcolor,
    topforms,
    // btnlist,
    addteions,
    gridData,
    delogtbale,
    modules: [Autoplay, Pagination, Navigation],
    // swipersss,
    // slids,
    isLongText,
    onSwiper,
    groups,
  tableRowClassName,
  changtype,
	}
}
}
</script>
<style lang="scss">
.workerpopove{
.el-table{
    --el-table-tr-bg-color:transparent!important;
    // border:1px solid transparent!important;
    // --el-table-border-color:transparent;
    --el-table-row-hover-bg-color:transparent;
    --el-table-bg-color:transparent;
}
.el-table .warning-row {
//   --el-table-tr-bg-color: #000 !important;
  background: rgba(15, 43, 63, 0.6)!important;
}
.topline{
  height: 100%!important;
  .swiper-slide{
    height: 20px!important;
  }
  .swiper-wrapper {
    transition-timing-function: linear !important; /* 没错就是这个属性 */
}
}
}
</style>
<style lang="scss" scoped>
.teamslist{
   .rightshow{
    display: flex;
    align-items: center;
  }
  &-top{
    justify-content: space-between;

  }
.row-content {
  display: flex;
  flex-wrap: wrap;
  max-width: 340px;
  justify-content: flex-start;
  align-items: stretch;
}
&-two{
  height: 80%;
  .lables{
    display: flex;
    flex-wrap: wrap;
  }
 
  &-label{
    display: flex;
    align-items: center;
    padding: 5px;
    .label-center{
      width: 10px;
      height: 10px;
      border-radius: 100%;
      border: 1px solid #000;
    }
    .line{
        width: 5px;
        height: 2px;
        background: #000;
      }
  &-one{
    display: flex;
    align-items: center;
    // position: relative;
  }
  }
  &-top{
    height: 27%;
    font-size: 12px;
  }
  &-btn{
    padding: 10px;
    width: 80%;
    position: relative;
    border: 2px solid #03558F;
    // margin: 10px;
  }
  .bgcolorcz{
        background: #F29961;

    }
    .bgcolorsj{
          background: #4582ff;

    }
}
}
.echatr{
  // display: flex;
  // align-items: center;
  position: relative;
  width: 100%;
  height: 85%;
  .digit{
    position: absolute;
    left: 39%;
    top: 24%;
    z-index: 1;
    font-size: 37px;
    font-weight: 900;
  }
}

</style>