<template>
  <div class="Highsupportmolds">
    <div class="Highsupportmolds-two">
      <titiesbg :titles="'监测概况'"></titiesbg>
      <div class="prows cursor" v-for="(item,index) in btns" :key="index" @click="open(formlabe[item.value])">
        <img class="picimg" v-if="formlabe[item.value]" :src="require(`@/assets/img/construction/file/${getfile(formlabe[item.value])}.png`)" alt="">
        <span>{{item.name}}</span>
      </div>
      <titiesbg :titles="'报警统计'"></titiesbg>
      <div class="rightbom">
          <div id="Alarmstatistics"></div>
          <el-scrollbar ref="myScrollbar" style="height:160px">
              <div v-for="(item,index) in woring" :key="index" class="labelechart">
                  <div class="icons" :style="`background:${WorkTypecolor[index]}`"></div>
                  <span>{{item.name}}</span>
                  <span>{{item.value2}}</span>
                  <span>{{item.value}}</span>
              </div>
          </el-scrollbar>
        </div>
        <titiesbg :titles="'监测项目'"></titiesbg>
        <div class="monitors">
            <div id="Monitorings" class="Monitorings"></div>
            <el-scrollbar ref="myScrollbar" class="mortir" style="height:120px">
                <div v-for="(item,index) in bjcounts" :key="index" class="labelechart">
                    <div class="icons" :style="`background:${WorkTypecolor[index]}`"></div>
                    <span>{{item.name}}</span>
                    <span></span>
                    <span>{{item.value}}</span>
                </div>
            </el-scrollbar>
        </div>
        <div class="tablebotm">
            <p>累计变化最大值</p>
            <el-table :data="counts.JKData1"
             border style="width: 100%" class="bs-table toptable"  max-height="100px" :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
             :style="['width: 100%;height:100%;',`color:${bgcolor.font};--el-table-border-color:${bgcolor.titlecolor}`]"
             :row-class-name="tableRowClassName">
                <el-table-column prop="value1" label="垂直位移" align="center" width="90px"></el-table-column>
                <el-table-column prop="value2" label="水平位移" align="center" ></el-table-column>
                <el-table-column prop="value3" label="立杆轴力" align="center"></el-table-column>
                <el-table-column prop="value4" label="立杆倾角" align="center" ></el-table-column>
                <el-table-column prop="value5" label="水平倾角" align="center" ></el-table-column>
            </el-table>
        </div>
    </div>
    <img :src="url" alt="" style="width:100%;height:600px">
  </div>
</template>

<script>
import titiesbg from "@/components/connet/Common/titiesbg.vue";
import store from "@/store";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import picimg from "@/components/connet/Common/picimg.vue";
import { onMounted, ref } from 'vue';

export default {
components:{
    titiesbg
},
setup(){
    let formlabe=ref({})
    let bgcolor=ref({})
    let getform=ref({
          ProjectCode:store.getters.code,
          InUserName:store.getters.username,
          Type:2,
          MonitorID:''
      })
    let btns=ref([{
                name:'高支模监测方案',
                value:'GzmFile'
            },{
                name:'高支模设计图纸',
                value:'GzmSJFile'
            },{
                name:'盘口检测报告',
                value:'GzmPKFile'
            },{
                name:'横杆检测报告',
                value:'GzmHGFile'
            },{
                name:'立杆检测报告',
                value:'GzmLGFile'
            }])
    // let a=ref
    let woring=ref([])
    let yjcount=ref([])
    let WorkTypecolor=["#407fff",'#1F9DF5','#21F5D6','#5c2223','#eea2a4',
			"#a682e6",'#b598a1','#c08eaf','#813c85','#806d9e',
			"#e15d68",'#5e616d','#3170a7','#8fb2c9','#c3d7df',
			"#f29961",'#12a182','#737c7b','#92b3a5','#1a6840',
			"#00cccd",'#bec936','#373834','#5bae23','#e4bf11',
			"#dedede",'#b78d12','#f0d695','#b4a992','#fa5d19',
			"#FE8463",'#de7622','#f1908c','#207f4c','#22a2c3',
			"#9BCA63",'#815c94','#e16c96','#12a182','#bec936',
			'#D7504B', '#C6E579', '#F4E001', '#F0805A', '#26C0C0',
			'#FFB7DD', '#660077', '#FFCCCC', '#FFC8B4', '#550088',
			'#FFFFBB', '#FFAA33', '#99FFFF', '#CC00CC', '#FF77FF',
			'#C63300', '#9955FF', '#66FF66', '#129393', '#395203',
			'#C1232B', '#B5C334', '#FCCE10', '#E87C25', '#27727B',
			'#FAD860', '#F3A43B', '#60C0DD', '#0D7CAA']
    let bjcounts=ref([])
    let counts=ref({})
    let url=ref('')
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

        getgksj()
        getyjcout()
        getbjtj()
        getseclc()
    })
    const getgksj=async()=>{
          const {data:res}=await gettable('GetJKBaseInfo',getform.value)
        // console.log('获取概况',res);
        if (res.code=="1000") {
            formlabe.value=res.data
        }
    }
    const getfile=(val)=>{
             let filetype=''
             let lastIndex= val.lastIndexOf('.')
             let file=val.substring(lastIndex+1)
            switch (file) {
                case 'pdf':
                    filetype='pdf'
                    break;
                case 'docx':
                case 'doc':
                    filetype='word'
                    break;
                case 'xlsx':
                case 'xls':
                    filetype='excel'
                    break;
                case 'ppt':
                    filetype='ppt'
                    break;
            }
            
            return filetype
      }
    const open=(val)=>{
        if (val) {
          let imgtype=['png','jpg']
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1)
          if (imgtype.includes(file)) {
              picimg.value.piclist(val)
          }else{
              window.open('https://f.zqface.com/?fileurl='+val,'_slef')
          }
        }
      }
    // 报警统计
    const getyjcout=async()=>{
          const {data:res}=await gettable('GetWarningJKHighFormworkInfo',getform.value)
            if (res.code=="1000") {
                woring.value=res.data.ECharts
                yjcount.value=res.data.PointNum
                getworing()
            }
    }
    const getbjtj=async()=>{
          const {data:res}=await gettable('GetJKMonitorStatistics',getform.value)
            // console.log('获取报警统计',res);
            if (res.code=="1000") {
                bjcounts.value=res.data.ECharts
                counts.value=res.data
                getMonitorings()
            }
    }
    const getMonitorings=()=>{
            var echarts = require('echarts');
			let myChart = echarts.getInstanceByDom(document.getElementById("Monitorings"))
            if (myChart == null) {
                myChart = echarts.init(document.getElementById('Monitorings'));
            }
            // console.log('获取',myChart);
            
            let colorList=WorkTypecolor
            let option = {
					title: [{
							text: '总数量',
							textStyle: {
								color: "#fff",
								fontSize: 18,
							},
							itemGap: 10,
							left: "center",
							top: "54%",
						},
						{
							text: counts.value.MonitorNum,
							textStyle: {
								color: "#fff",
								fontSize: 15,
								fontWeight: "normal",
							},
							itemGap: 10,
							left: "center",
							top: "40%",
						},
					],
					tooltip: {
						trigger: "item",
					},
					series: [{
						hoverAnimation: false,
						type: "pie",
						center: ["50%", "50%"],
						radius: ["60%", "90%"],
						clockwise: true,
						avoidLabelOverlap: true,
						hoverOffset: 15,
						itemStyle: {
							normal: {
								color: function(params) {
									return colorList[params.dataIndex];
								},
							},
						},
						label: {
							show: false,
						},
						labelLine: {},
						data: bjcounts.value,
					}, ],
				};
			myChart.setOption(option);
			window.addEventListener("resize", function() {
				myChart.resize();
			});
        }
    const getworing=()=>{
            var echarts = require('echarts');
			// var myecharts = echarts.init(document.getElementById('Alarmstatistics'));
			let myChart = echarts.getInstanceByDom(document.getElementById("Alarmstatistics"))
            if (myChart == null) {
                myChart = echarts.init(document.getElementById('Alarmstatistics'));
                }
            let colorList=WorkTypecolor
            let option = {
					title: [{
							text: '总数量',
							textStyle: {
								color: "#fff",
								fontSize: 18,
							},
							itemGap: 10,
							left: "center",
							top: "54%",
						},
						{
							text: yjcount.value,
							textStyle: {
								color: "#fff",
								fontSize: 15,
								fontWeight: "normal",
							},
							itemGap: 10,
							left: "center",
							top: "40%",
						},
					],
					tooltip: {
						trigger: "item",
					},
					series: [{
						hoverAnimation: false,
						type: "pie",
						center: ["50%", "50%"],
						radius: ["60%", "90%"],
						clockwise: true,
						avoidLabelOverlap: true,
						hoverOffset: 15,
						itemStyle: {
							normal: {
								color: function(params) {
									return colorList[params.dataIndex];
								},
							},
						},
						label: {
							show: false,
						},
						labelLine: {},
						data: woring.value,
					}, ],
				};
				// myChart.setOption(option);
				myChart.setOption(option);
				window.addEventListener("resize", function() {
					myChart.resize();
				});
        }
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
    const getseclc=async()=>{
          const {data:res}=await gettable('GetGZMImageByEquipCode',getform.value)

			// const  {data:res }= await this.$http.post('/aiot/Api.ashx?PostType=get&Type=GetGZMImageByEquipCode', this.getform)
            // console.log('获取',res);
            if (res.code=="1000") {
                // this.options=res.data
                url.value=res.data[0].GZMImage
            }
        }

    return{
        formlabe,
        getform,
        btns,
        woring,
        yjcount,
        WorkTypecolor,
        getbjtj,
        bjcounts,
        counts,
        bgcolor,
        url,

        getfile,
        open,
        getyjcout,
        // geteqment,
        getworing,
        getMonitorings,
        tableRowClassName,
        getseclc


    }
}
}
</script>

<style lang="scss" scoped>
.Highsupportmolds{
  display: grid;
  grid-template-columns: 40% 60%;
  height: 100%;
  width: 100%;
  color: #fff;
  &-two{
    margin: 10px;
    img{
      width: 30px;
      height: 30px;
    }
  }
}
.tablebotm{
    p{
        margin-bottom: 10px;
    }
}
.monitors{
    display: flex;
}
.picimg{
    width: 30px;
    height: 30px;
}
.prows{
    display: inline-block;
    margin: 10px;
}
.el-scrollbar{
    width: 60%;
}
#Monitorings{
    width: 50%;
    height: 150px;
}
.eqmentid{
    width: 100%;
    height: 150px;
}
.icons{
    width: 12px;
    height: 12px;
    border-radius: 100%;
}
.rightbom{
    display: flex;
    width: 100%;
}
.labelechart{
    display: grid;
    grid-template-columns: 10% 55% 20% 15%;
    margin: 10px;
    align-items: center;
}
#Alarmstatistics{
    width: 50%;
    height: 150px;
}
</style>