<template>
  <!-- 磅房信息 -->
  <div class="Largevolume padding"  :style="{color:bgcolor.font}">
    <!-- <div :class="['Largevolume-top','righticon']"
    :style="{background:'linear-gradient(90deg, rgba(1, 194, 255, 0) 0%,'+bgcolor.titlecolor+' 97%)'}"
    >
        <span class="padding-text-span" >{{bigcew.titles}}</span>
        <img :src="bigcew.src"  >
    </div> -->
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms" @opens="opentable()"></Chamfering>

    <div v-if="bigcew.titles=='大体积混泥土测温'||bigcew.titles=='临时管理'" class="Largevolume-two">
      <div class="Largevolume-two-left" v-for="(item,index) in Largevolumelable" :key="index" v-show="item.type!=2">
        <p>{{item.name}}：{{form[item.value]}}</p>
      </div>
    </div>
    <div v-else class="Largevolume-three">
        <p>会议次数：{{totles}}</p>
        <el-scrollbar height="180px">
            <p v-for="(item,index) in gridData" :key="index" class="scrollbar-demo-item cursor"
            :style="(index%2)!=0?'background: rgba(15, 43, 63, 0.6)':''" :title="item.MeetingName"
            >{{item.MeetingTime}}&nbsp{{ item.MeetingName }}</p>
        </el-scrollbar>
      </div>
      <Chamfering :homeindex="4" :horn="0"></Chamfering>
      <delog ref="delogs"></delog>
  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/technology/content/delog.vue";

export default {
props:['bigcew'],
components:{
  Chamfering,
  delog
},
setup(props){

  let bgcolor=ref({})
  let totles=ref(0)
//   let echart=ref(true)
//   let countlist=ref([])
  let getform=ref({
      MeetingName:'',
      ProjectCode:store.getters.code,
      page:1,
      count:1000
    })
    let Largevolumelable=ref([
        {
            name:'房间数量',
            value:'RoomNum',
            
        },{
            name:'使用人数',
            value:'Person'
        },{
            name:'设计单位',
            value:'DesignName'
        },{
            name:'承建单位',
            value:'',
            type:2
        },{
            name:'安装单位',
            value:'InstallationUnit'
        },{
            name:'施工单位',
            value:'BuildName'
        },{
            name:'安装日期',
            value:'InstallationDate',
            type:2
        }
    ])
    let gridData=ref([])
    let form=ref({})
    let topforms=ref({
      url:props.bigcew.src,
      name:props.bigcew.titles,
      text:'更多记录',
      lefs:'lefs'
    })
    let delogs=ref(null)
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      gettablemeting()
      gettables()
      switch (props.bigcew.titles) {
        case '临时管理':
          gettemporary()
          
          break;
      }
  })
  const gettables=async()=>{
    const {data:res}=await gettable('GetMeetingSummaryInfo',getform.value)
    if (res.code=="1000") {
      totles.value=res.data.AllCount
      gridData.value=res.data.MeetingInfo
    }
  }
  const opentable=()=>{ 
    delogs.value.showdelog(0,'会议纪要')
  }
// GetMeetingSummaryTable
  const gettablemeting=async()=>{
// GetMeetingSummaryTable
      const {data:res}=await gettable('GetMeetingSummaryTable',getform.value)
        // console.log('获取会议',res,res.Total);
        
        if (res.code=="1000") {
          gridData.value=res.data
        }
        totles.value=res.Total
  }
  // 临时管理
  const gettemporary=async()=>{
      const {data:res}=await gettable('GetTemporaryManageInfo',getform.value)
      // console.log('临时数据',res);
      if (res.code=="1000") {
        form.value=res.data
      }
  }


 

	return{
		getform,
    totles,
		bgcolor,
    gridData,
    Largevolumelable,
    form,
    topforms,
    delogs,

    gettablemeting,
    gettables,
    gettemporary,
    opentable

	// getLargevolume,
	}
}
}
</script>

<style lang="scss" scoped>
.Largevolume{
&-two{
  height: 86%;
  padding: 10px;
  display: grid;
  font-size: 14px;
//   grid-template-columns: 60% 40%;
  align-items: center;
    // img{
    //     grid-row: 1/span 4;
    //     grid-column: 2;
    // }
    p{
        text-align: start;
    }
}
&-three{
    height: 85%;
    p{
        font-size: 14px;
        text-align: start;
        padding: 10px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    .scrollbar-demo-item{
        border: 1px solid #0E9CFF;
    }
}
}



</style>