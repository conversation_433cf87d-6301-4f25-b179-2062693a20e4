"use strict";(self["webpackChunksmart"]=self["webpackChunksmart"]||[]).push([[445],{37984:function(e,a,l){l.d(a,{Z:function(){return d}});var t=l(73396);const o=["id"];function s(e,a,l,s,n,u){return(0,t.wg)(),(0,t.iD)("div",{id:l.ids,class:"qwtj"},null,8,o)}var n=l(44870),u={props:["ids","options1"],setup(e,a){(0,n.iH)(e.options1);let o=null;(0,t.YP)((()=>e.options1),((e,a)=>{e&&(0,t.Y3)((()=>{s()}))}),{immediate:!0,deep:!0});const s=t=>{var s=l(30197);o=s.getInstanceByDom(document.getElementById(e.ids)),null==o&&(o=s.init(document.getElementById(e.ids))),o.off("click"),o.on("click",(function(e){"line"===e.seriesType&&a.emit("popsdelog",e)})),o.setOption(e.options1),window.addEventListener("resize",(function(){o?.resize()}))};return(0,t.Jd)((()=>{o.dispose()})),{getecharts:s}}},i=l(40089);const r=(0,i.Z)(u,[["render",s],["__scopeId","data-v-69f7285e"]]);var d=r},63815:function(e,a,l){l.d(a,{Z:function(){return z}});l(57658);var t=l(73396),o=l(44870),s=l(87139),n=l(10455),u=l(18089),i=l(36331);function r(e){let a=[],l=[{name:"施工方案列表",value:[{name:"序号",value:"rowNum",widths:"60"},{name:"报审日期",value:"CreateTime",widths:""},{name:"方案名称",value:"ProgrammeName",widths:""},{name:"方案类型",value:"ProgrammeType",widths:""},{name:"编制人",value:"Editor",widths:""},{name:"方案状态",value:"ProgrammeState",widths:""},{name:"二维码",value:"QRCode",widths:"",img:"true"}]},{name:"施工方案列表查询",value:[{name:"日期",value:"CreateTime",type:3,widths:"150"},{name:"关键字搜索",value:"ProgrammeName",type:1,widths:"150"}]},{name:"施工方案详情",value:[{name:"基本信息",value:"",type:"0",widths:""},{name:"方案状态",value:"ProgrammeState",type:"0",widths:""},{name:"创建人",value:"Creator",type:"0",widths:""},{name:"创建时间",value:"CreateTime",type:"0",widths:""},{name:"方案类型",value:"ProgrammeType",type:"0",widths:""},{name:"方案名称",value:"ProgrammeName",type:"0",widths:""},{name:"编制人",value:"Editor",type:"0",widths:""},{name:"编制完成时间",value:"EditorTime",type:"0",widths:""},{name:"报审时间",value:"SubmissionTime",type:"0",widths:""},{name:"备注",value:"Remake",type:"0",widths:""},{name:"方案附件列表",value:"ProgrammeFile",type:"1",widths:""},{name:"审批信息",value:"",type:"0",widths:""},{name:"施工单位审批",value:"ConstructionUnit",type:"0",widths:""},{name:"审批时间",value:"ConstructionTime1",type:"0",widths:""},{name:"审批人",value:"ConstructionPerson1",type:"0",widths:""},{name:"",value:"",type:"0",widths:""},{name:"审批时间",value:"ConstructionTime2",type:"0",widths:""},{name:"审批人",value:"ConstructionPerson2",type:"0",widths:""},{name:"监理单位审批",value:"ControlUnit",type:"0",widths:""},{name:"审批时间",value:"ControlTime1",type:"0",widths:""},{name:"审批人",value:"ControlPerson1",type:"0",widths:""},{name:"",value:"",type:"0",widths:""},{name:"审批时间",value:"ControlTime2",type:"0",widths:""},{name:"审批人",value:"ControlPerson2",type:"0",widths:""},{name:"交底附件列表",value:"DisclosureFile",type:"1",widths:""}]},{name:"技术交底列表",value:[{name:"序号",value:"rowNum",widths:"60"},{name:"交底日期",value:"CreateTime",widths:""},{name:"交底名称",value:"DisclosureName",widths:""},{name:"交底类型",value:"DisclosureType",widths:""},{name:"交底人",value:"DisclosurePerson",widths:""},{name:"交底状态",value:"DisclosureState",widths:""},{name:"关联实施方案",value:"ProgrammeName",value1:"FileUrl",widths:""}]},{name:"技术交底详情",value:[{name:"基本信息",value:"",type:"0",widths:""},{name:"方案状态",value:"ProgrammeState",type:"0",widths:""},{name:"创建人",value:"DisclosurePerson",type:"0",widths:""},{name:"创建时间",value:"CreateTime",type:"0",widths:""},{name:"交底类型",value:"DisclosureType",type:"0",widths:""},{name:"交底部位",value:"DisclosurePosition",type:"0",widths:""},{name:"关联施工方案",value:"DisclosureType",type:"0",widths:""},{name:"交底人",value:"DisclosurePerson",type:"0",widths:""},{name:"交底时间",value:"DisclosureTime",type:"0",widths:""},{name:"备注",value:"Remake",type:"0",widths:""},{name:"附件列表",value:"FileList",type:"1",widths:""}]},{name:"施工日志列表",value:[{name:"序号",value:"rowNum",widths:"60"},{name:"日期",value:"ConstructionDate",widths:"120"},{name:"记录人",value:"Recorder",widths:"80"},{name:"记录内容",value:"RecordContent",widths:""},{name:"尚未解决问题",value:"Problems",widths:"110"},{name:"是否闭环",value:"ClosedLoop",widths:"100"}]},{name:"会议纪要列表",value:[{name:"序号",value:"rowNum",widths:"60"},{name:"日期",value:"MeetingTime",widths:"120"},{name:"会议主题",value:"MeetingName",widths:"80"},{name:"会议地点",value:"MeetingAddr",widths:""},{name:"与会人员",value:"Participants",widths:"110"},{name:"文件",value:"MeetingFileName",value1:"MeetingFile",widths:"100"}]},{name:"施工日志详情",value:[{name:"日志日期",value:"ConstructionDate",type:"0"},{name:"星期",value:"Week",type:"0"},{name:"气象",value:"Climate",type:"0"},{name:"风力风向",value:"WindDirectionPower",type:"0"},{name:"气温",value:"AirTemperature",type:"0"},{name:"记录内容",value:"RecordContent",type:"0"},{name:"照片",value:"ConstructionPhoto",type:"0",img:!0},{name:"尚待解决问题",value:"Problems",type:"0"},{name:"是否闭环",value:"ClosedLoop",type:"0"},{name:"记录人",value:"Recorder",type:"0"}]}];return l.forEach(((l,t)=>{l.name==e&&(a=l.value)})),a}var d=l(57597),m=l(24239),v=l(58829),c=l(15941);const p=e=>((0,t.dD)("data-v-1661ff5f"),e=e(),(0,t.Cn)(),e),w={key:0},g=["src","onClick"],y={key:3},h={class:"datedelog-body-one"},U=p((()=>(0,t._)("img",{src:n,alt:""},null,-1))),f={class:"datedelog-p"},S={key:0,class:"cursor-box"},C={key:0,class:"h2"},k={key:1},b={key:2},D=["src","onClick"],_={key:4,class:"table"};var H={__name:"delog",setup(e,{expose:a}){let l=(0,o.iH)(!1),n=(0,o.iH)({}),p=(0,o.iH)(""),H=(0,o.iH)([]),P=(0,o.iH)(!1),j=(0,o.iH)([]),z=(0,o.iH)([]),T=(0,o.iH)(0),x=[],I=(0,o.iH)(0),W=["基本信息","方案附件列表","审批信息","交底附件列表","附件列表"],N=(0,o.iH)({page:1,count:10,CreateTime:"",GetSpecialProgramTable:"",ProjectCode:m.Z.getters.code,InUserName:m.Z.getters.username}),V=(0,o.iH)(0),q=["施工日志详情"],Z=(0,o.iH)(""),L=(0,o.iH)([]),Y=(0,o.iH)(0),$=(0,o.iH)(!1),F=(0,o.iH)([]),G=["运单编号","偏差情况","基本信息","备注","方案附件列表","审批信息","交底附件列表","附件列表","记录内容","气温","照片","尚待解决问题"],R=[],A="",M=(0,o.iH)({}),E=(0,o.iH)(null),O=((0,o.iH)([]),!0),B=(0,o.iH)(""),K=(0,o.iH)([{name:"文件名称",value:"FileName",widths:""},{name:"文件大小",value:"FileSize",widths:""},{name:"上传人",value:"Uploader",widths:""},{name:"上传时间",value:"UploadTime",widths:""}]),J=["关联实施方案","文件"],Q=(0,o.iH)(null),X=(0,o.iH)(["新盛建设740"]),ee=(0,o.iH)(""),ae=(0,o.iH)([]);window.addEventListener("setthcolor",(()=>{n.value=JSON.parse(sessionStorage.getItem("themecolor"))})),(0,t.bv)((()=>{n.value=JSON.parse(sessionStorage.getItem("themecolor")),N.value.ProjectCode=m.Z.getters.code}));const le=(e,a)=>{switch(c.log("titleText",e,a),B.value="",p.value=a,x[0]=a,I.value=0,O=!0,L.value=[],Y.value=0,$.value=!1,a){case"施工方案":Z.value="GetSpecialProgramTable",z.value=r("施工方案列表查询"),j.value=r("施工方案列表");break;case"技术交底":Z.value="GetDisclosureTable",z.value=r("施工方案列表查询"),z.value[1].value="DisclosureName",j.value=r("技术交底列表");break;case"施工日志":Z.value="GetConstructionLogTableInfo",z.value=r("施工方案列表查询"),z.value[1].value="RecordContent",j.value=r("施工日志列表");break;case"会议纪要":Z.value="GetMeetingSummaryTable",z.value=r("施工方案列表查询"),z.value[0].value="MeetingTime",j.value=r("会议纪要列表");break}ie(),l.value=!0},te=()=>{N.value.page=1,N.value.count=10,ie()},oe=async e=>{},se=e=>{let a=["png","jpg","jpeg","gif","bmp"];if(e){let l=e.lastIndexOf("."),t=e.substring(l+1).toLowerCase();if(a.includes(t))E.value.piclist(e);else{let a=e.substring(e.lastIndexOf(".")+1).toLowerCase();"mp4"!==a&&window.open("https://f.zqface.com/?fileurl="+e,"_slef")}}},ne=e=>{const a=q.includes(p.value);return G.includes(e.name)?a?"grid-column: 1 /span 2":"grid-column: 1 /span 3":R.includes(e.name)?"grid-column: 1 /span 2":""},ue=e=>{se(e.FileUrl)},ie=async()=>{P.value=!0;const{data:e}=await(0,d.rT)(Z.value,N.value);P.value=!1,H.value=e.data,T.value=e.Total||0},re=async e=>{let a={GUID:e.GUID,ConstructionDate:e.Date||e.ConstructionDate,ProjectCode:m.Z.getters.code};const{data:l}=await(0,d.rT)(A,a);"1000"==l.code&&(M.value=Array.isArray(l.data)?l.data[0]:l.data,ae.value=l.data)},de=(e,a)=>{ae.value.length>=1&&(0===a&&V.value>0?V.value--:1===a&&V.value<ae.value.length-1&&V.value++,M.value=ae.value[V.value])},me=(e,a)=>0===a&&0===V.value||1===a&&V.value===ae.value.length-1?"color: #CCC;":"color: #01C2FF;",ve=()=>`border:2px solid ${n.value.titlecolor};\n      background:rgba(${n.value.delogcolor},0.35)`,ce=e=>e.widths?`width: ${e.widths}px`:"width: 200px",pe=e=>(re(e),!0),we=e=>{if(c.log("查看",e),O=!0,B.value="",!($.value&&Y.value>=1&&pe(e)))switch(ye(),Y.value++,p.value){case"施工方案":F.value=r("施工方案详情"),I.value=1,A="GetSpecialProgramDetailInfo",p.value=e.ProgrammeName,B.value=e.ProgrammeName,pe(e);break;case"技术交底":F.value=r("技术交底详情"),I.value=1,A="GetDisclosureDetailInfo",p.value=e.DisclosureName,B.value=e.DisclosureName,pe(e);break;case"施工日志":I.value=1,F.value=r("施工日志详情"),A="GetConstructionLogWeatherDetail",p.value="施工日志详情",ee.value=e.ConstructionDate||e.Date,B.value=e.ConstructionDate,re(e);break;default:if(Y.value>=2)if(e.GUID){if(pe(e))return}else p.value=`${p.value} - 详情`;break}},ge=(e,a)=>"偏差情况"==e.name?"负偏差"==a.Deviation?"color:  #01C2FF":"超负差"==a.Deviation?"color:  red":"color:  #008000":"",ye=()=>{L.value.push({title:p.value,labelist1:[...j.value],serch:[...z.value],url:Z.value,getform:{...N.value},tableDate:[...H.value],total:T.value,level:Y.value})},he=(e,a=!1,l=null)=>{I.value=0,O=!0,p.value=e.title,j.value=e.labelist1,z.value=e.serch,Z.value=e.url,N.value=e.getform,H.value=e.tableDate,T.value=e.total,Y.value=null!==l?l:e.level,a&&(L.value=[]),ie()},Ue=()=>{if($.value&&1===I.value&&L.value.length>0){const e=L.value.pop();he(e,!0,0)}else if(L.value.length>0){const e=L.value.pop();he(e)}else l.value=!1},fe=({row:e,rowIndex:a})=>a%2!=0?"warning-row":"",Se=e=>{c.log(`${e} 显示多少页`),N.value.count=e,N.value.page=1,ie()},Ce=e=>{c.log(`选择第几: ${e}`),N.value.page=e,ie()};return a({showdelog:le}),(e,a)=>{const r=(0,t.up)("el-input"),d=(0,t.up)("el-date-picker"),m=(0,t.up)("el-option"),c=(0,t.up)("el-select"),x=(0,t.up)("el-button"),Z=(0,t.up)("el-empty"),L=(0,t.up)("el-table-column"),Y=(0,t.up)("el-table"),$=(0,t.up)("el-pagination"),G=(0,t.up)("ArrowLeftBold"),R=(0,t.up)("el-icon"),A=(0,t.up)("ArrowRightBold"),le=(0,t.up)("el-dialog"),ie=(0,t.Q2)("loading");return(0,t.wg)(),(0,t.j4)(le,{modelValue:(0,o.SU)(l),"onUpdate:modelValue":a[4]||(a[4]=e=>(0,o.dq)(l)?l.value=e:l=e),"destroy-on-close":"",class:"delogss",width:(0,o.SU)(q).includes((0,o.SU)(p))?"50%":"70%"},{default:(0,t.w5)((()=>[(0,t.Wm)(u.Z,{ref:"selections",onColses:Ue,titles:(0,o.SU)(p)},null,8,["titles"]),"0"==(0,o.SU)(I)?((0,t.wg)(),(0,t.iD)("div",{key:0,class:"datedelog bodybottom bules",style:ve},[(0,o.SU)(B)?((0,t.wg)(),(0,t.iD)("h2",w,(0,s.zw)((0,o.SU)(p)),1)):(0,t.kq)("",!0),((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)((0,o.SU)(z),((e,a)=>((0,t.wg)(),(0,t.iD)("div",{class:"serch",key:a},[(0,t._)("span",null,(0,s.zw)(e.name)+":",1),1==e.type?((0,t.wg)(),(0,t.j4)(r,{key:0,modelValue:(0,o.SU)(N)[e.value],"onUpdate:modelValue":a=>(0,o.SU)(N)[e.value]=a,style:(0,s.j5)(ce(e)),placeholder:"请输入关键字",size:"small",clearable:""},null,8,["modelValue","onUpdate:modelValue","style"])):(0,t.kq)("",!0),3==e.type?((0,t.wg)(),(0,t.j4)(d,{key:1,modelValue:(0,o.SU)(N)[e.value],"onUpdate:modelValue":a=>(0,o.SU)(N)[e.value]=a,type:"date",size:"small",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:(0,s.j5)(ce(e))},null,8,["modelValue","onUpdate:modelValue","style"])):2==e.type?((0,t.wg)(),(0,t.j4)(c,{key:2,"popper-class":"bules",size:"small","popper-append-to-body":!0,clearable:"",style:(0,s.j5)(ce(e)),modelValue:(0,o.SU)(N)[e.value],"onUpdate:modelValue":a=>(0,o.SU)(N)[e.value]=a,class:"m-2",placeholder:"请选择",onClick:a=>oe(e)},{default:(0,t.w5)((()=>[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(e.list,((e,a)=>((0,t.wg)(),(0,t.j4)(m,{key:a,label:e.name,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["style","modelValue","onUpdate:modelValue","onClick"])):(0,t.kq)("",!0)])))),128)),(0,t.Wm)(x,{type:"primary",size:"small",onClick:te},{default:(0,t.w5)((()=>[(0,t.Uk)("搜索")])),_:1}),(0,t.Wm)(Y,{data:(0,o.SU)(H),class:"cursor",style:(0,s.j5)(["width: 100%",`color:${(0,o.SU)(n).font};\n              --el-table-border-color:${(0,o.SU)(n).titlecolor}`]),"row-class-name":fe,"header-cell-style":{background:"rgba(15, 43, 63, 0.6)",color:"#fff"},"max-height":"550px","empty-text":"暂无数据"},{empty:(0,t.w5)((()=>[(0,t.wy)((0,t.Wm)(Z,null,null,512),[[ie,(0,o.SU)(P)]])])),default:(0,t.w5)((()=>[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)((0,o.SU)(j),((e,a)=>((0,t.wg)(),(0,t.j4)(L,{key:a,width:e.widths,prop:e.value,label:e.name,align:"center"},{default:(0,t.w5)((a=>["1"==e.type?((0,t.wg)(),(0,t.iD)("span",{key:0,style:(0,s.j5)(ge(e,a.row))},(0,s.zw)(a.row[e.value]),5)):e.img&&a.row[e.value]?((0,t.wg)(),(0,t.iD)("img",{key:1,src:a.row[e.value],alt:"",style:{width:"80px",height:"80px"},class:"cursor",onClick:l=>se(a.row[e.value])},null,8,g)):(0,o.SU)(J).includes(e.name)?((0,t.wg)(),(0,t.j4)(x,{key:2,type:"primary",text:"",size:"small",onClick:l=>se(a.row[e.value1])},{default:(0,t.w5)((()=>[(0,t.Uk)((0,s.zw)(a.row[e.value]),1)])),_:2},1032,["onClick"])):((0,t.wg)(),(0,t.iD)("span",y,(0,s.zw)(a.row[e.value]),1))])),_:2},1032,["width","prop","label"])))),128)),(0,o.SU)(O)?((0,t.wg)(),(0,t.j4)(L,{key:0,label:"操作",align:"center",width:"100"},{default:(0,t.w5)((e=>[(0,t.Wm)(x,{type:"primary",text:"",size:"small",onClick:a=>we(e.row)},{default:(0,t.w5)((()=>[(0,t.Uk)("查看")])),_:2},1032,["onClick"])])),_:1})):(0,t.kq)("",!0)])),_:1},8,["data","style","header-cell-style"]),(0,t.Wm)($,{"current-page":(0,o.SU)(N).page,"onUpdate:currentPage":a[0]||(a[0]=e=>(0,o.SU)(N).page=e),"popper-class":"bules","page-size":(0,o.SU)(N).count,"onUpdate:pageSize":a[1]||(a[1]=e=>(0,o.SU)(N).count=e),"page-sizes":[10,20,40,50,100],background:!1,layout:"total, sizes, prev, pager, next, jumper",total:(0,o.SU)(T),onSizeChange:Se,onCurrentChange:Ce},null,8,["current-page","page-size","total"])])):((0,t.wg)(),(0,t.iD)("div",{key:1,class:(0,s.C_)("datedelog bodybottom bules "+((0,o.SU)(q).includes((0,o.SU)(p))?"lables":"lable")),style:ve},[(0,t._)("div",{class:"datedelog-header",style:(0,s.j5)(`background:linear-gradient(90deg, ${(0,o.SU)(n).titlecolor} 0%,\n            rgba(2, 193, 253, 0) 89%);color:${(0,o.SU)(n).font}`)},[(0,t._)("div",h,[U,(0,t._)("p",f,(0,s.zw)((0,o.SU)(ee)+(0,o.SU)(p)),1),(0,o.SU)(X).includes((0,o.SU)(N).InUserName)?((0,t.wg)(),(0,t.iD)("div",S,[(0,t.Wm)(R,{class:"cursor icon",style:(0,s.j5)(me((0,o.SU)(V),0)),onClick:a[2]||(a[2]=e=>de((0,o.SU)(ae),0)),disabled:0==(0,o.SU)(V)},{default:(0,t.w5)((()=>[(0,t.Wm)(G)])),_:1},8,["style","disabled"]),(0,t._)("span",null,(0,s.zw)((0,o.SU)(V)+1)+"/"+(0,s.zw)((0,o.SU)(ae).length),1),(0,t.Wm)(R,{class:"cursor icon",style:(0,s.j5)(me((0,o.SU)(V),1)),onClick:a[3]||(a[3]=e=>de((0,o.SU)(ae),1)),disabled:(0,o.SU)(V)==(0,o.SU)(ae).length-1},{default:(0,t.w5)((()=>[(0,t.Wm)(A)])),_:1},8,["style","disabled"])])):(0,t.kq)("",!0)])],4),((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)((0,o.SU)(F),((e,a)=>((0,t.wg)(),(0,t.iD)("div",{class:(0,s.C_)([(0,o.SU)(W).includes(e.name)?"":"text","1"==e.type?"full-width":""]),key:a,style:(0,s.j5)(ne(e))},[(0,o.SU)(W).includes(e.name)?((0,t.wg)(),(0,t.iD)("h2",C,(0,s.zw)(e.name),1)):"0"==e.type&&e.name?((0,t.wg)(),(0,t.iD)("span",k,(0,s.zw)(e.name)+"：",1)):(0,t.kq)("",!0),"0"==e.type?((0,t.wg)(),(0,t.iD)("span",b,(0,s.zw)((0,o.SU)(M)[e.value]),1)):e.img?((0,t.wg)(),(0,t.iD)("img",{key:3,src:(0,o.SU)(M)[e.value],class:"cursor",onClick:a=>se((0,o.SU)(M)[e.value]),alt:"",style:{width:"180px",height:"180px"}},null,8,D)):(0,t.kq)("",!0),"1"==e.type?((0,t.wg)(),(0,t.iD)("div",_,[(0,t.Wm)(Y,{data:(0,o.SU)(M)[e.value],class:"cursor",style:(0,s.j5)(["width: 100%",`color:${(0,o.SU)(n).font};\n                --el-table-border-color:${(0,o.SU)(n).titlecolor}`]),"row-class-name":fe,"header-cell-style":{background:"rgba(15, 43, 63, 0.6)",color:"#fff"},"max-height":"200px","empty-text":"暂无数据",onRowClick:ue},{empty:(0,t.w5)((()=>[(0,t.wy)((0,t.Wm)(Z,null,null,512),[[ie,(0,o.SU)(P)]])])),default:(0,t.w5)((()=>[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)((0,o.SU)(K),((e,a)=>((0,t.wg)(),(0,t.j4)(L,{key:a,width:e.widths,prop:e.value,label:e.name,align:"center","show-overflow-tooltip":""},null,8,["width","prop","label"])))),128))])),_:2},1032,["data","style","header-cell-style"])])):(0,t.kq)("",!0)],6)))),128))],2)),(0,t.Wm)(i.Z,{ref_key:"picimgs",ref:E},null,512),(0,t.Wm)(v.Z,{ref_key:"delogshows",ref:Q},null,512)])),_:1},8,["modelValue","width"])}}},P=l(40089);const j=(0,P.Z)(H,[["__scopeId","data-v-1661ff5f"]]);var z=j},58829:function(e,a,l){l.d(a,{Z:function(){return k}});var t=l(73396),o=l(44870),s=l(87139),n=l(10455),u=l(24239),i=l(57597),r=l(18089),d=l(36331);const m=e=>((0,t.dD)("data-v-63cf2f7d"),e=e(),(0,t.Cn)(),e),v={class:"datedelog-body-one"},c=m((()=>(0,t._)("img",{src:n,alt:""},null,-1))),p={class:"datedelog-p"},w={key:0,class:"cursor-box"},g={key:0,class:"datedelog-content"},y={key:0},h=["src","onClick"],U={key:1,class:"datedelog-content1"};var f={__name:"delog",setup(e,{expose:a}){let l=(0,o.iH)({}),n=(0,o.iH)(!1),m=[{name:"日志日期",value:"ConstructionDate"},{name:"星期",value:"Week"},{name:"气象",value:"Climate"},{name:"风力风向",value:"WindDirectionPower"},{name:"气温",value:"AirTemperature"},{name:"记录内容",value:"RecordContent"},{name:"照片",value:"ConstructionPhoto"},{name:"尚待解决问题",value:"Problems"},{name:"是否闭环",value:"ClosedLoop"},{name:"记录人",value:"Recorder"}],f=(0,o.iH)({InUserName:u.Z.getters.username}),S=(0,o.iH)(null),C=(0,o.iH)(0),k=(0,o.iH)({}),b=(0,o.iH)(""),D=["记录内容","运单编号","尚待解决问题"],_=(0,o.iH)([]),H=(0,o.iH)(0),P=(0,o.iH)(["新盛建设740"]);window.addEventListener("setthcolor",(()=>{l.value=JSON.parse(sessionStorage.getItem("themecolor"))})),(0,t.bv)((()=>{l.value=JSON.parse(sessionStorage.getItem("themecolor"))}));const j=(e,a)=>{H.value=0,b.value=a.Date||a.ConstructionDate,C.value=e,x(a),n.value=!0},z=()=>{n.value=!1},T=e=>{let a=["png","jpg","jpeg","gif","bmp"];if(e){let l=e.lastIndexOf("."),t=e.substring(l+1).toLowerCase();if(a.includes(t))S.value.piclist(e);else{let a=e.substring(e.lastIndexOf(".")+1).toLowerCase();"mp4"!==a&&window.open("https://f.zqface.com/?fileurl="+e,"_slef")}}},x=async e=>{let a={GUID:e.GUID,ConstructionDate:e.Date||e.ConstructionDate,ProjectCode:u.Z.getters.code};const{data:l}=await(0,i.rT)("GetConstructionLogWeatherDetail",a);"1000"==l.code&&(k.value=Array.isArray(l.data)?l.data[0]:l.data,_.value=l.data)},I=(e,a)=>{_.value.length>=1&&(0===a&&H.value>0?H.value--:1===a&&H.value<_.value.length-1&&H.value++,k.value=_.value[H.value])},W=(e,a)=>0===a&&0===H.value||1===a&&H.value===_.value.length-1?"color: #CCC;":"color: #01C2FF;",N=e=>{if(D.includes(e.name))return"grid-column: 1/2 span;"},V=()=>{window.location.href="https://ai.zqface.com/manage/#/administrations/Constructionlog"};return a({showdelog:j}),(e,a)=>{const u=(0,t.up)("ArrowLeftBold"),i=(0,t.up)("el-icon"),D=(0,t.up)("ArrowRightBold"),j=(0,t.up)("el-dialog");return(0,t.wg)(),(0,t.j4)(j,{modelValue:(0,o.SU)(n),"onUpdate:modelValue":a[3]||(a[3]=e=>(0,o.dq)(n)?n.value=e:n=e),"destroy-on-close":"",class:"delogss",width:"50%"},{default:(0,t.w5)((()=>[(0,t.Wm)(r.Z,{ref:"selections",onColses:z},null,512),(0,t._)("div",{class:"datedelog bodybottom",style:(0,s.j5)(`border:2px solid ${(0,o.SU)(l).titlecolor};\n    background:rgba(${(0,o.SU)(l).delogcolor},0.35)`)},[(0,t._)("div",{class:"datedelog-header",style:(0,s.j5)(`background:linear-gradient(90deg, ${(0,o.SU)(l).titlecolor} 0%,\n            rgba(2, 193, 253, 0) 89%);color:${(0,o.SU)(l).font}`)},[(0,t._)("div",v,[c,(0,t._)("p",p,(0,s.zw)((0,o.SU)(b))+"施工日志",1),(0,o.SU)(P).includes((0,o.SU)(f).InUserName)?((0,t.wg)(),(0,t.iD)("div",w,[(0,t.Wm)(i,{class:"cursor icon",style:(0,s.j5)(W((0,o.SU)(H),0)),onClick:a[0]||(a[0]=e=>I((0,o.SU)(_),0)),disabled:0==(0,o.SU)(H)},{default:(0,t.w5)((()=>[(0,t.Wm)(u)])),_:1},8,["style","disabled"]),(0,t._)("span",null,(0,s.zw)((0,o.SU)(H)+1)+"/"+(0,s.zw)((0,o.SU)(_).length),1),(0,t.Wm)(i,{class:"cursor icon",style:(0,s.j5)(W((0,o.SU)(H),1)),onClick:a[1]||(a[1]=e=>I((0,o.SU)(_),1)),disabled:(0,o.SU)(H)==(0,o.SU)(_).length-1},{default:(0,t.w5)((()=>[(0,t.Wm)(D)])),_:1},8,["style","disabled"])])):(0,t.kq)("",!0)])],4),0==(0,o.SU)(C)?((0,t.wg)(),(0,t.iD)("div",g,[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)((0,o.SU)(m),((e,a)=>((0,t.wg)(),(0,t.iD)("div",{key:a,class:(0,s.C_)("text"),style:(0,s.j5)(N(e))},[(0,t._)("span",null,(0,s.zw)(e.name)+"：",1),6!==a?((0,t.wg)(),(0,t.iD)("span",y,(0,s.zw)((0,o.SU)(k)[e.value]),1)):6==a?((0,t.wg)(),(0,t.iD)("img",{key:1,src:(0,o.SU)(k)[e.value],class:"cursor",onClick:a=>T((0,o.SU)(k)[e.value]),alt:"",style:{width:"180px",height:"180px"}},null,8,h)):(0,t.kq)("",!0)],4)))),128))])):1==(0,o.SU)(C)?((0,t.wg)(),(0,t.iD)("div",U,[(0,t._)("p",{class:"cursor",onClick:a[2]||(a[2]=e=>V())},"当天未上传施工日志！")])):(0,t.kq)("",!0)],4),(0,t.Wm)(d.Z,{ref_key:"picimgs",ref:S},null,512)])),_:1},8,["modelValue"])}}},S=l(40089);const C=(0,S.Z)(f,[["__scopeId","data-v-63cf2f7d"]]);var k=C}}]);