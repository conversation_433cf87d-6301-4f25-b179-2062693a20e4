<template>
  <el-calendar class="green" ref="calendar" :style="[`border:2px solid ${bgcolor.titlecolor};background:rgba(${bgcolor.delogcolor},0.35)`]">
    <template #header="{ date }">
      <div class="datecount">
        <span class="datecount-one">{{ date }}</span>
        <div class="circlecount">
            <div v-for="(item,index) in colorlist" :key="index" @click="open(item)" class="circle cursor" :style="`background:${item.color}`">{{item.name}}</div>
        </div>
      </div>
      <el-button-group>
        <el-button size="small" @click="selectDate('prev-month')">
         上个月
        </el-button>
        <el-button size="small" @click="selectDate('today')">今天</el-button>
        <el-button size="small" @click="selectDate('next-month')">
        下个月
        </el-button>
      </el-button-group>
    </template>
    <template #date-cell="{ data }">
      <p :class="data.isSelected ? 'is-selected' : ''">
        {{data.day.split('-').slice(2).join('-')}}
      </p>
      <div v-for="(item,index) in caletable" :key="index" class="circlecounts" v-show="data.day==item.date">
        <el-scrollbar height="85px" style="width:100%;height:85px">
            <div v-for="(iss,i) in colorlist" :key="i"  class="circle" @click="open(iss)"
            v-show="data.day==item.date&&item.type.includes(iss.type) " :style="`background:${iss.color}`">
                {{iss.name}}
            </div>
        </el-scrollbar>
      </div>
    </template>
  </el-calendar>
  <el-dialog v-model="dialogTableVisible" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]"  destroy-on-close
    class="construction" width="60%" >
    <div class="heaereq-one">
        <div class="toplist" >
            <div :class="['heaereq cursor']"
            :style="[`background:rgba(${bgcolor.delogcolor},0.35)`]">
                <div class="icontop1 bor" :style="[`border:1px solid ${bgcolor.titlecolor}`]" ></div>
                <div class="icontop" :style="[`border:1px solid ${bgcolor.titlecolor}`]"></div>
                <div class="icontop2 bor" :style="[`border:1px solid ${bgcolor.titlecolor}`]"></div>
            </div>
        </div>
        <div class="closedelog cursor" :style="[`background: radial-gradient(50% 50% at 50% 50%,
         rgba(3, 251, 255, 0.17) 0%, ${bgcolor.hovercor} 100%);left:99%`]" @click="close()">
            <el-icon class="closeicon"><CloseBold /></el-icon>
        </div>
    </div>
    <div class="construction-body echartshea">
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
            <div class="datedelog-body-one">
                <img src="@/assets/img/home/<USER>" alt="">
                <p class="datedelog-p">{{headerna}}</p>
            </div>
        </div>
    </div>
    <div class="imgswiper echaimg">
      <swiper
         :slides-per-view="3"
         :navigation="{
             nextEl: '.swiper-button-next', //前进后退按钮
             prevEl: '.swiper-button-prev',
         }"
         :space-between="20"
         :autoplay="{ disableOnInteraction: false }" 
         class="teacher_ul"
         >
         <swiper-slide class="teacher_li" v-for="(item, index) in piclist" :key="index">
             <div class="teacher_pW">
                 <img v-lazy="item.ImgUrl" :key="index" @click="pic(item.ImgUrl)" alt="" class="cursor" style="height:150px">
                 <div class="teacher_pW-top">
                     {{item.index+1}}
                 </div>
             </div>
         </swiper-slide>
         <!-- <div class="swiper-button-next"></div> -->
        <!-- <div class="swiper-button-prev"></div> -->
      </swiper>
      <!-- <div class="swiper-button-next"></div> -->
      <!-- <div class="swiper-button-prev"></div> -->
    </div>
    <div class="construction-body echartshea">
      <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
        rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
        <div class="datedelog-body-one">
            <img src="@/assets/img/home/<USER>" alt="">
            <p class="datedelog-p">{{headerna}}记录</p>
        </div>
      </div>
    </div>
    <div id="echartline">
      <qualityechart :refs="'calechartline'" :ids="'calechartline'"  :options1="options"></qualityechart>
    </div>
    <picimg ref="picimg"></picimg>
    </el-dialog>
</template>

<script>
import { CalendarDateType, CalendarInstance } from 'element-plus'
import { onMounted, ref } from 'vue'
import 'swiper/swiper-bundle.css'
import SwiperCore, {
  Autoplay,
} from "swiper";
//例如
import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/vue'
SwiperCore.use([Navigation, Pagination, Scrollbar, Autoplay]);
import picimg from "@/components/connet/Common/picimg.vue";
import qualityechart from "@/components/connet/Common/echartscom.vue";

export default {
  components:{
        Swiper,
        SwiperSlide,
        picimg,
        qualityechart
    },
setup(){
    let bgcolor=ref({})
    let picimg=ref(null)
    let options=ref({})
    let piclist=ref([
      {
        // ImgUrl:require('@/assets/img/home/<USER>'),
        index:0
      },
      {
        // ImgUrl:require('@/assets/img/home/<USER>'),
        index:1
      },
      {
        // ImgUrl:require('@/assets/img/home/<USER>'),
        index:2
      },{
        // ImgUrl:require('@/assets/img/home/<USER>'),
        index:3
      }
    ])
    let titles=ref('')
    let headerna=ref('')
    let dialogTableVisible=ref(false)
    let colorlist=ref([
        {
        name:'污',
        color:'#FB8B05',
        head:'污水监测',
        titles:'ph值',
        type:1
        },{
        name:'洒',
        color:'#5E5314',
        head:'洒水清扫',
        titles:'洒水清扫次数',
        type:2
        },{
        name:'化',
        color:'#E2C027',
        head:'化粪池清理',
        titles:'化粪池清理次数',
        type:3
        },{
        name:'隔',
        color:'#9B1E64',
        head:'隔油池清理',
        titles:'隔油池清理次数',
        type:4
        },{
        name:'消',
        color:'#87723E',
        head:'现场消毒',
        titles:'消毒次数',
        type:5
        },{
        name:'垃',
        color:'#4D4030',
        head:'生活垃圾外运',
        titles:'桶',
        type:6
        },{
        name:'噪',
        color:'#FEBA07',
        head:'手持式噪音监测',
        titles:'检查结果',
        type:7
        },{
        name:'尘',
        color:'#DC9123',
        head:'手持式扬尘监测',
        titles:'检测结果',
        type:8
        },{
        name:'渣',
        color:'#F6CEC1',
        head:'建筑垃圾、渣土外运',
        titles:'载重(t)',
        },{
        name:'混',
        color:'#F86B1D',
        head:'混凝土进场验收',
        titles:'吨',
        },{
        name:'钢',
        color:'#954416',
        head:'钢筋进场验收',
        titles:'吨（t）',
        },{
        name:'砖',
        color:'#732E12',
        head:'砖块进场验收',
        titles:'万块',
        },{
        name:'PC',
        color:'#1A6840',
        head:'PC版进场验收',
        titles:'块',
        },{
        name:'测',
        color:'#8B614D',
        head:'实测实量',
        titles:'次',
        },{
        name:'水',
        color:'#2E317C',
        head:'智能水表',
        titles:'本次用水量（m³）',
        },{
        name:'基',
        color:'#475164',
        head:'基坑降水记录',
        titles:'收集的降水量（m³）',
        },{
        name:'雨',
        color:'#126E82',
        head:'雨水收集',
        titles:'收集水量（m³）',
        },{
        name:'中',
        color:'#2C9678',
        head:'中水回用',
        titles:'本次用水量（m³）',
        },{
        name:'维',
        color:'#411C35',
        head:'设备维保',
        titles:'次',
        },{
        name:'电',
        color:'#0EB0C9',
        head:'智能电表',
        titles:'本次用电量（kW·h）',
        },{
        name:'油',
        color:'#141E1B',
        head:'油的用途',
        titles:'升（L）',
        },
    ])
    const calendar =ref();
    let caletable=ref([
        {
          date:'2024-01-02',
          type:[1] 
        },{
          date:'2024-01-03',
          type:[1,2,3,4,5,6,7,8,9] 
        },{
          date:'2024-01-04',
          type:[1,2]
        },{
          date:'2024-01-05',
          type:[1,3]
        },
    ])
    window.addEventListener('setthcolor', ()=> {
            // console.log('导航');
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // console.log('获取日期',);
        
    })
    const selectDate = (val) => {
    if (calendar.value) {
        // console.log('获取',val,calendar.value);
        calendar.value.selectDate(val);
    }
    };
    const open=(val)=>{
      headerna.value=val.head
      titles.value=val.titles
      dialogTableVisible.value=true
        getecharts()

    }
    const close=()=>{
      dialogTableVisible.value=false

    }
    const pic=(val)=>{
        picimg.value.piclist(val)
    }
    const getecharts=()=>{
      options.value = {
        tooltip: {
            trigger: 'axis'
          },
          grid: {
          top:'20%',
          left: '3%',
          right: '4%',
          bottom: '8%',
          containLabel: true
        },
        xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLine: {
						lineStyle: {
							color: 'rgba(3, 251, 255, 0.3)'
						}
            
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						interval: 0,
						// textStyle: {
						color:'#03FBFF',
						// },
						// 默认x轴字体大小
						fontSize: 12,
						// margin:文字到x轴的距离
						margin: 15
					},
        data: ['2024-1-26','2024-1-27','2024-1-28','2024-1-29']
      },
      yAxis: {
        name:titles.value,
        nameTextStyle:{
          color:'#fff',
        },
        type: 'value',
        // axisTick: {
				// 		// show: false
				// 	},
					axisLabel: {
						// textStyle: {
							color: '#03FBFF'
						// }
					},
					 splitLine :{    //网格线
              lineStyle:{
                  type:'dashed',
                  color:'rgba(3, 251, 255, 0.3)'   //设置网格线类型 dotted：虚线   solid:实线
              },
              show:true //隐藏或显示
          }
      },
      series: [
        {
          name: '洒扫次数',
          type: 'line',
          stack: 'Total',
          color:'#03FBFF',
          data: [200,150,300]
        }
      ]
        };
    }
    const getpic=async()=>{
        let i=0
        // const {data:res}=await gettable('GetQualityAlbumSel',getform.value)
        // // console.log('获取项目照片',res);
        // if (res.code=="1000") {
        //     piclist.value=res.data

        //     piclist.value.map((item,index)=>{
        //         if (item.IsShow=='展示中') {
        //             item.index=i++
        //         }
        //         // item.count=i
        //     })
        //     count.value=i
        // }
    }
    return{
        bgcolor,
        picimg,
        titles,
        options,
        piclist,
        headerna,
        dialogTableVisible,
        colorlist,
        calendar,
        caletable,
        selectDate,
        open,
        close,
        pic,
        getpic,
        getecharts
    }
}
}
</script>
<style lang="scss">
.green{
    height: 92%!important;
    .el-calendar-table thead th{
        color: #fff!important;
    }
    .el-calendar__body{
        height: 82%!important;
    }
    .el-calendar-day{
        padding: 3px 0px;
    }
    .el-calendar-table .el-calendar-day:hover{
        height: 100%!important;
    }
    .el-calendar-table{
        height: 100%!important;
    }
    .el-scrollbar__view{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    }
    --el-calendar-selected-bg-color:#365e8b;
    --el-calendar-cell-width:100px;
}
.echartshea{
  .datedelog-header{
    margin: 5px 0px!important;
  }
}
.echaimg{
    position: relative;
    padding:10px 40px!important;
      .swiper-button-prev, .swiper-button-next{
      top: 40%!important;
    }
    }
</style>
<style lang="scss" scoped>
.datecount{
    text-align: start;
    &-one{
        display: block;
        margin:10px 0px;
    }
}
#echartline{
width: 100%;
height: 260px;
}
.imgswiper{
  padding: 10px 30px!important;
}
.circlecount{
    display: grid;
    grid-template-columns: repeat(21,4.7%);
}
.circlecounts{
    display: flex;
    flex-wrap: wrap;
}
.circle{
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 100%;
    margin:3px;
}
</style>