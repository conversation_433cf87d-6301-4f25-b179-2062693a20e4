<template>
  <!-- 设备 -->
  <div class="documentwaring padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"></Chamfering>
      <div class="ec-cell ai">
        <el-date-picker
              v-model="value1"
              class="bules"
              type="daterange"
              popper-class="bules"
              range-separator="-"
              ormat="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width:200px"
              size="small"
              @change="daterange"
            />
      <div class="ai-content">
          <div class="pie-box-1" id="AIecharts" ref="AIecharts"></div>
        <el-scrollbar  ref="myScrollbar" style="height:150px;width:60%">
            <div class="counts-one cursor" v-for="(item,index) in allData" :key="index" @click="open(item)">
                <div class="icons" :style="`background:${WorkTypecolor[index]}`"></div>
                <span>{{item.name}}</span>
                <span>{{item.value1}}</span>
                <span>{{item.value2}}</span>
            </div>
        </el-scrollbar>
      </div>
        <delog ref="delogss"></delog>
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    
  </div>
</template>

<script>
import { ref ,onMounted,getCurrentInstance} from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/personnelcon/content/delog.vue";

export default {
props:['homeindex'],
components:{
  Chamfering,
  delog
},
setup(){
  const $formatDateTime = getCurrentInstance().appContext.config.globalProperties.$formatDateTime

 let WorkTypecolor=[
			"#407fff",'#1F9DF5','#21F5D6','#5c2223','#eea2a4',
			"#a682e6",'#b598a1','#c08eaf','#813c85','#806d9e',
			"#e15d68",'#5e616d','#3170a7','#8fb2c9','#c3d7df',
			"#f29961",'#12a182','#737c7b','#92b3a5','#1a6840',
			"#00cccd",'#bec936','#373834','#5bae23','#e4bf11',
			"#dedede",'#b78d12','#f0d695','#b4a992','#fa5d19',
			"#FE8463",'#de7622','#f1908c','#207f4c','#22a2c3',
			"#9BCA63",'#815c94','#e16c96','#12a182','#bec936',
			'#D7504B', '#C6E579', '#F4E001', '#F0805A', '#26C0C0',
			'#FFB7DD', '#660077', '#FFCCCC', '#FFC8B4', '#550088',
			'#FFFFBB', '#FFAA33', '#99FFFF', '#CC00CC', '#FF77FF',
			'#C63300', '#9955FF', '#66FF66', '#129393', '#395203',
			'#C1232B', '#B5C334', '#FCCE10', '#E87C25', '#27727B',
			'#FAD860', '#F3A43B', '#60C0DD', '#0D7CAA'
		]
  let bgcolor=ref({})
  let getform=ref({
      ProjectCode:store.getters.code,
      Begtime:'',
      Endtime:''
    })
  let form=ref({})
  let topforms=ref({
      url:require('@/assets/img/personnelcon/documentwaring.png'),
      name:"AI抓拍统计"
    })
  let allData=ref([])
  let value1=ref([])
  let Total=ref(0)
  let delogss=ref(null)
  window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
   onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      let date=new Date()
      let date1=new Date()
      date.setTime(date.getTime() - (24 * 60 * 60 * 1000 * 30))
      // console.log('调用',getCurrentInstance().appContext.config.globalProperties);
      
      getform.value.Begtime=$formatDateTime(date,'yyyy-MM-dd')
      getform.value.Endtime=$formatDateTime(date1,'yyyy-MM-dd')
      // console.log('日期',getform.value.Begtime,getform.value.Endtime);
      value1.value=[getform.value.Begtime,getform.value.Endtime]
      geteqment()
   })
   const geteqment=async()=>{

    const {data:res}=await gettable('GetAIStatisticsEcharts',getform.value)
    // console.log('获取',res);
    if (res.code=="1000") {
          allData.value=res.data.Echarts
          Total.value=res.data.Total
        // this.$nextTick(()=>{
          getecharts()
        // })
        }
    // form.value=res.data
   }
  const daterange=(val)=>{
    if (val) {
      getform.value.Begtime=val[0]
      getform.value.Endtime=val[1]
    }
    geteqment()
  }
  const open=(val)=>{
    console.log('打开',val);
    let GUID={
      name:'AI抓拍'
    }
    delogss.value.showdelog(GUID,val,'')
  }
  const getecharts=async()=>{
        var echarts = require('echarts');
        let myChart = echarts.getInstanceByDom(document.getElementById('AIecharts'));
        if (myChart == null) {
            myChart = echarts.init(document.getElementById('AIecharts'));
        }
        let serieslist=[]
        // if (allData.value.length>0) {
            
       serieslist= allData.value?.map((item,index)=>{
            return{
                name:item.name,
                value:item.value2
            }
        })
        // }

        let option = {
            title: {
                top: '45%',
                left: 'center',
                text:'总数量',
                textStyle: {
                    color: '#fff',
                    fontStyle: 'normal',
                    fontWeight: 'normal',
                    fontSize: 14
                },
                subtext: Total.value,
                subtextStyle: {
                    color: '#fff',
                    fontSize: 12
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    // console.log(params)
                    if (params.seriesType=='liquidFill') {
                        return ''
                    }else{
                        return `<span style=\"display:inline-block;margin-right:5px;
                        border-radius:10px;width:10px;height:10px;background-color:${params.color};\"></span>${params.name}${params.value}`
                    }
                    
                }
            },
            series: [{
                    type: 'liquidFill',
                    itemStyle: {
                        normal: {
                            opacity: 0.4,
                            shadowBlur: 0,
                            shadowColor: 'blue'
                        }
                    },
                    name: '总数',
                    data: [{
                        value: 0.6,
                        itemStyle: {
                            normal: {
                                color: '#53d5ff',
                                opacity: 0.6
                            }
                        }
                    }],
                  //  background: '#fff',
                    color: ['#53d5ff'],
                    center: ['50%', '50%'],
                  backgroundStyle: {
                        color: '#001C4E'
                    },
                    label: {
                        show:false,
                        normal: {
                            formatter: '',
                            textStyle: {
                                fontSize: 12
                            }
                        }
                    },
                    outline: {
                        itemStyle: {
                            borderColor: '#86c5ff',
                            borderWidth: 0
                        },
                        borderDistance: 0
                    }
                },
                {
                    type: 'pie',
                    radius: ['70%', '90%'],
                    color:WorkTypecolor,
                    hoverAnimation: false, ////设置饼图默认的展开样式
                    label: {
                        show: false,
                        
                        normal: {
                            formatter: '{b}\n{d}%',
                            show: false,
                            position: ''
                        },
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },

                    itemStyle: { // 此配置
                        // normal: {
                        //     borderWidth: 2,
                        //     borderColor: '#fff',
                        // },
                        emphasis: {
                            borderWidth: 0,
                            shadowBlur: 2,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    data: serieslist
                }
            ]
        }
    myChart.setOption(option);
		window.addEventListener("resize", function() {
		myChart.resize();
	});

    }
  return{
  // list,
  form,
  bgcolor,
  topforms,
  getform,
  WorkTypecolor,
  allData,
  value1,
  Total,
  delogss,

  getecharts,
  geteqment,
  daterange,
  open
  }
}
}
</script>
<style lang="scss">
.ai{
    .el-date-editor.el-input, .el-date-editor.el-input__inner{
        width: 220px!important;
    }
    .el-input__inner{
        background-color:transparent!important;
        color:#fff!important;
        border:transparent!important;
    }

    .el-date-editor{
      --el-input-border-color:transparent!important;
    }
    .el-date-editor .el-range-input{
        width: 52%!important;
        color: #fff!important;
        background: transparent!important;
    }
    .el-range-separator{
        color: #fff!important;
    }
    .el-date-editor .el-range__icon{
        display: none!important;
    }
}
</style>
<style lang="scss" scoped>
.el-date-editor{
  // grid-row: 1!important;
  // grid-column: 1/span 2!important;
  display: block;
}
:deep(.bules){
  // .el-date-editor{
    // --el-input-border-color:transparent!important;
    background: transparent!important;
    .el-date-table td.in-range .el-date-table-cell {
      background-color: #174b99!important;
}
  // }
}
.ai{
  &-content{
    display: flex;
  }
  .icons{
    width: 10px;
    height: 10px;
    border-radius: 100%;
  }
  .counts{
    width: 100%;
    &-one{
        display: grid;
        grid-template-columns: 5% 57% 20% 18%;
        align-items: center;
        margin: 5px;
        font-size: 12px;
    }
}
}
.documentwaring{
  .ai{
    margin-top: 10px;
    // display: grid;
    font-size: 12px;
    // grid-template-columns: repeat(2,50%);
  }
  .pie-box-1{
    width: 150px;
    height: 150px;
  }
  .count{
    font-size: 30px;
    margin: 10px;
  }
  .offline{
    color: red;
  }
}
</style>