<template>
  <div class="Productstatistics">
    <div id="Productstatistics1" class="echarts"></div>
    <swiper class="swiperdp" 
         :slides-per-view="5"
         :loop="true" 
         :loopedSlides="8"
         :direction="'vertical'" 
         @swiper="onSwiper"
         :autoplay="{
           delay: 1500,
           disableOnInteraction: false
         }" 
         :modules="modules" 
         :speed="800"
         :style="{ height: swiperHeight + 'px' }">
            <swiper-slide v-for="(row, index) in form.GoodsTabList" :key="index" class="product-slide">
                <div class="swiper-slide-content">
                    <div class="icons" :style="{background:$colorlist()[index]}"></div>
                    <span>{{row.GoodsName}}</span>
                </div>
                <span>{{row.GoodsRate}}</span>
                <span>{{row.GoodsNum}}</span>
            </swiper-slide>
    </swiper>
  </div>
</template>

<script setup>
import { onMounted,ref,getCurrentInstance, nextTick, onBeforeUnmount } from 'vue';
const $colorlist = getCurrentInstance().appContext.config.globalProperties.$colorlist
import { gettable, setdata, deldata } from "@/network/api/requestnet";
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import { useStore } from 'vuex'
const store = useStore()

let myChart
let total=ref(0)
let modules=[Autoplay, Pagination, Navigation]
const getform=ref({
    ProjectCode:store.getters?.code,
})

// 添加swiper高度的响应式变量
const swiperHeight = ref(200); // 默认高度

const showdelog=()=>{
    getscore();
    // 添加全屏监听
    addFullScreenListeners();
    // 初始计算高度
    checkFullScreen();
}
// 检测是否全屏
const checkFullScreen = () => {
    const isFullScreen = document.fullscreenElement || 
                        document.webkitFullscreenElement || 
                        document.mozFullScreenElement || 
                        document.msFullscreenElement;
    
    // 根据全屏状态更新swiper高度
    if (isFullScreen) {
        // 全屏状态下设置更大的高度，例如屏幕高度的30%
        const screenHeight = window.innerHeight;
        swiperHeight.value = Math.floor(screenHeight * 0.3);
    } else {
        // 非全屏状态下使用默认高度
        swiperHeight.value = 200;
    }
    
    // 更新图表大小
    nextTick(() => {
        myChart?.resize();
    });
};

// 添加全屏变化事件监听
const addFullScreenListeners = () => {
    document.addEventListener('fullscreenchange', checkFullScreen);
    document.addEventListener('webkitfullscreenchange', checkFullScreen);
    document.addEventListener('mozfullscreenchange', checkFullScreen);
    document.addEventListener('MSFullscreenChange', checkFullScreen);
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', checkFullScreen);
};

// 移除事件监听
const removeFullScreenListeners = () => {
    document.removeEventListener('fullscreenchange', checkFullScreen);
    document.removeEventListener('webkitfullscreenchange', checkFullScreen);
    document.removeEventListener('mozfullscreenchange', checkFullScreen);
    document.removeEventListener('MSFullscreenChange', checkFullScreen);
    
    window.removeEventListener('resize', checkFullScreen);
};

let serieslist=ref([])
let form=ref({
    GoodsTabList: []
})

onMounted(()=>{
    // console.log('获取',store.getters?.code);
    let code= window.location.href?.split('?code=')[1]

  getform.value.ProjectCode= code
    getscore();
    // 添加全屏监听
    addFullScreenListeners();
    // 初始计算高度
    checkFullScreen();
})

onBeforeUnmount(() => {
    // 组件销毁前移除监听器
    removeFullScreenListeners();
})

// const showdelog=(val)=>{
//     getscore()
// }
// 获取销售积分
const getscore=async()=>{
    const {data:res}=await gettable('GetJFGoodsStatistics',getform.value)
    // console.log('获取销售积分',res);
    if(res.code=='1000'){
        total.value=res.data.GoodsTotal
        serieslist.value=res.data.GoodsEcharts
        
        // 确保GoodsTabList存在且至少有足够项目以保证无缝滚动效果
        if (res.data.GoodsTabList && res.data.GoodsTabList.length > 0) {
            // 计算需要复制的次数，确保足够数量支持无缝滚动
            const originalList = [...res.data.GoodsTabList];
            // 由于slides-per-view为5，确保至少有2倍以上的幻灯片数量
            const minSlides = 5 * 3; // 至少是slidesPerView的3倍
            const repeatTimes = Math.ceil(minSlides / originalList.length);
            
            // 创建新数组
            let newList = [];
            for (let i = 0; i < repeatTimes; i++) {
                newList = [...newList, ...originalList];
            }
            
            form.value = {
                ...res.data,
                GoodsTabList: newList
            };
            // console.log('处理后的GoodsTabList长度:', form.value.GoodsTabList.length);
        } else {
            form.value = {
                ...res.data,
                GoodsTabList: Array(15).fill({ GoodsName: '暂无数据' }) // 如果没有数据，创建默认项
            };
        }
        
        nextTick(()=>{
            getecahrts()
        })
    }   
}
const onSwiper = (swiper) => {
    // console.log('初始化Swiper，启动自动滚动');
    
    // 确保自动播放启动
    // setTimeout(() => {
    //     swiper.autoplay.start();
    // }, 100);
    
    // 监听自动播放状态
    swiper.on('autoplayStart', () => {
        // console.log('自动播放已启动');
    });
    
    swiper.on('autoplayStop', () => {
        // console.log('自动播放已停止，尝试重新启动');
        swiper.autoplay.start();
    });
};
const getecahrts=()=>{
        let echarts = require('echarts');
        myChart = echarts.getInstanceByDom(document.getElementById('Productstatistics1'))
         if (myChart == null) {
            myChart = echarts.init(document.getElementById('Productstatistics1'));
         }

    let option = {
    title: {
        top: '45%',
        left: 'center',
        text:'总数量',
        textStyle: {
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontSize: 14
        },
        subtext:total.value,
        subtextStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: 'normal',

        }
    },
    tooltip: {
        trigger: 'item',
        formatter: function(params) {
            // console.log(params)
            if (params.seriesType=='liquidFill') {
                return ''
            }else{
                return `<span style=\"display:inline-block;margin-right:5px;
                border-radius:10px;width:10px;height:10px;background-color:${params.color};\"></span>${params.name}${params.value}`
            }
            
        }
            },
            series: [
                {
                    type: 'liquidFill',
                    itemStyle: {
                        normal: {
                            opacity: 0.4,
                            shadowBlur: 0,
                            shadowColor: 'blue'
                        }
                    },
                    name: '总数',
                    data: [{
                        value: 0.6,
                        itemStyle: {
                            normal: {
                                color: '#53d5ff',
                                opacity: 0.6
                            }
                        }
                    }],
                //  background: '#fff',
                    color: ['#53d5ff'],
                    center: ['50%', '50%'],
                    backgroundStyle: {
                        color: '#001C4E'
                    },
                    label: {
                        show:false,
                        normal: {
                            formatter: '',
                            textStyle: {
                                fontSize: 14
                            }
                        }
                    },
                    outline: {
                        itemStyle: {
                            borderColor: '#86c5ff',
                            borderWidth: 0
                        },
                        borderDistance: 0
                    }
                },
                {
                    type: 'pie',
                    radius: ['70%', '90%'],
                    color: $colorlist(),
                    hoverAnimation: false, ////设置饼图默认的展开样式
                    label: {
                        show: false,
                        
                        normal: {
                            formatter: '{b}\n{d}%',
                            show: false,
                            position: ''
                        },
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },

                    itemStyle: { // 此配置
                        // normal: {
                        //     borderWidth: 2,
                        //     borderColor: '#fff',
                        // },
                        emphasis: {
                            borderWidth: 0,
                            shadowBlur: 2,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    data: serieslist.value
                }
            ]
        }

        myChart?.setOption(option)

        window.addEventListener("resize", function() {
          myChart?.resize();
        });
    }
defineExpose({
    showdelog
})
</script>
<style lang="scss" scoped>
.Productstatistics{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}
.echarts{
    width: 100%;
    height: 50%;
}
.swiperdp{
    margin-top: 5px;
    overflow: hidden;
    /* 移除固定高度，使用动态计算的高度 */
    transition: height 0.3s ease; /* 添加过渡效果使高度变化更平滑 */
}
.swiper-slide-content{
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
:deep(.swiper-slide) {
    display: grid;
    grid-template-columns: repeat(3,33.3%);
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    align-items: center;
    padding: 0 5px;
    font-size: 14px;
}
.icons{
    width: 10px;
    height: 10px;
    // background-color: #fff;
    border-radius: 100%;
    margin-right: 5px;
}
:deep(.swiper) {
    width: 100%;
    position: relative;
}
:deep(.swiper-wrapper) {
    transition-timing-function: ease;
}
:deep(.product-slide) {
    transition: transform 0.3s ease;
}
</style>