<template>
  <div class="concer">
    <div class="concer-home-btn">
        <div v-for="(item,index) in btnliststop" :key="index" :class="['technology-btn cursor',{'changindex':falge2==index}]" 
        :style="[falge2==index?`background:${bgcolor.changcolor};color:#FFF`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
         rgba(7, 93, 184, 0.6) 100%);color:#FFF`]" @click="change1(item,index)"
        >{{item.name}}</div>
    </div>
    
    <!-- <div v-if="falge2==0"> -->
        <!-- <i class="iconfont icon-fangda cursor" v-if="amplifyindex==0" @click="amplifyopen(1)"
        :style="{color:bgcolor.titlecolor,left:amplifyindex==0?'20%':'2%'}" title="放大"></i>
        <i class="iconfont icon-suoxiao cursor" v-else-if="amplifyindex==1" @click="amplifyopen(0)"
        :style="{color:bgcolor.titlecolor,left:amplifyindex==0?'20%':'2%'}" title="缩小"></i> -->
        <img v-for="(item,index) in imglist" @mouseenter="mouseenter(item,index)" @mouseleave="mouseleave(item,index)"
        :key="index" class="imgpoting cursor" v-show="amplifyindex==0?item.XPosition>'20'&&item.XPosition<'80':item" :src="falge==index?item.src1:item.src" 
        :style="`top:${item.YPosition}%;left:${item.XPosition}%`" alt="" srcset="" @click="open(item)">
        
    <!-- </div> -->
        <sharedelog  ref="Foundations"></sharedelog>
    
    <el-dialog v-model="dialogTableVisible" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]"  destroy-on-close
    class="construction" :width="showfalge==0?'30%':'60%'" >
    <div class="heaereq-one">
        <div class="toplist" >
            <div :class="['heaereq cursor']"
            :style="[`background:rgba(${bgcolor.delogcolor},0.35)`]">
                <div class="icontop1 bor" :style="[`border:1px solid ${bgcolor.titlecolor}`]" ></div>
                <div class="icontop" :style="[`border:1px solid ${bgcolor.titlecolor}`]"></div>
                <div class="icontop2 bor" :style="[`border:1px solid ${bgcolor.titlecolor}`]"></div>
            </div>
        </div>
        <div class="closedelog cursor" :style="[`background: radial-gradient(50% 50% at 50% 50%,
         rgba(3, 251, 255, 0.17) 0%, ${bgcolor.hovercor} 100%);left:${showfalge==0?'97%':'99%'}`]" @click="close()">
            <el-icon class="closeicon"><CloseBold /></el-icon>
        </div>
    </div>
    <div class="construction-body">
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
            <div class="datedelog-body-one">
                <img src="@/assets/img/home/<USER>" alt="">
                <p class="datedelog-p">{{titles}}{{showfalge==1?'历史记录数据':''}}</p>
            </div>
            <p v-if="showfalge==0" class="datedelog-p cursor" @click="hisoe()">历史记录</p>
            <el-date-picker
            v-if="showfalge==1"
                v-model="getform.Date"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择日期"
                @change="changedate"
            />
        </div>
        <div v-if="showfalge==0">
            <div v-if="titles=='智能水表'||titles=='智能电表'" class="lables" :style="`color:${bgcolor.font}`">
                <p v-for="(item,index) in lables" :key="index">{{item.name}}：{{delogform[item.value]}}</p>
            </div>
            <div v-if="titles=='扬尘在线监控设备'||titles=='标养室'" class="lables" :style="`color:${bgcolor.font}`">
                <p v-for="(item,index) in maintenance" :key="index" v-show="titles=='扬尘在线监控设备'?index<4:(index<2||index>3)">{{item.name}}：{{delogform[item.value]}}</p>
                <div v-if="titles=='扬尘在线监控设备'" class="lables-one"></div>
                <div v-else-if="titles=='标养室'" class="bynane">
                    <el-table :data="gridData1" class="tableleft"  :style="['width: 99%;height:100%;',`color:${bgcolor.font};
                    --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
                    :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
                    empty-text="暂无数据" :loading="loading" max-height="200px" 
                    >
                        <el-table-column  prop="date" align="center" label="监测点"></el-table-column>
                        <el-table-column  prop="date" align="center"  label="温度（℃）" ></el-table-column>
                        <el-table-column  prop="date" align="center"  label="湿度（%Rh）" ></el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <div v-else-if="showfalge==1">
            <div class="construction-body-content">
                <div v-for="(item,index) in btnlist1" :key="index" :class="['monitor-two-btn cursor',{'changindex':falge1==index}]" 
                :style="[falge1==index?`background:${bgcolor.changcolor};color:#FFF`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
                rgba(7, 93, 184, 0.6) 100%);color:#FFF`]" @click="change(item,index)"
                >{{item}}</div>
            </div>
            <div class="construction-body-two">
            <p class="datedelog-p realtime" v-for="(item,index) in ectitleyc" :key="index">{{item.name}} {{titles=='扬尘在线监控设备'?delogtotle+' '+item.value:item.value}}</p>

            </div>
            <div class="" id="echartsdom">
                <Bieechart :ids="'echartsdom'" :options1="options"></Bieechart>
            </div>
        </div>
    </div>

    </el-dialog>
  </div>
</template>

<script>
import { computed, nextTick, onMounted, reactive, ref,watch } from 'vue'
import Bieechart from "@/components/connet/Common/echartscom.vue";
import calendar from "@/components/connet/construction/content/calendar.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import { formatDateTime } from "@/utils/date";
import sharedelog from "./sharedelog.vue";
export default {
components:{
        Bieechart,
        calendar,
        sharedelog
    },
setup(props,ctx){
    let imglist=ref([])

    let getform=ref({
        ProjectCode:store.getters.code,
        UsingPage:'绿色施工',
        IconType:'',
        DetialType:'',
        EquipCode:'',
        Date:'',
        Type:'TSP',
        page:1,
        count:10
    })
    let falge=ref(-1)
    let falge1=ref(0)
    let falge2=ref(-1)
    // let sitefalg=ref(0)
    let bgcolor=ref({})
    let btnlist1=ref([])
    let showfalge=ref(-1)//更多
    let dialogTableVisible=ref(false)
    let amplifyindex=ref(0)
    let delogform=ref({})
    let delogtotle=ref('')
    let loading=ref(false)
    let titles=ref('')
    let value1=ref('')
    let ectitleyc=ref([])
    let Foundations=ref(null)
    // let size=ref
    let lables=[{
        name:'设备编号',
        value:'WatermeterCode'
    },{
        name:'设备使用区域',
        value:'WatermeterRegion'
    },{
        name:'数据时间',
        value:'RealTime'
    },{
        name:'实时读数',
        value:'RealData'
    },{
        name:'初始读数',
        value:'FirstData'
    },{
        name:'今日耗能',
        value:'TodayPower'
    },

    ]
    let maintenance=[
        {
        name:'设备名称',
        value:'EquipName'
        },
        {
        name:'设备型号',
        value:'EquipModel'
        },{
        name:'设备编号',
        value:'MN'
        },{
        name:'设备状态',
        value:'EquipState'
        },{
        name:'数据时间',
        value:''
        }
    ]
    
    let gridData1=ref([])
    let options=ref({})
    let btnlist=['TSP','PM 2.5','PM 10','温度','湿度','大气压','风速','风向','噪声']
    let btnliststop=[{
        name:'深基坑监测',
        value:''
        },{
        name:'高支模监测',
        value:''
        },{
        name:'标养室监测',
        value:''   
        },{
        name:'绿色工地',
        value:''   
        }
        ]
    window.addEventListener('setthcolor', ()=> {
            // console.log('导航');
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        getlocation()
    })
    const getlocation=async()=>{
        let loction=[]
        const {data:res}=await gettable('GetHomePageIconPosition',getform.value)
        if (res.code=="1000") {
            // console.log('返回',res);
            
            loction=res.data
            loction.map((item,index)=>{
                // console.log('标养室',item.IconType);
                // item.ss="标养室"
                switch (item.IconType) {
                    case '标养室':
                        item.src=require('@/assets/img/construction/maintenance.png')
                        item.src1=require('@/assets/img/construction/maintenance1.png')

                        break;
                    case '智能水表':
                        // console.log('获取11',item);
                        item.src=require('@/assets/img/construction/Smartwaters.png')
                        item.src1=require('@/assets/img/construction/Smartwaters1.png')

                        break;
                    case '智能电表':
                        item.src=require('@/assets/img/construction/electricityicon.png')
                        item.src1=require('@/assets/img/construction/electricityicon1.png')

                        break;
                    case '扬尘在线监控设备':
                        item.src=require('@/assets/img/construction/Fugitive.png')
                        item.src1=require('@/assets/img/construction/Fugitive1.png')

                        break;
                }
                // item.
            });
            // console.log('获取',loction);
            
            imglist.value=loction
        }
    }
    const getdetil=async(val)=>{
        let GUID={
            IconType:val.IconType,
            EquipCode:val.EquipCode,
            ProjectCode:store.getters.code
        }
        let date =new Date()
        // formatDateTime()
        getform.value.Date=formatDateTime(date, 'yyyy-MM-dd')
        // showfalge.value=0
        getform.value.IconType=val.IconType
        getform.value.EquipCode=val.EquipCode

        const {data:res}=await gettable('GetGreenConstructionInfo',GUID)
        // console.log('获取小弹窗',res);
        if (res.code=="1000") {
            delogform.value=res.data
        }else{
            delogform.value={}
        }
    }
    const gethistory=async(val)=>{
        let series
        let length
        const {data:res}=await gettable('GetHistoryGCdetailInfo',getform.value)
        // console.log('获取',res);
        if (res.code=="1000") {

            switch (getform.value.IconType) {
                case '扬尘在线监控设备':
                getecharts(res.data.Echart)
                ectitleyc.value[0].value=res.data.RealData
                    break;
                case '智能电表':
                case '智能水表':
                    // console.log('获取只能水表');
                    
                ectitleyc.value[0].value=res.data.FirstData
                ectitleyc.value[1].value=res.data.EndData

                let namlist=res.data.Echart.map((item,index)=>{
                    return item.name
                })
                series=[
                    {
                    name: getform.value.IconType,
                    type: 'line',
                    stack: 'Total',
                    data: res.data.Echart
                    }]
              length={
                    show:false,
                    textStyle:{
                        color:'#fff'
                    },
                    data: [getform.value.IconType]
                }
                // console.log('打印',series,length,namlist);
                
                getother(series,length,namlist)
                    break;
            }
        }
    }
    const mouseenter=(e,index)=>{
        falge.value=index
    }
    const mouseleave=(e,index)=>{
        falge.value=-1
    }
    const close=()=>{
        dialogTableVisible.value=false
    }
    const open=(item)=>{
        showfalge.value=0
        titles.value=item.IconType
        getdetil(item)
        dialogTableVisible.value=true
    }
    const change1=(val,index)=>{
        falge2.value=index
        nextTick(()=>{
        // console.log('点击切换',val.value.value.showdelog());
        // val.value.value.showdelog(val,index)
        Foundations.value.showdelog(val,index)
        })
    }
    const changedate=(val)=>{
        // console.log('获取日期',val);
        if (val) {
        getform.value.Date=val
        gethistory()
        
        }
    }
    const hisoe=()=>{
        // console.log('查询历史数据',titles.value);
        
        showfalge.value=1
        // nextTick(()=>{
        let visualMap
        let series
        let length
        switch (titles.value) {
            case '扬尘在线监控设备':
                // console.log('胡斌');
              btnlist1.value=btnlist
              ectitleyc.value=[{
                name:'实时',
                value:''
              }]
            gethistory()

                break;
            case '智能电表':
            case '智能水表':

            // console.log('获取');
            
              ectitleyc.value=[{
                name:'初始读数',
                value:''
              },
              {
                name:'最终读数',
                value:''
              },
              ]
            gethistory()

                break;
            case '标养室':
              ectitleyc.value=[{
                name:'实时温度',
                value:''
              },
              {
                name:'实时湿度',
                value:''
              },
              ]
              btnlist1.value=['监测点1','监测点1','监测点1']
              series=[
                {
                name: 'Email',
                type: 'line',
                stack: 'Total',
                data: [120, 132, 101, 134, 90, 230, 210]
                },
                {
                name: 'Union Ads',
                type: 'line',
                stack: 'Total',
                data: [220, 182, 191, 234, 290, 330, 310]
                },]
              length={
                textStyle:{
                    color:'#fff'
                    },
                data: ['Email', 'Union Ads']
                }
            getother(series,length)

                break;
        }
        // console.log('显示',ectitleyc.value);
        
        // })
    }
    const change=(val,index)=>{
        // console.log('触发数据',val);
        
        falge1.value=index
        delogtotle.value=val
        getform.value.Type=val
        
        // nextTick(()=>{
        gethistory()

        // })
        // getdetil(val)
    }
    const getother=(val,lengths,namelsit)=>{
        options.value = {
        color:['#73c0de'],
        legend: lengths,
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '5%',
        right: '12%',
        bottom: '10%'
      },
      xAxis: {
        axisLine:{
            // show: true,
            lineStyle:{
            color:'#fff'
            }
         },
        axisLabel: {
            show: true,
            textStyle: {
                color: "#fff" //X轴文字颜色
            },
        },
        data:namelsit
      },
      yAxis: {
         splitLine :{    //网格线
              lineStyle:{
                  type:'dashed',
                  color:'rgba(3, 251, 255, 0.3)'   //设置网格线类型 dotted：虚线   solid:实线
              },
              show:true //隐藏或显示
          },
        axisLabel: {
            show: true,
            textStyle: {
                color: "#fff" //X轴文字颜色
            },
        },
      },

      series:val
        }
    }
    const getecharts=(val)=>{
            // console.log('huoq',values);
        let date=[]
        let name=[]
        if (val.length>0) {
            date=val
            name=val.map((item,index)=>{
                return item.name
            })
        }

      options.value = {
        color:['#73c0de','#3ba272'],
        // legend: lengths,
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '5%',
        right: '12%',
        bottom: '10%'
      },
      xAxis: {
        axisLine:{
            // show: true,
            lineStyle:{
            color:'#fff'
            }
         },
        axisLabel: {
            show: true,
            textStyle: {
                color: "#fff" //X轴文字颜色
            },
        },
        data:name
      },
      yAxis: {
         splitLine :{    //网格线
              lineStyle:{
                  type:'dashed',
                  color:'rgba(3, 251, 255, 0.3)'   //设置网格线类型 dotted：虚线   solid:实线
              },
              show:true //隐藏或显示
          },
        axisLabel: {
            show: true,
            textStyle: {
                color: "#fff" //X轴文字颜色
            },
        },
      },
      visualMap:{
            top: 50,
            right: 10,
            pieces: [
            {
                gt: 0,
                lte: 300,
                color: '#00A74A'
            },
            {
                gt: 300,
                lte: 800,
                color: '#FAE503'
            },
            {
                gt: 800,
                lte: 10000,
                color: '#AA0000'
            }
            ],
            outOfRange: {
            color: '#999'
            }
      },
      series:{
                name: '数据来源',
                type: 'line',
                data: date,
                markLine: {
                silent: true,
                lineStyle: {
                    color: '#333',
                },
                data: [
                    {
                    yAxis: 300
                    },
                    {
                    yAxis: 800
                    }
                ]
                }
        }
    }
    }

    const amplifyopen=(val)=>{
        amplifyindex.value=val
        ctx.emit("getamplify1", val);
    }
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
    return{
        dialogTableVisible,
        amplifyindex,
        bgcolor,
        showfalge,
        imglist,
        falge,
        falge1,
        falge2,
        Foundations,
        // sitefalg,
        titles,
        lables,
        gridData1,
        loading,
        maintenance,
        value1,
        btnlist,
        delogtotle,
        getform,
        options,
        ectitleyc,
        btnlist1,
        btnliststop,
        delogform,
        // changref,

        // background,
        mouseenter,
        mouseleave,
        open,
        amplifyopen,
        close,
        tableRowClassName,
        hisoe,
        change,
        getecharts,
        getother,
        change1,
        getlocation,
        getdetil,
        gethistory,
        changedate
    }
}
}
</script>
<style lang="scss">
.construction{
    // margin-top: 2%!important;
    // background: rgba(2, 193, 253, 0.24)!important;
    opacity: 1;
    box-sizing: border-box!important;
    .el-dialog__header{
        display: none!important;
    }
    .el-dialog__body{
        padding: 10px!important;
    }
    // .workerpopove{
    .el-table{
            --el-table-tr-bg-color:transparent!important;
            // border:1px solid transparent!important;
            // --el-table-border-color:transparent;
            --el-table-row-hover-bg-color:transparent;
            --el-table-bg-color:transparent;
        }
    .el-table .warning-row {
        //   --el-table-tr-bg-color: #000 !important;
        background: rgba(15, 43, 63, 0.6)!important;
        }

// }
    
}
</style>
<style lang="scss" scoped>
.concer{
    width: 100%;
    height: 100%;
    &-home{
        &-btn{
            display: flex;
            align-items: center;
            justify-content: center;
            justify-content: space-evenly;
        }
    }
    .technology-btn{
        position: relative;
        font-weight: bold;
        // justify-content: space-evenly;
        padding: 10px;
        margin: 10px;
    }
}
.realtime{
    color: #fff;
    padding: 10px;
    text-align: start;
    font-size: 18px;
    padding-right: 10%;
    // display: inline-block;
}
.iconfont{
    font-size: 45px!important;
    position: absolute;
    left: 2%;
}
.imgpoting{
    position: absolute;
    width: 50px;
    height: 70px;
}
.tower:hover{
    background-image: url('@/assets/img/home/<USER>/tower1.png');
    width: 65px;
    height: 75px;
}
.Similarity{
    width: 100%;
    height: 100%;
}
.changindex::before{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        left: -2px;
        top: -2px; 
        opacity: 1;
        border-top: 2px solid #E0A538;
        border-left: 2px solid #E0A538;
    }
.changindex::after{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        right: -2px;
        bottom: -2px; 
        opacity: 1;
        border-bottom: 2px solid #E0A538;
        border-right: 2px solid #E0A538;
    }
.Similaritysum{
    width: 100%;
    height: 100%;
    position: relative;
    &-p{
        position: absolute;
        bottom: 20%;
        left: 38%;
    font-size: 20px;
    font-weight: bold;
    }
}
#echartsdom{
    width: 100%;
    height: 300px;
}
.lables{
    display: grid;
    grid-template-columns: repeat(2,50%);
    p{
        padding: 10px;
        text-align: start;
    }
    &-one{
        width: 300px;
        height: 160px;
        border:1px solid #000;
        grid-row:3;
        margin-left: 40%;
    }
    .bynane{
    grid-area: 3/span 2/span 2;

    }
}
// home
.construction{
    &-body{
        &-content{
            display: grid;
            grid-template-columns: repeat(9,11%);
        }
        &-two{
            display: flex;
            // justify-content: space-around;
        }
    }
    .monitor-two-btn{
        margin: 5px;
        padding: 10px;
    }
    

}
.count{
        height: 100%;
    }
</style>