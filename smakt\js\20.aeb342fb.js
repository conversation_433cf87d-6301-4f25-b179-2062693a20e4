(self["webpackChunksmart"]=self["webpackChunksmart"]||[]).push([[20],{15063:function(e,t,a){"use strict";a.d(t,{Z:function(){return b}});var l=a(73396),i=a(87139);const o=["align"],n=["innerHTML"],s=["align","onClick","onMouseenter"],r=["innerHTML"];function c(e,t,a,c,d,u){return(0,l.wg)(),(0,l.iD)("div",{class:"dv-scroll-board",ref:d.ref},[d.header.length&&d.mergedConfig?((0,l.wg)(),(0,l.iD)("div",{key:0,class:"header",style:(0,i.j5)(`background-color: ${d.mergedConfig.headerBGC};`)},[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(d.header,((t,a)=>((0,l.wg)(),(0,l.iD)("div",{class:"header-item",key:`${t}${a}`,style:(0,i.j5)(`\n          height: ${d.mergedConfig.headerHeight}px;\n          line-height: ${d.mergedConfig.headerHeight}px;\n          width: ${d.widths[a]}px;\n        `),align:d.aligns[a]},[(0,l.WI)(e.$slots,`header-${a}`,{header:t},(()=>[(0,l._)("span",{innerHTML:t},null,8,n)]))],12,o)))),128))],4)):(0,l.kq)("",!0),d.mergedConfig?((0,l.wg)(),(0,l.iD)("div",{key:1,class:"rows",style:(0,i.j5)(`height: ${e.height-(d.header.length?d.mergedConfig.headerHeight:0)}px;`)},[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(d.rows,((a,o)=>((0,l.wg)(),(0,l.iD)("div",{class:"row-item",key:`${a.toString()}${a.scroll}`,style:(0,i.j5)(`\n          height: ${d.heights[o]}px;\n          line-height: ${d.heights[o]}px;\n          background-color: ${d.mergedConfig[a.rowIndex%2===0?"evenRowBGC":"oddRowBGC"]};\n        `)},[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(a.ceils,((n,c)=>((0,l.wg)(),(0,l.iD)("div",{class:"ceil",key:`${n}${o}${c}`,style:(0,i.j5)(`width: ${d.widths[c]}px;`),align:d.aligns[c],onClick:e=>u.emitEvent("click",o,c,a,n),onMouseenter:e=>u.handleHover(!0,o,c,a,n),onMouseleave:t[0]||(t[0]=e=>u.handleHover(!1))},[(0,l.WI)(e.$slots,`cell-${c}`,{content:n,row:a.originalData,rowIndex:a.rowIndex,columnIndex:c},(()=>[(0,l.WI)(e.$slots,"cell",{content:n,row:a.originalData,rowIndex:a.rowIndex,columnIndex:c},(()=>[(0,l._)("span",{innerHTML:n},null,8,r)]))]))],44,s)))),128))],4)))),128))],4)):(0,l.kq)("",!0)],512)}a(30541),a(57658);function d(e,t){let a;return function(){clearTimeout(a);const[l,i]=[this,arguments];a=setTimeout((()=>{t.apply(l,i)}),e)}}function u(e,t){const a=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver,l=new a(t);return l.observe(e,{attributes:!0,attributeFilter:["style"],attributeOldValue:!0}),l}var h=a(15941),m={data(){return{dom:"",width:0,height:0,debounceInitWHFun:"",domObserver:""}},methods:{async autoResizeMixinInit(){const{initWH:e,getDebounceInitWHFun:t,bindDomResizeCallback:a,afterAutoResizeMixinInit:l}=this;await e(!1),t(),a(),"function"===typeof l&&l()},initWH(e=!0){const{$nextTick:t,$refs:a,ref:l,onResize:i}=this;return new Promise((o=>{t((t=>{const n=this.dom=a[l];this.width=n?n.clientWidth:0,this.height=n?n.clientHeight:0,n?this.width&&this.height||h.warn("DataV: Component width or height is 0px, rendering abnormality may occur!"):h.warn("DataV: Failed to get dom node, component rendering may be abnormal!"),"function"===typeof i&&e&&i(),o()}))}))},getDebounceInitWHFun(){const{initWH:e}=this;this.debounceInitWHFun=d(100,e)},bindDomResizeCallback(){const{dom:e,debounceInitWHFun:t}=this;this.domObserver=u(e,t),window.addEventListener("resize",t)},unbindDomResizeCallback(){let{domObserver:e,debounceInitWHFun:t}=this;e&&(e.disconnect(),e.takeRecords(),e=null,window.removeEventListener("resize",t))}},mounted(){const{autoResizeMixinInit:e}=this;e()},beforeDestroy(){const{unbindDomResizeCallback:e}=this;e()}},v=a(56904),g=a(19482),p=a(15941),w={name:"DvScrollBoard",mixins:[m],props:{config:{type:Object,default:()=>({})}},data(){return{ref:"scroll-board",defaultConfig:{header:[],data:[],list:[],rowNum:5,headerBGC:"#00BAFF",oddRowBGC:"#003B51",evenRowBGC:"#0A2732",waitTime:2e3,headerHeight:35,columnWidth:[],align:[],index:!1,indexHeader:"#",carousel:"single",hoverPause:!0},mergedConfig:null,header:[],rowsData:[],rows:[],widths:[],heights:[],avgHeight:0,aligns:[],animationIndex:0,animationHandler:"",updater:0,needCalc:!1}},watch:{config(){const{stopAnimation:e,calcData:t}=this;e(),this.animationIndex=0,t()}},methods:{handleHover(e,t,a,l,i){const{mergedConfig:o,emitEvent:n,stopAnimation:s,animation:r}=this;e&&n("mouseover",t,a,l,i),o.hoverPause&&(e?s():r(!0))},afterAutoResizeMixinInit(){const{calcData:e}=this;e()},onResize(){const{mergedConfig:e,calcWidths:t,calcHeights:a}=this;e&&(t(),a())},calcData(){const{mergeConfig:e,calcHeaderData:t,calcRowsData:a}=this;e(),t(),a();const{calcWidths:l,calcHeights:i,calcAligns:o}=this;l(),i(),o();const{animation:n}=this;n(!0)},mergeConfig(){let{config:e,defaultConfig:t}=this;this.mergedConfig=(0,v.deepMerge)((0,g.deepClone)(t,!0),e||{})},calcHeaderData(){let{header:e,index:t,indexHeader:a}=this.mergedConfig;e.length?(e=[...e],t&&e.unshift(a),this.header=e):this.header=[]},calcRowsData(){let{data:e,index:t,headerBGC:a,rowNum:l,list:i}=this.mergedConfig;Array.isArray(e)||(e=[]);this.mergedConfig.header;const o=e.map((e=>e?Array.isArray(e)?e:"object"===typeof e?i&&i.length>0?i.map((t=>e.hasOwnProperty(t)?e[t]:(p.warn(`Field ${t} not found in row data`),""))):Object.values(e):[e]:[]));t&&o.forEach(((e,t)=>{const l=`<span class="index" style="background-color: ${a};">${t+1}</span>`;e.unshift(l)}));const n=o.length;if(n>l&&n<2*l){const t=[...e];o.push(...o),e.push(...t)}const s=o.map(((t,a)=>({ceils:t,rowIndex:a,scroll:a,originalData:e[a]})));this.rowsData=s,this.rows=s},calcWidths(){const{width:e,mergedConfig:t,rowsData:a}=this,{columnWidth:l,header:i}=t,o=l.reduce(((e,t)=>e+t),0);let n=0;a[0]?n=a[0].ceils.length:i.length&&(n=i.length);const s=(e-o)/(n-l.length),r=new Array(n).fill(s);this.widths=(0,v.deepMerge)(r,l)},calcHeights(e=!1){const{height:t,mergedConfig:a,header:l}=this,{headerHeight:i,rowNum:o,data:n}=a;let s=t;l.length&&(s-=i);const r=s/o;this.avgHeight=r,e||(this.heights=new Array(n.length).fill(r))},calcAligns(){const{header:e,mergedConfig:t}=this,a=e.length;let l=new Array(a).fill("left");const{align:i}=t;this.aligns=(0,v.deepMerge)(l,i)},async animation(e=!1){const{needCalc:t,calcHeights:a,calcRowsData:l}=this;t&&(l(),a(),this.needCalc=!1);let{avgHeight:i,animationIndex:o,mergedConfig:n,rowsData:s,animation:r,updater:c}=this;const{waitTime:d,carousel:u,rowNum:h}=n,m=s.length;if(h>=m)return;if(e&&(await new Promise((e=>setTimeout(e,d))),c!==this.updater))return;const v="single"===u?1:h;let g=s.slice(o);if(g.push(...s.slice(0,o)),this.rows=g.slice(0,"page"===u?2*h:h+1),this.heights=new Array(m).fill(i),await new Promise((e=>setTimeout(e,300))),c!==this.updater)return;this.heights.splice(0,v,...new Array(v).fill(0)),o+=v;const p=o-m;p>=0&&(o=p),this.animationIndex=o,this.animationHandler=setTimeout(r,d-300)},stopAnimation(){const{animationHandler:e,updater:t}=this;this.updater=(t+1)%999999,e&&clearTimeout(e)},emitEvent(e,t,a,l,i){const{ceils:o,rowIndex:n}=l;this.$emit(e,{row:o,ceil:i,rowIndex:n,columnIndex:a})},updateRows(e,t){const{mergedConfig:a,animationHandler:l,animation:i}=this;this.mergedConfig={...a,data:[...e]},this.needCalc=!0,"number"===typeof t&&(this.animationIndex=t),l||i(!0)}},destroyed(){const{stopAnimation:e}=this;e()}},f=a(40089);const y=(0,f.Z)(w,[["render",c]]);var b=y},1020:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return Xe}});var l=a(73396),i=a(44870),o=a(20065),n=a(87139),s=a(57597),r=(a(30197),a(72559)),c=a(61008);const d=e=>((0,l.dD)("data-v-4492396c"),e=e(),(0,l.Cn)(),e),u={class:"Productstatistics"},h=d((()=>(0,l._)("div",{id:"Productstatistics1",class:"echarts"},null,-1))),m={class:"swiper-slide-content"};var v={__name:"Productstatistics",setup(e,{expose:t}){const d=(0,l.FN)().appContext.config.globalProperties.$colorlist,v=(0,o.oR)();let g,p=(0,i.iH)(0),w=[c.pt,c.tl,c.W_];const f=(0,i.iH)({ProjectCode:v.getters?.code}),y=(0,i.iH)(200),b=()=>{k(),x(),S()},S=()=>{const e=document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement;if(e){const e=window.innerHeight;y.value=Math.floor(.3*e)}else y.value=200;(0,l.Y3)((()=>{g?.resize()}))},x=()=>{document.addEventListener("fullscreenchange",S),document.addEventListener("webkitfullscreenchange",S),document.addEventListener("mozfullscreenchange",S),document.addEventListener("MSFullscreenChange",S),window.addEventListener("resize",S)},_=()=>{document.removeEventListener("fullscreenchange",S),document.removeEventListener("webkitfullscreenchange",S),document.removeEventListener("mozfullscreenchange",S),document.removeEventListener("MSFullscreenChange",S),window.removeEventListener("resize",S)};let C=(0,i.iH)([]),H=(0,i.iH)({GoodsTabList:[]});(0,l.bv)((()=>{let e=window.location.href?.split("?code=")[1];f.value.ProjectCode=e,k(),x(),S()})),(0,l.Jd)((()=>{_()}));const k=async()=>{const{data:e}=await(0,s.rT)("GetJFGoodsStatistics",f.value);if("1000"==e.code){if(p.value=e.data.GoodsTotal,C.value=e.data.GoodsEcharts,e.data.GoodsTabList&&e.data.GoodsTabList.length>0){const t=[...e.data.GoodsTabList],a=15,l=Math.ceil(a/t.length);let i=[];for(let e=0;e<l;e++)i=[...i,...t];H.value={...e.data,GoodsTabList:i}}else H.value={...e.data,GoodsTabList:Array(15).fill({GoodsName:"暂无数据"})};(0,l.Y3)((()=>{T()}))}},z=e=>{e.on("autoplayStart",(()=>{})),e.on("autoplayStop",(()=>{e.autoplay.start()}))},T=()=>{let e=a(30197);g=e.getInstanceByDom(document.getElementById("Productstatistics1")),null==g&&(g=e.init(document.getElementById("Productstatistics1")));let t={title:{top:"45%",left:"center",text:"总数量",textStyle:{color:"#fff",fontStyle:"normal",fontWeight:"normal",fontSize:14},subtext:p.value,subtextStyle:{color:"#fff",fontSize:16,fontWeight:"normal"}},tooltip:{trigger:"item",formatter:function(e){return"liquidFill"==e.seriesType?"":`<span style="display:inline-block;margin-right:5px;\n                border-radius:10px;width:10px;height:10px;background-color:${e.color};"></span>${e.name}${e.value}`}},series:[{type:"liquidFill",itemStyle:{normal:{opacity:.4,shadowBlur:0,shadowColor:"blue"}},name:"总数",data:[{value:.6,itemStyle:{normal:{color:"#53d5ff",opacity:.6}}}],color:["#53d5ff"],center:["50%","50%"],backgroundStyle:{color:"#001C4E"},label:{show:!1,normal:{formatter:"",textStyle:{fontSize:14}}},outline:{itemStyle:{borderColor:"#86c5ff",borderWidth:0},borderDistance:0}},{type:"pie",radius:["70%","90%"],color:d(),hoverAnimation:!1,label:{show:!1,normal:{formatter:"{b}\n{d}%",show:!1,position:""}},labelLine:{normal:{show:!1}},itemStyle:{emphasis:{borderWidth:0,shadowBlur:2,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},data:C.value}]};g?.setOption(t),window.addEventListener("resize",(function(){g?.resize()}))};return t({showdelog:b}),(e,t)=>((0,l.wg)(),(0,l.iD)("div",u,[h,(0,l.Wm)((0,i.SU)(r.tq),{class:"swiperdp","slides-per-view":5,loop:!0,loopedSlides:8,direction:"vertical",onSwiper:z,autoplay:{delay:1500,disableOnInteraction:!1},modules:(0,i.SU)(w),speed:800,style:(0,n.j5)({height:y.value+"px"})},{default:(0,l.w5)((()=>[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(H).GoodsTabList,((e,t)=>((0,l.wg)(),(0,l.j4)((0,i.SU)(r.o5),{key:t,class:"product-slide"},{default:(0,l.w5)((()=>[(0,l._)("div",m,[(0,l._)("div",{class:"icons",style:(0,n.j5)({background:(0,i.SU)(d)()[t]})},null,4),(0,l._)("span",null,(0,n.zw)(e.GoodsName),1)]),(0,l._)("span",null,(0,n.zw)(e.GoodsRate),1),(0,l._)("span",null,(0,n.zw)(e.GoodsNum),1)])),_:2},1024)))),128))])),_:1},8,["modules","style"])]))}},g=a(40089);const p=(0,g.Z)(v,[["__scopeId","data-v-4492396c"]]);var w=p,f=a(49242),y=a(15941);const b=["src"];var S={__name:"shops",setup(e,{expose:t}){(0,f.sj)((e=>({"07c7a646":v.value})));let a=[c.pt,c.tl,c.W_];const d=(0,o.oR)();let u=(0,i.iH)({ProjectCode:d.getters.code}),h=(0,i.iH)([]),m=[{name:"姓名:",value:"WorkerName"},{name:"班组:",value:"TeamName"},{name:"兑换商品:",value:"GoodsName"},{name:"核销积分:",value:"Score"},{name:"商品兑换时间:",value:"RedeemTime"}];const v=(0,i.iH)(4);(0,l.bv)((()=>{let e=window.location.href?.split("?code=")[1];u.value.ProjectCode=e,w(),p(),window.addEventListener("resize",p)}));const g=()=>{w(),p(),window.addEventListener("resize",p)};(0,l.Jd)((()=>{window.removeEventListener("resize",p)}));const p=()=>{window.innerHeight>920?v.value=5:v.value=4},w=async()=>{const{data:e}=await(0,s.rT)("GetJFGoodsExchangeDetail",u.value);y.log("获取售货机商品积分",e),h.value=e.data},S=e=>{setTimeout((()=>{e.autoplay.start()}),100),e.on("autoplayStart",(()=>{})),e.on("autoplayStop",(()=>{e.autoplay.start()}))};return t({showdelog:g}),(e,t)=>((0,l.wg)(),(0,l.j4)((0,i.SU)(r.tq),{class:"swiperdp","slides-per-view":v.value,loop:!0,loopedSlides:8,direction:"vertical",onSwiper:S,autoplay:{delay:1500,disableOnInteraction:!1},modules:(0,i.SU)(a),speed:800},{default:(0,l.w5)((()=>[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(h),((e,t)=>((0,l.wg)(),(0,l.j4)((0,i.SU)(r.o5),{key:t,class:"product-slide"},{default:(0,l.w5)((()=>[(0,l._)("img",{src:`data:image/jpeg;base64,${e.HeadImage}`,alt:"",style:{width:"80px",height:"100px"}},null,8,b),((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(m),((t,a)=>((0,l.wg)(),(0,l.iD)("span",{key:a},(0,n.zw)(t.name+(e[t.value]??"")),1)))),128))])),_:2},1024)))),128))])),_:1},8,["slides-per-view","modules"]))}};const x=(0,g.Z)(S,[["__scopeId","data-v-66035402"]]);var _=x;const C=e=>((0,l.dD)("data-v-084840ce"),e=e(),(0,l.Cn)(),e),H=C((()=>(0,l._)("div",{class:"el-header-left"},[(0,l._)("svg",{class:"iconfont svg"},[(0,l._)("use",{"xlink:href":"#icon-fuwuchaoshi"})]),(0,l._)("p",{class:"titles"},"安全积分云超市"),(0,l._)("p",null,"Security Points Cloud Supermarket")],-1))),k={class:"el-header-right"},z={class:"time"},T=["src"],D={class:"weater-temp"},$={class:"left-section"},R={class:"left-section-top"},j={class:"shop-title-box"},I=["src"],W={class:"title shop-title1"},E={class:"title shop-title2"},U={class:"counter-container"},L={key:0,class:"separator"},B={class:"digit-current"},G={class:"digit-next"},F={class:"indicator"},P={class:"left-section-bottom"},A=C((()=>(0,l._)("h3",{class:"titlesh3"},"售货机商品统计",-1))),Y={class:"center-section"},M=C((()=>(0,l._)("h3",{class:"titlesh3"},"人员积排名",-1))),N={class:"center-section-top"},O=C((()=>(0,l._)("h3",{class:"titlesh3"},"今日积分获取来源",-1))),K=C((()=>(0,l._)("div",{class:"center-section-bottom",id:"echarts2"},null,-1))),Z={class:"right-section"},J=C((()=>(0,l._)("h3",{class:"titlesh3"},"总收入分析图",-1))),q=C((()=>(0,l._)("div",{class:"right-section-top",id:"echarts3"},null,-1))),V=C((()=>(0,l._)("h3",{class:"titlesh3"},"今日商品兑换明细",-1))),X={class:"right-section-bottom"};var Q={__name:"largescreen1",setup(e){const t=(0,l.FN)().appContext.config.globalProperties.$http,{proxy:r}=(0,l.FN)(),c=(r.$formatDateTime(new Date,"yyyy-MM-dd"),(0,o.oR)()),d=((0,i.iH)(133),(0,i.iH)(133)),u=((0,i.iH)([]),(0,i.iH)({ZCRS:0,SoldNum:0,TotalScore:0,TodayUsingScore:0})),h=(0,i.iH)({...u.value}),m=(0,i.iH)({});let v=(0,i.iH)({data:[]}),g=(0,i.iH)({ProjectCode:c.getters?.code});const p=(0,l.Fl)((()=>{const e={};for(const t in u.value){const a=u.value[t].toString().padStart(6,"0");e[t]=a.slice(0,3)+","+a.slice(3)}return e}));let f=[{name:"人员及商品统计",value:"",src:a(73341),color:"#07a6ff",bgcolor:"rgba(7, 166, 255, 0.3)",unit:"人",list:[{name:"在场人员数量",value:"ZCRS",value1:"HBZCRSRate"},{name:"今日商品售出数量",value:"SoldNum",value1:"HBSoldRate"}]},{name:"积分统计",value:"",src:a(76770),color:"#00fffc",bgcolor:"rgba(0, 255, 252, 0.3)",unit:"分",list:[{name:"人员积分总数",value:"TotalScore",value1:"HBTotalScoreRate"},{name:"今日积分使用数量",value:"TodayUsingScore",value1:"HBTodayUsingScoreRate"}]}],y=(0,i.iH)({icon:"999"}),b=(0,i.iH)({}),S=null,x=(0,i.iH)(null),C=(0,i.iH)(null);(0,i.iH)({});(0,l.bv)((()=>{let e=window.location.href?.split("?code=")[1];g.value.ProjectCode=e,se();for(const t in u.value)m.value[t]=p.value[t].split("").map((()=>!1));Q(),ee(),te(),ae()}));const Q=async()=>{const{data:e}=await(0,s.rT)("GetJFScoreStatistics",g.value);if("1000"==e.code){b.value=e.data,h.value={...u.value};let t=!1;for(const a in u.value)void 0!==e.data[a]&&u.value[a]!==e.data[a]&&(u.value[a]=e.data[a],t=!0);if(t){for(const e in u.value)m.value[e]=p.value[e].split("").map((()=>!1));ne()}}},ee=async()=>{const{data:e}=await(0,s.rT)("GetJFScoreByPerson",g.value);"1000"==e.code&&(v.value={...v.value,data:e.data?.ScoreList.map((e=>({name:e.name,value:e.value})))},(0,l.Y3)((()=>{let e=document.querySelectorAll(".rank");e.forEach(((e,t)=>{e.textContent=t+1,e.style.backgroundImage=`url(${a(6334)("./max"+(t+1>3?"4":t+1)+".svg")})`}))})))},te=async()=>{const{data:e}=await(0,s.rT)("GetJFScoreByToday",g.value);"1000"==e.code&&(0,l.Y3)((()=>{le(e.data)}))},ae=async()=>{const{data:e}=await(0,s.rT)("GetJFRevenueAnalysis",g.value);"1000"==e.code&&(0,l.Y3)((()=>{ie(e.data)}))},le=e=>{let t=a(30197),l=t.getInstanceByDom(document.getElementById("echarts2"));null==l&&(l=t.init(document.getElementById("echarts2")));let i={tooltip:{trigger:"item"},series:[{name:"数据来源",type:"pie",radius:"60%",data:e??[],label:{show:!0,formatter:"{b}: {c} ({d}%)",color:"#fff",fontSize:12}}]};l?.setOption(i),window.addEventListener("resize",(function(){l?.resize()}))},ie=e=>{let t=a(30197),l=t.getInstanceByDom(document.getElementById("echarts3"));null==l&&(l=t.init(document.getElementById("echarts3")));const i={tooltip:{trigger:"axis",axisPointer:{label:{show:!0,backgroundColor:"#fff",color:"#556677",borderColor:"rgba(0,0,0,0)",shadowColor:"rgba(0,0,0,0)",shadowOffsetY:0},lineStyle:{width:0}},backgroundColor:"#012259",textStyle:{color:"#ffffff"},padding:[10,10],extraCssText:"box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)"},legend:{data:["积分","人民币"],textStyle:{color:"#fff"}},grid:{left:"2%",right:"4%",bottom:"2%",top:"20%",containLabel:!0},xAxis:{type:"category",data:e?.map((e=>e.name))??[],axisLine:{lineStyle:{color:"#002860"}},axisTick:{show:!1},axisLabel:{interval:1,textStyle:{color:"#ffffff"},fontSize:12,margin:15},axisPointer:{},boundaryGap:!1},yAxis:{type:"value",axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#002860"}},axisLabel:{textStyle:{color:"#ffffff"}},splitLine:{show:!0,lineStyle:{color:"#002860"}}},series:[{name:"积分",type:"line",data:e?.map((e=>e.value1))??[],symbolSize:1,symbol:"circle",smooth:!0,yAxisIndex:0,showSymbol:!1,lineStyle:{width:3,color:"#00aff0",shadowColor:"rgba(158,135,255, 0.3)"},itemStyle:{normal:{color:"#00aff0"}}},{name:"人民币",type:"line",stack:"Total",data:e?.map((e=>e.value2))??[],symbolSize:1,symbol:"circle",smooth:!0,yAxisIndex:0,showSymbol:!1,lineStyle:{width:3,color:"#95ED6A",shadowColor:"rgba(158,135,255, 0.3)"},itemStyle:{normal:{color:"#95ED6A"}}}]};l?.setOption(i),window.addEventListener("resize",(function(){l?.resize()}))},oe=(e,t)=>{if(!t){const t=d.value.toString().padStart(6,"0"),a=t.slice(0,3)+","+t.slice(3);return e>=a.length||","===a[e]?"":a[e]}const a=h.value[t].toString().padStart(6,"0"),l=a.slice(0,3)+","+a.slice(3);return e>=l.length||","===l[e]?"":l[e]},ne=()=>{setTimeout((()=>{for(const e in u.value){const t=p.value[e],a=h.value[e].toString().padStart(6,"0"),l=a.slice(0,3)+","+a.slice(3);t.split("").forEach(((t,a)=>{a<l.length&&t!==l[a]&&(m.value[e]||(m.value[e]=[]),m.value[e][a]=!0,setTimeout((()=>{m.value[e][a]=!1}),600))}))}}),0)},se=async()=>{const{data:e}=await t.get("https://api.qweather.com/v7/weather/now?location=101210102&key=2fea52dc1c3a48299b93406599ae5362");"200"==e.code&&(y.value=e.now)};return(0,l.Jd)((()=>{S&&clearInterval(S)})),(e,t)=>{const a=(0,l.up)("Clock"),o=(0,l.up)("el-icon"),s=(0,l.up)("el-header"),r=(0,l.up)("dv-decoration-2"),c=(0,l.up)("dv-decoration-6"),d=(0,l.up)("dv-scroll-ranking-board"),u=(0,l.up)("el-main"),h=(0,l.up)("el-container");return(0,l.wg)(),(0,l.j4)(h,{class:"large-bg"},{default:(0,l.w5)((()=>[(0,l.Wm)(s,null,{default:(0,l.w5)((()=>[H,(0,l._)("div",k,[(0,l.Wm)(o,null,{default:(0,l.w5)((()=>[(0,l.Wm)(a)])),_:1}),(0,l._)("span",z,(0,n.zw)(e.$formatDateTime(new Date,"yyyy-MM-dd w")),1),(0,l._)("h2",null,(0,n.zw)(e.$formatDateTime(new Date,"HH:mm:ss")),1),(0,l._)("img",{class:"weater",src:`https://ai-zqface-com-wjgl.oss-cn-hangzhou.aliyuncs.com/fillist/Realsystem/icons/${(0,i.SU)(y).icon}.png`,alt:"",style:{width:"50px",height:"50px"}},null,8,T),(0,l._)("span",null,(0,n.zw)((0,i.SU)(y).text),1),(0,l._)("span",D,(0,n.zw)((0,i.SU)(y).temp)+"°C",1)])])),_:1}),(0,l.Wm)(r,{style:{width:"100%",height:"5px"}}),(0,l.Wm)(u,null,{default:(0,l.w5)((()=>[(0,l._)("div",$,[(0,l._)("div",R,[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(f),(e=>((0,l.wg)(),(0,l.iD)("div",{class:"left-section-item",key:e.name},[(0,l._)("div",j,[(0,l._)("h3",{class:"shop-title",style:(0,n.j5)({color:e.color})},(0,n.zw)(e.name),5),(0,l.Wm)(c,{style:{width:"100px",height:"20px"}}),(0,l._)("img",{src:e.src,alt:"",style:{width:"100%",height:"8px"}},null,8,I)]),((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(e.list,((t,a)=>((0,l.wg)(),(0,l.iD)("div",{class:"shop-content",key:a,style:(0,n.j5)({color:e.color})},[(0,l._)("span",W,(0,n.zw)(t.name),1),(0,l._)("span",E,"环比+"+(0,n.zw)((0,i.SU)(b)[t.value1]),1),(0,l._)("div",U,[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(p.value[t.value],((a,i)=>((0,l.wg)(),(0,l.iD)(l.HY,{key:i},[","===a?((0,l.wg)(),(0,l.iD)("div",L,"，")):((0,l.wg)(),(0,l.iD)("div",{key:1,class:"digit-box",style:(0,n.j5)({"background-color":e.bgcolor})},[(0,l._)("div",{class:(0,n.C_)(["digit-flip-container",m.value[t.value]&&m.value[t.value][i]?"flip-animate":""])},[(0,l._)("div",B,(0,n.zw)(a),1),(0,l._)("div",G,(0,n.zw)(oe(i,t.value)),1)],2)],4))],64)))),128)),(0,l._)("span",F,(0,n.zw)(e.unit),1)])],4)))),128))])))),128))]),(0,l._)("div",P,[A,(0,l.Wm)(w,{ref_key:"Productstatistics1",ref:x},null,512)])]),(0,l._)("div",Y,[M,(0,l._)("div",N,[(0,l.Wm)(d,{config:(0,i.SU)(v),style:{width:"100%",height:"300px"}},null,8,["config"])]),O,K]),(0,l._)("div",Z,[J,q,V,(0,l._)("div",X,[(0,l.Wm)(_,{ref_key:"Shops1",ref:C},null,512)])])])),_:1})])),_:1})}}};const ee=(0,g.Z)(Q,[["__scopeId","data-v-084840ce"]]);var te=ee;const ae=e=>((0,l.dD)("data-v-c8a84108"),e=e(),(0,l.Cn)(),e),le={class:"decoration"},ie=ae((()=>(0,l._)("span",null,null,-1))),oe=ae((()=>(0,l._)("span",null,null,-1))),ne=ae((()=>(0,l._)("span",null,null,-1))),se=[ie,oe,ne];var re={__name:"decoration",props:{color:{type:String,default:"#00ffff"},size:{type:Number,default:8},middleSize:{type:Number,default:10},gap:{type:Number,default:4}},setup(e){const t=e;(0,f.sj)((t=>({ab871b04:e.gap+"px","5d577df6":e.size+"px","70550f9c":e.color,"50fcd9ae":a.value,"6e9bf170":e.middleSize+"px"})));const a=(0,l.Fl)((()=>t.color+"4D"));return(e,t)=>((0,l.wg)(),(0,l.iD)("div",le,se))}};const ce=(0,g.Z)(re,[["__scopeId","data-v-c8a84108"]]);var de=ce,ue=a(15063);const he=e=>((0,l.dD)("data-v-6c85c8ca"),e=e(),(0,l.Cn)(),e),me={class:"large-bg"},ve=he((()=>(0,l._)("h2",{class:"title"},"安全云积分驾驶舱",-1))),ge={class:"star-title"},pe=he((()=>(0,l._)("p",null,"月安全之星",-1))),we={class:"month"},fe={class:"star-title ranking"},ye=he((()=>(0,l._)("p",null,"人员实时积分排行",-1))),be={class:"shop-content"},Se={class:"digit-current"},xe={class:"digit-next"},_e={key:1},Ce={key:2,class:"ranking2"},He={class:"scroll-text-container"},ke={class:"content-one"},ze={class:"content-two-box"},Te={class:"star-title"},De=he((()=>(0,l._)("p",null,"积分获取来源",-1))),$e={class:"box"},Re=he((()=>(0,l._)("div",{class:"pie1",id:"pie1"},null,-1))),je={key:0,class:"column0"},Ie={class:"content-two-box"},We={class:"star-title"},Ee=he((()=>(0,l._)("p",null,"安全隐患随手拍统计",-1))),Ue={class:"box"},Le=he((()=>(0,l._)("div",{class:"pie1",id:"pie2"},null,-1))),Be={key:0,class:"column0"},Ge={class:"content-three"},Fe={class:"star-title"},Pe=he((()=>(0,l._)("p",null,"商品兑换记录",-1))),Ae={class:"star-title"},Ye=he((()=>(0,l._)("p",null,"积分变化记录",-1))),Me=he((()=>(0,l._)("div",{class:"bar echarts",id:"bar1"},null,-1))),Ne={class:"content-two-box1"};var Oe={__name:"largescreen2",setup(e){(0,f.sj)((e=>({"569a08a8":(0,i.SU)(T)})));const t=(0,o.oR)(),r=(0,l.FN)().appContext.config.globalProperties.$colorlist;let c=(0,i.iH)({ProjectCode:"",Type:0,RankType:1,HazardType:0,RedeemType:0}),d=(0,i.iH)(),u=["#ccc","#FFD700","#B87333"],h=(0,i.iH)({data:[],rowNum:10,oddRowBGC:"transparent",columnWidth:[40,50,200,60],evenRowBGC:"transparent",list:["index","name","value1","value"]}),m=(0,i.iH)({header:["获取来源","获取占比","积分数量"],headerBGC:"transparent",data:[],oddRowBGC:"transparent",evenRowBGC:"transparent",waitTime:3e3,rowHeight:35,rowNum:7,columnWidth:[150,100,100],hoverPause:!0,index:!1,list:["ScoreType","ScoreRate","ScoreCount"]}),v=(0,i.iH)({header:["隐患类型","隐患占比","隐患数量"],headerBGC:"transparent",data:[],oddRowBGC:"transparent",evenRowBGC:"transparent",waitTime:3e3,rowHeight:35,rowNum:7,columnWidth:[150,100,100],hoverPause:!0,index:!1,list:["ScoreType","ScoreRate","ScoreCount"]}),g=(0,i.iH)({header:["#","时间","姓名","变动原因","积分"],data:[],oddRowBGC:"#003B51",evenRowBGC:"#0A2732",waitTime:3e3,rowHeight:35,rowNum:5,columnWidth:[50,160,80,200,80],hoverPause:!0,index:!1,list:["rowNum","InDate","WorkerName","ScoreSource","ScoreChange"]}),p=(0,i.iH)([]),w=[{name:"本周",value:3},{name:"本月",value:1},{name:"本年",value:2},{name:"总计",value:0}],y=[{name:"实时",value:0},{name:"本周",value:3},{name:"本月",value:1}];const b=(0,i.iH)({});let S=["icon-wodeshangpin","icon-banzu1","icon-a-yonghuxinxiyonghu","icon-shipin1","icon-viivajifen-zongjifen","icon-duihuan1","icon-shitiku","icon-suishoupai","icon-ziliaoku"];const x=(0,i.iH)({ScorePerson:0,ScoreTotal:0});let _=[{name:"获得积分人数",bgcolor:"rgba(7, 166, 255, 0.3)",color:"#07a6ff",value:"ScorePerson"},{name:"获得总积分",bgcolor:"rgba(0, 255, 255, 0.3)",color:"#00fffc",value:"ScoreTotal"}],C=((0,i.iH)(0),(0,i.iH)([])),H=((0,i.iH)(null),(0,i.iH)(0),(0,i.iH)([])),k=(0,i.iH)([]);const z=(0,i.iH)({pie1:null,pies0:null,bar1:null});let T=(0,i.iH)(850),D=(0,i.iH)("50%"),$=(0,i.iH)("40%"),R=(0,i.iH)("300"),j=(0,i.iH)("450"),I=(0,i.iH)("250");const W=(0,i.iH)({...x.value}),E=(0,l.Fl)((()=>{const e={};for(const t in x.value)e[t]=x.value[t].toString().padStart(5,"0");return e}));let U,L=(0,i.iH)(0),B=(0,i.iH)(""),G=(0,i.iH)(null);(0,l.YP)((()=>t.getters.code),(async e=>{c.value.ProjectCode=e,await(0,l.Y3)();for(const t in x.value)b.value[t]=E.value[t].split("").map((()=>!1));K(),q(),V(),ee(),Q(),se(),Z(),te(),G.value=setInterval((()=>{Z()}),3e4)}),{immediate:!0});const F=e=>({fontSize:(0==e?70:1==e?80:50)+"px"}),P=e=>{const t=[80,120,50];return 0===e?`height:${t[e]}px;border:2px solid ${u[e]};\n        border-right: none;\n        border-radius: 10px 0 0 0;`:1===e?`height:${t[e]}px;border:2px solid ${u[e]};\n        border-radius: 10px 10px 0 0;`:`height:${t[e]}px;border:2px solid ${u[e]};\n        border-left: none;\n        border-radius: 0 10px 0 0;`},A=e=>{let t=e.value/L.value*100;return`width:${t}%`},Y=(e,t)=>t?W.value[t].toString().padStart(5,"0")[e]||"":W.value.toString().padStart(5,"0")[e]||"",M=()=>{let e=window.innerHeight;e<=1e3?(T.value=850,D.value="55%",$.value="40%",R.value="300",j.value="450"):e<=1100?(T.value=980,D.value="61%",$.value="44%",R.value="350",j.value="590"):(T.value=850,D.value="55%",$.value="40%",R.value="300",j.value="450")},N=()=>{setTimeout((()=>{for(const e in x.value){const t=E.value[e],a=W.value[e].toString().padStart(5,"0");t.split("").forEach(((t,l)=>{l<a.length&&t!==a[l]&&(b.value[e]||(b.value[e]=[]),b.value[e][l]=!0,setTimeout((()=>{b.value[e][l]=!1}),600))}))}}),0)},O=()=>{let e=`color:${B.value.FontColor};font-size:${B.value.FontSize}px`;return e},K=async()=>{const{data:e}=await(0,s.rT)("GetThisMonthSafetyStar",c.value);"1000"==e.code&&(d.value=e.data,d.value.length>=2&&([d.value[0],d.value[1]]=[d.value[1],d.value[0]]))},Z=async()=>{const{data:e}=await(0,s.rT)("GetScrollingSloganByLED",c.value);"1000"==e.code&&(B.value=e.data)},J=e=>{const t=e&&e>0&&e<=3?e:4;return`background-image:url(${a(6334)("./max"+t+".svg")})`},q=async()=>{const{data:e}=await(0,s.rT)("GetJFScoreByPerson",c.value);W.value={...x.value};let t=!1;for(const a in x.value)void 0!==e.data[a]&&x.value[a]!==e.data[a]&&(x.value[a]=e.data[a],t=!0);if(t){for(const e in x.value)b.value[e]=E.value[e].split("").map((()=>!1));N()}L.value=e.data.MaxScore,h.value={...h.value,data:e.data?.ScoreList.map(((e,t)=>({name:e.name,value:e.value,value1:"",index:t+1})))}},V=async()=>{const{data:e}=await(0,s.rT)("GetRealCountByProject",c.value);"1000"==e.code&&(p.value=e.data,p.value[0].Count=378)},X=(e,t,a)=>{switch(a){case 0:c.value.RankType=e.value,q();break;case 1:c.value.Type=e.value,ee();break;case 2:c.value.RedeemType=e.value,se();break;case 3:c.value.HazardType=e.value,te();break}},Q=async()=>{const{data:e}=await(0,s.rT)("GetScoreRecordTableByProject",c.value);H.value=e.data,"1000"==e.code&&(H.value=e.data,g.value={...g.value,data:H.value})},ee=async()=>{const{data:e}=await(0,s.rT)("GetJFScoreByDate",c.value),t=e.data.EchartTable?.map(((e,t)=>({index:t,...e,ScoreType:e.ScoreType||""})));C.value=e.data,m.value={...m.value,data:t},(0,l.Y3)((()=>{ie(e.data.EchartList,"pie1")}))},te=async()=>{const{data:e}=await(0,s.rT)("GetHazardTypeStatistics",c.value),t=e.data.EchartTable?.map(((e,t)=>({index:t,...e,ScoreType:e.ScoreType||""})));v.value={...v.value,data:t},(0,l.Y3)((()=>{ie(e.data.EchartList,"pie2")}))},ae=(e,t=0)=>{const l=document.getElementById(e);if(!l)return t<3?(setTimeout((()=>ae(e,t+1)),200),null):null;const{width:i,height:o}=l.getBoundingClientRect();if(0===i||0===o)return t<3?(setTimeout((()=>ae(e,t+1)),200),null):null;try{let t=a(30197),i=t.getInstanceByDom(l);return null==i&&(i=t.init(l,null,{renderer:"canvas",useDirtyRect:!0})),z.value[e]=i,i}catch(n){return t<3?(setTimeout((()=>ae(e,t+1)),200),null):null}},le=(e,t)=>{const a=z.value[e];a&&a.setOption(t)},ie=(e,t)=>{if(!z.value[t])return;let a={tooltip:{trigger:"item"},series:[{name:"数据来源",type:"pie",radius:"70%",data:e??[],color:r(),label:{show:!0,position:"inside",formatter:"{c}%",color:"#fff",fontSize:12,fontWeight:"bold"}}]};le(t,a)},oe=e=>{if(!z.value.bar1)return;let t={tooltip:{trigger:"item",axisPointer:{type:"shadow"},formatter:function(e){return`<span style="display:inline-block;margin-right:5px;\n                    border-radius:10px;width:10px;height:10px;background-color:${e.color};"></span>${e.name}：${e.value}`}},grid:{top:"5%",left:"2%",bottom:"1%",right:"2%",containLabel:!0},xAxis:[{type:"category",data:e?.map((e=>e.name)),nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#82b0ec"}},axisLabel:{interval:0,formatter:function(e){return e.slice(0,2)},textStyle:{color:"#fff"}}}],yAxis:[{type:"value",axisLabel:{textStyle:{color:"#fff"},formatter:"{value}"},splitLine:{lineStyle:{color:"#0c2c5a"}},axisLine:{show:!1}}],series:[{name:"",type:"pictorialBar",symbolSize:[20,10],symbolOffset:[0,-5],symbolPosition:"end",z:12,label:{normal:{show:!0,position:"inside",align:"center",formatter:"{c}"}},data:e?.map((e=>({...e,itemStyle:{color:e.itemStyle?.color||"#07a6ff"}})))},{type:"bar",itemStyle:{normal:{opacity:.7}},barWidth:"20",data:e?.map((e=>({...e,itemStyle:{color:e.itemStyle?.color||"#07a6ff"}})))}]};le("bar1",t)},ne=(e,t)=>{const a=`pies${t}`;z.value[a]||ae(a);let l={series:[{type:"gauge",startAngle:90,endAngle:-270,pointer:{show:!1},progress:{show:!0,overlap:!1,roundCap:!0,clip:!1,itemStyle:{color:"#1AC9FF"}},axisLine:{lineStyle:{width:8,color:[[1,"#0A5681"]]}},splitLine:{show:!1,distance:0,length:10},axisTick:{show:!1},axisLabel:{show:!1,distance:0},data:e,title:{show:!1,fontSize:20},detail:{show:!0,width:50,height:14,fontSize:20,color:"#fff",offsetCenter:[0,0],formatter:function(e){return e+"%"}}}]};le(a,l)},se=async()=>{const{data:e}=await(0,s.rT)("GetRedeemGoodsRecordEcharts",c.value);k.value=e.data.RedeemEchartsList;const t=r();let a=e.data.RedeemTreeList.map(((e,a)=>({name:e.name||"",value:e.value||0,itemStyle:{color:t[a%t.length]}})));oe(a),await(0,l.Y3)(),k.value.forEach(((e,t)=>{const a=`pies${t}`,l=document.getElementById(a);if(l){l.style.display="block",l.style.visibility="visible";const i=ae(a);if(i){const a=[{...e.RedeemEcharts[0],title:{offsetCenter:["50%","50%"]}}];ne(a,t)}}}))};(0,l.bv)((async()=>{await(0,l.Y3)();const e=e=>{const t=document.getElementById(e);if(t){const e=window.getComputedStyle(t);"none"!==e.display&&"hidden"!==e.visibility||(t.style.display="block",t.style.visibility="visible")}};["pie1","bar1","pie2"].forEach((t=>{e(t);ae(t)})),M(),window.addEventListener("resize",M)})),(0,l.Jd)((()=>{window.removeEventListener("resize",M),clearInterval(G.value),Object.entries(z.value).forEach((([e,t])=>{if(t&&t.dispose)try{t.dispose()}catch(a){}})),z.value={pie1:null,pies0:null,pies1:null,pies2:null,bar1:null},U&&U.dispose()}));const re=({width:e,height:t})=>{};return(e,t)=>{const a=(0,l.up)("dv-decoration-8"),o=(0,l.up)("dv-decoration-5"),s=(0,l.up)("el-header"),f=(0,l.up)("el-button"),x=(0,l.up)("el-aside"),C=(0,l.up)("el-main"),H=(0,l.up)("el-container");return(0,l.wg)(),(0,l.iD)("div",me,[(0,l.Wm)(H,null,{default:(0,l.w5)((()=>[(0,l.Wm)(s,null,{default:(0,l.w5)((()=>[(0,l.Wm)(a,{class:"abs abs1",style:{width:"25%",height:"50px"}}),ve,(0,l.Wm)(o,{style:{width:"500px",height:"40px"}}),(0,l.Wm)(a,{class:"abs abs2",reverse:!0,style:{width:"25%",height:"50px"}})])),_:1}),(0,l.Wm)(H,null,{default:(0,l.w5)((()=>[(0,l.Wm)(x,{width:"20%",class:"padding",style:(0,n.j5)({height:(0,i.SU)(T)+"px"})},{default:(0,l.w5)((()=>[(0,l._)("div",ge,[(0,l.Wm)(de,{color:"#00ffff",size:8,middleSize:10}),pe]),(0,l._)("div",we,[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(d),((e,t)=>((0,l.wg)(),(0,l.iD)("div",{key:t,class:"month-item",style:(0,n.j5)(`color:${(0,i.SU)(u)[t]}`)},[(0,l._)("h4",null,(0,n.zw)(e.name),1),(0,l._)("h4",null,(0,n.zw)(e.value),1),(0,l._)("div",{class:(0,n.C_)("icon"),style:(0,n.j5)(`${P(t)}`)},[(0,l._)("i",{class:(0,n.C_)("iconfont icon-jiangbei"),style:(0,n.j5)(F(t))},null,4)],4)],4)))),128))]),(0,l._)("div",fe,[(0,l.Wm)(de,{color:"#00ffff",size:8,middleSize:10}),ye,((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(y),((e,t)=>((0,l.wg)(),(0,l.j4)(f,{key:t,class:(0,n.C_)(`btns${t}`),link:"",onClick:a=>X(e,t,0),type:(0,i.SU)(c).RankType==e.value?"success":"primary"},{default:(0,l.w5)((()=>[(0,l.Uk)((0,n.zw)(e.name),1)])),_:2},1032,["class","onClick","type"])))),128))]),(0,l._)("div",be,[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(_),((e,t)=>((0,l.wg)(),(0,l.iD)("span",{key:t,style:(0,n.j5)(`color:${e.color}`)},(0,n.zw)(e.name),5)))),128)),((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(_),((e,t)=>((0,l.wg)(),(0,l.iD)("div",{class:"counter-container",key:t},[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(E.value[e.value],((t,a)=>((0,l.wg)(),(0,l.iD)("div",{key:a,class:"digit-box",style:(0,n.j5)({"background-color":e.bgcolor})},[(0,l._)("div",{class:(0,n.C_)(["digit-flip-container",b.value[e.value]&&b.value[e.value][a]?"flip-animate":""])},[(0,l._)("div",Se,(0,n.zw)(t),1),(0,l._)("div",xe,(0,n.zw)(Y(a,e.value)),1)],2)],4)))),128))])))),128))]),(0,l._)("div",{class:"center-section-top",style:(0,n.j5)({height:(0,i.SU)(D)})},[(0,l.Wm)(ue.Z,{ref:"scrollTableRef",class:"scroll-ranking",style:(0,n.j5)(`height: ${(0,i.SU)(j)}px;`),config:(0,i.SU)(h),onResize:re},{cell:(0,l.w5)((({row:e,columnIndex:t})=>[0==t?((0,l.wg)(),(0,l.iD)("div",{key:0,class:"ranking0",style:(0,n.j5)(J(e.index))},[(0,l._)("span",null,(0,n.zw)(e.index),1)],4)):1==t?((0,l.wg)(),(0,l.iD)("span",_e,(0,n.zw)(e.name),1)):2==t?((0,l.wg)(),(0,l.iD)("div",Ce,[(0,l._)("div",{class:"ranking2-info",style:(0,n.j5)(A(e))},null,4)])):(0,l.kq)("",!0)])),_:1},8,["style","config"])],4)])),_:1},8,["style"]),(0,l.Wm)(C,{class:"padding",style:(0,n.j5)({height:(0,i.SU)(T)+"px"})},{default:(0,l.w5)((()=>[(0,l._)("div",He,[(0,l._)("div",{class:"scroll-text",style:(0,n.j5)(O())},(0,n.zw)((0,i.SU)(B).SloganContent),5)]),(0,l._)("div",ke,[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(p),((e,t)=>((0,l.wg)(),(0,l.iD)("div",{key:t,class:"content-one-item",style:{width:"135px",height:"165px"}},[(0,l._)("i",{class:(0,n.C_)(`fonts iconfont ${(0,i.SU)(S)[t]}`),style:(0,n.j5)(`color:${(0,i.SU)(r)()[t]}`)},null,6),(0,l._)("p",{class:"text-p",style:(0,n.j5)(`color:${(0,i.SU)(r)()[t]}`)},(0,n.zw)(e.Name),5),(0,l._)("h2",{class:"texts",style:(0,n.j5)(`--shadow-color: ${(0,i.SU)(r)()[t]};}`)},(0,n.zw)(e.Count),5)])))),128))]),(0,l._)("div",{class:"content-two",style:(0,n.j5)({height:(0,i.SU)($)})},[(0,l._)("div",ze,[(0,l._)("div",Te,[(0,l.Wm)(de,{color:"#00ffff",size:8,middleSize:10}),De]),(0,l._)("div",$e,[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(w),((e,t)=>((0,l.wg)(),(0,l.j4)(f,{key:t,link:"",onClick:a=>X(e,t,1),type:(0,i.SU)(c).Type==e.value?"success":"primary"},{default:(0,l.w5)((()=>[(0,l.Uk)((0,n.zw)(e.name),1)])),_:2},1032,["onClick","type"])))),128))]),Re,(0,l.Wm)(ue.Z,{ref:"scrollTableRef",class:"scroll-table",style:(0,n.j5)(`height: ${(0,i.SU)(R)}px;`),config:(0,i.SU)(m),onResize:re},{cell:(0,l.w5)((({row:e,columnIndex:t})=>[0==t?((0,l.wg)(),(0,l.iD)("div",je,[(0,l._)("div",{class:"icons",style:(0,n.j5)(`background-color:${(0,i.SU)(r)()[e?.index]}`)},null,4),(0,l._)("span",null,(0,n.zw)(e?.ScoreType??""),1)])):((0,l.wg)(),(0,l.iD)("div",{key:1,class:(0,n.C_)(`custom-column-${t+1}`)},(0,n.zw)(e?e[Object.keys(e)[t+1]]??"":""),3))])),_:1},8,["style","config"])]),(0,l._)("div",Ie,[(0,l._)("div",We,[(0,l.Wm)(de,{color:"#00ffff",size:8,middleSize:10}),Ee]),(0,l._)("div",Ue,[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(w),((e,t)=>((0,l.wg)(),(0,l.j4)(f,{key:t,link:"",onClick:a=>X(e,t,3),type:(0,i.SU)(c).HazardType==e.value?"success":"primary"},{default:(0,l.w5)((()=>[(0,l.Uk)((0,n.zw)(e.name),1)])),_:2},1032,["onClick","type"])))),128))]),Le,(0,l.Wm)(ue.Z,{ref:"scrollTableRef",class:"scroll-table",style:(0,n.j5)(`height: ${(0,i.SU)(R)}px;`),config:(0,i.SU)(v),onResize:re},{cell:(0,l.w5)((({row:e,columnIndex:t})=>[0==t?((0,l.wg)(),(0,l.iD)("div",Be,[(0,l._)("div",{class:"icons",style:(0,n.j5)(`background-color:${(0,i.SU)(r)()[e?.index]}`)},null,4),(0,l._)("span",null,(0,n.zw)(e?.ScoreType??""),1)])):((0,l.wg)(),(0,l.iD)("div",{key:1,class:(0,n.C_)(`custom-column-${t+1}`)},(0,n.zw)(e?e[Object.keys(e)[t+1]]??"":""),3))])),_:1},8,["style","config"])])],4),(0,l._)("div",Ge,[(0,l._)("div",Fe,[(0,l.Wm)(de,{color:"#00ffff",size:8,middleSize:10}),Pe,((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,i.SU)(w),((e,t)=>((0,l.wg)(),(0,l.j4)(f,{key:t,class:(0,n.C_)(`btn${t}`),link:"",onClick:a=>X(e,t,2),type:(0,i.SU)(c).RedeemType==e.value?"success":"primary"},{default:(0,l.w5)((()=>[(0,l.Uk)((0,n.zw)(e.name),1)])),_:2},1032,["class","onClick","type"])))),128)),(0,l._)("div",Ae,[(0,l.Wm)(de,{color:"#00ffff",size:8,middleSize:10}),Ye])]),Me,(0,l._)("div",Ne,[(0,l.Wm)(ue.Z,{ref:"scrollTableRef",class:"scroll-table",config:(0,i.SU)(g),style:(0,n.j5)(`height:${(0,i.SU)(I)}px`),onResize:re},null,8,["config","style"])])])])),_:1},8,["style"])])),_:1})])),_:1})])}}};const Ke=(0,g.Z)(Oe,[["__scopeId","data-v-6c85c8ca"]]);var Ze=Ke;const Je={class:"large-screen"};var qe={__name:"largescreen",setup(e){let t=(0,i.iH)(1);const a=(0,o.oR)(),n=()=>{try{const e=window.location.hash,l=e.split("?code=")[1].split("&"),i=l[0];i&&a.dispatch("getcode",i),t.value=1}catch(e){t.value=1}},s=()=>{n()};return(0,l.bv)((()=>{n(),window.addEventListener("popstate",s)})),(0,l.Ah)((()=>{window.removeEventListener("popstate",s)})),(e,a)=>((0,l.wg)(),(0,l.iD)("div",Je,[0==(0,i.SU)(t)?((0,l.wg)(),(0,l.j4)(te,{key:0,ref:"largescreen"},null,512)):1==(0,i.SU)(t)?((0,l.wg)(),(0,l.j4)(Ze,{key:1,ref:"largescreen2"},null,512)):(0,l.kq)("",!0)]))}};const Ve=(0,g.Z)(qe,[["__scopeId","data-v-27683f33"]]);var Xe=Ve},6334:function(e,t,a){var l={"./max1.svg":41249,"./max2.svg":62770,"./max3.svg":66536,"./max4.svg":93612};function i(e){var t=o(e);return a(t)}function o(e){if(!a.o(l,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return l[e]}i.keys=function(){return Object.keys(l)},i.resolve=o,e.exports=i,i.id=6334},73341:function(e,t,a){"use strict";e.exports=a.p+"img/001.3f1002ce.svg"},76770:function(e,t,a){"use strict";e.exports=a.p+"img/002.1b0d78d9.svg"},41249:function(e,t,a){"use strict";e.exports=a.p+"img/max1.e2b7b919.svg"},62770:function(e,t,a){"use strict";e.exports=a.p+"img/max2.1fcd35ca.svg"},66536:function(e,t,a){"use strict";e.exports=a.p+"img/max3.89810f62.svg"},93612:function(e,t,a){"use strict";e.exports=a.p+"img/max4.718b0133.svg"}}]);