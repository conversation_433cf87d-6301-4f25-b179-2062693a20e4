// 智能监控
<template>
  <div class="monitor">
    <div class="monitor-echarts">
        <div v-for="(item,index) in topecharts" :key="index" class="monitor-echarts-one" @click="openmonitor(item,index)">
            <div :id="item.name" style="height:85px;width:100px"></div>
            <p :style="{color:item.textcolor}" class="datedelog-p">{{item.value}}</p>
        </div>
    </div>
    <img v-for="(item,index) in imglist" @mouseenter="mouseenter(item,index)" @mouseleave="mouseleave(item,index)"
    :key="index" class="imgpoting cursor"  :src="falge==index?item.src1:item.src" 
    :style="`top:${item.YPosition}%;left:${item.XPosition}%`" alt="" srcset="" @click="open(item)">
    
    <el-dialog v-model="dialogTableVisible" class="delogss" :width="'70%'" @close="clsoe">
    <selection ref="selections" @colses="closes" :titles="title"></selection>
    <div class="homemonitor-one bodybottom">
        <div v-for="(item,index) in toptitles" :key="index" :class="'monitor'+index">
            <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
                rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
                <div class="datedelog-body-one">
                    <img src="@/assets/img/home/<USER>" alt="">
                    <p class="datedelog-p">{{item}}</p>
                </div>
            </div>
            <div v-if="item=='监控信息'" class="homemonitor-inform">
                <p v-for="(its,i) in plist" :key="i" class="homemonitor-p">{{its}}：{{its=='监控类型'?formvideo.MonitorType:''}}</p>
                <div class="morplay" id="morplay"></div>
            </div>
            <div v-else-if="item=='抓拍记录'">
                <div class="homemonitor-body-two">
                    <p v-for="(item,index) in leftlable" :key="index" :style="{color:bgcolor.font}">{{item.name}}：{{leftform[item.value]}}</p>
                </div>
                <div class="homemonitor-body-three">
                    <img :src="leftform.SnapImage" alt=""  @click="pic(leftform.SnapImage)" style="height:180px;width:120px" >
                    <div class="Similaritysum">
                        <Bieechart :ids="'morSimilarity'" :options1="option"></Bieechart>
                        <p class="Similaritysum-p"  :style="{color:bgcolor.font}">相似度</p>
                    </div>
                    <img :src="leftform.HeadImage" alt=""  @click="pic(leftform.HeadImage)" style="height:180px;width:120px">
                </div>
            </div>
            <div v-else-if="item=='抓拍事件'" class="tablezpevent">
                <el-table :data="formvideo.SnapRecodList"  :style="[`width: 100%;color:${bgcolor.font};
                    --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
                    :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
                    empty-text="暂无数据" max-height="500px" @row-click="openleft"
                    >
                        <template #empty>
                            <el-empty  v-loading="loading"></el-empty>
                        </template>
                        <el-table-column width="90" prop="rowNum" label="序号" align="center"> </el-table-column>
                        <el-table-column prop="SnapTime" label="抓拍时间" align="center"> </el-table-column>
                        <el-table-column prop="AlarmType" label="抓拍事件" align="center"> </el-table-column>
                        <el-table-column prop="WorkerName" label="员工名称" align="center"> </el-table-column>
                        <el-table-column prop="GNAME" label="所属工种" align="center"> </el-table-column>
                        <el-table-column prop="TeamName" label="所属班组" align="center"> </el-table-column>
                        <el-table-column prop="CellPhone" label="手机号码" align="center"> </el-table-column>
                        <el-table-column prop="SnapImage" label="抓拍照片" align="center">
                            <template #default="scope">
                                <img :src="scope.row.SnapImage" style="width:50px;height:50px" @click="pic(scope.row.SnapImage)" class="cursor" alt="" srcset="">
                            </template>
                        </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="getform.page"
                    v-model:page-size="getform.count"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="Totles"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>
    </div>
    <picimg ref="picimg"></picimg>
    </el-dialog>
    <delog ref="delogs"></delog>

  </div>
</template>

<script>
import { onMounted, ref } from 'vue'
import Bieechart from "@/components/connet/Common/echartscom.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";
import selection from "@/components/connet/Common/selection.vue";
import delog from "@/components/connet/monitoring/delog.vue";
export default {
components:{
    Bieechart,
    picimg,
    selection,
    delog
    },
    setup(){
        let bgcolor=ref({})
        let imglist=ref(
        [{
        src:require('@/assets/img/home/<USER>/monitor.png'),
        src1:require('@/assets/img/home/<USER>/monitor1.png'),
        IconType:'监控',
        YPosition:'30',
        XPosition:'50'
        },
        ]
        )
        let topecharts=ref([{
            name:'c1',
            value:'监控数量',
            textcolor:'#00e4ff',
            colorStops:['#43e6b4','#2a9a8b'],
            borderColor:'#27e5f1',
            value1:'',
            unti:'台'
        },
        {
            name:'c2',
            value:'离线监控',
            textcolor:'#3f80ff',
            colorStops:['#3f80ff','#165eea'],
            borderColor:'#3f80ff',
            value1:'',
            unti:'台'
        },
        {
            name:'c3',
            value:'监控在线率',
            textcolor:'#a682e6',
            colorStops:['#a682e6','#a682e6'],
            borderColor:'#a682e6',
            value1:'',
            unti:'%'
        },
        {
            name:'c4',
            value:'抓拍总事件',
            textcolor:'#e32639',
            colorStops:['#e15d6a','#e32639'],
            borderColor:'#e32639',
            value1:'',
            unti:'次'
        },
        {
            name:'c5',
            value:'近7天抓拍事件',
            textcolor:'#e7691d',
            colorStops:['#f39861','#e7691d'],
            borderColor:'#e7691d',
            value1:'',
            unti:'次'
        }
        ])
        let falge=ref(-1)
        let loading=ref(false)
        let dialogTableVisible=ref(false)
        let toptitles=['监控信息','抓拍事件','抓拍记录']
        let plist=['监控名称','监控类型']
        let title=ref('监控抓拍信息')
        let option=ref({})
        let formecahrt=ref({})

        let getform=ref({
            ProjectCode:store.getters.code,
            EquipCode:'J747404931',
            InUserName:store.getters.username,
            page:1,
            count:10
        })
        let formvideo=ref({})
        let leftlable=ref([{
            name:'姓名',
            value:'WorkerName'
        },{
            name:'性别',
            value:'Sex'
        },{
            name:'年龄',
            value:'Age'
        },{
            name:'类型',
            value:'WorkerType'
        },{
            name:'工种',
            value:'GNAME'
        },{
            name:'班组',
            value:'TeamName'
        },{
            name:'手机号码',
            value:'CellPhone'
        },{
            name:'抓拍事件',
            value:'AlarmType'
        },{
            name:'抓怕时间',
            value:'SnapTime'
        },
        ])
        let tableDate=ref([])
        let Totles=ref(0)
        let leftform=ref({})
        let picimg=ref(null)
        let token=ref('')
        let player=ref(null)
        let delogs=ref(null)
        window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        })
        onMounted(()=>{
            bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
            // topecharts.forEach((item,index)=>{
            //     drawLiquidfill(item)
            // })
            // bieechart()
            gettops()
            // getdelogs()
        })
        const gettops=async()=>{
            // GetMonitorNumAndSnap
            const {data:res}=await gettable('GetMonitorNumAndSnap',getform.value)
                // console.log('获取',res);
                if (res.code=="1000") {
                    formecahrt.value=res.data
                    formecahrt.value.forEach((item,index)=>{
                        
                        if (item.name==topecharts.value[index].value) {
                            topecharts.value[index].value1=item.value
                        }
                    })
                    // console.log('获取顶部',topecharts.value);
                    
                    topecharts.value.forEach((item,index)=>{
                        drawLiquidfill(item)
                    })
                    // bieechart()

                }
        }
        // 获取弹窗
        const getdelogs=async()=>{
            loading.value=true
            formvideo.value.SnapRecodList=[]
            const {data:res}=await gettable('GetJKAndSnapRecodTable',getform.value)
            // console.log('获取列表',res);
            loading.value=false

            if (res.code=="1000") {
                formvideo.value=res.data
                Totles.value=res.Total
                leftform.value=res.data.SnapRecodList[0]
                bieechart()
                gettoken()

            }
        }
        const openleft=(row)=>{
            // console.log('点击',row);
            leftform.value=row
            bieechart()

        }
        const openmonitor=(val,index)=>{
            // console.log('点击',val,index);
            
            // title.value=val.value
            // delogs.value.open(val)
            if (index==0) {
                // title.value='监控数量'
                delogs.value.showdelog(val,'视频监控')

            }
        }
        const closes=()=>{
            dialogTableVisible.value=false
        }
        const pic=(val)=>{
            picimg.value.piclist(val)
        }
        const mouseenter=(e,index)=>{
        falge.value=index
        }

        const mouseleave=(e,index)=>{
            falge.value=-1

        }
        const open=(val)=>{
            getdelogs()
            dialogTableVisible.value=true
        }
        const close=()=>{
            dialogTableVisible.value=false
        }
        const gettoken=async(val)=>{
        // console.log('获取token',store.getters.username);
        
            let gettablesmodes={
                InUserName:store.getters.username,
                Type:'掌勤扬尘'
            }
            const {data:res}=await gettable('GetElevatorMonitoringToken',gettablesmodes)
            // console.log('获取token',res);
            if (res.code=="1000") {
                token.value=res.data.token
                playvioce(formvideo.value)
            }

        }
        const playvioce=(val)=>{
        // console.log('播放',val);
            if (player.value) {
              try {
                player.value.stop();
                if (typeof player.value.destroy === 'function') {
                  player.value.destroy();
                }
              } catch (error) {
                console.error('停止播放器时出错:', error);
              }
            }
            if (val.FluentLink) {
            player.value =  new EZUIKit.EZUIKitPlayer({
             autoplay: true,
             id:'morplay',
             accessToken:token.value,
             url:val.FluentLink,
            width:230,
            height:150,
            template: "simple", // simple - 极简版;standard-标准版;security - 安防版(预览回放);voice-语音版；
            handleError:(res)=>{
            console.log('播放错误回调',res);
            }
            });
            }

        }
        const clsoe=()=>{
            if (player.value) {
              try {
                player.value.stop();
                if (typeof player.value.destroy === 'function') {
                  player.value.destroy();
                }
                player.value = null;
              } catch (error) {
                console.error('关闭播放器时出错:', error);
              }
            }
        }
        const drawLiquidfill=(val)=> {
				// 基于准备好的dom，初始化echarts实例
				// console.log('获取',val);
                
				// let hygrometer = this.$echarts.init(this.$refs.hygrometer)
				// console.log('第一个图',this.datas);
				var echarts = require('echarts');
				var hygrometer = echarts.init(document.getElementById(val.name));
				// 使用指定的配置项和数据显示图表
				hygrometer.setOption({
					title: [{
						text: val.value1+val.unti,
						left: 'center',
						top: '33%',
						textStyle: {
							fontSize: '16',
							fontWeight: '400',
							color: val.textcolor,
						},
					}, ],
					tooltip: {
						show: false
					},
					series: [{
						name: '睡了',
						type: 'liquidFill',
						radius: '80px',
						data: [0.2, 0.2, ],
						label: {
							// normal: {
								show: false,
							// }
						},
						backgroundStyle: {
							color: '#042457', //图表的背景颜色
							//borderWidth: '10',//图表的边框宽度
							//borderColor: '#000',//图表的边框颜色
							itemStyle: {
								shadowBlur: 100, //设置无用
								shadowColor: '#f60', //设置无用
								opacity: 1 //设置无用
							}
						},
						color: [{
							type: 'linear',
							x: 0,
							y: 1,
							x2: 0,
							y2: 0,
							colorStops: [{
								offset: 1,
								color: [val.colorStops[0]], // 0% 处的颜色
							}, {
								offset: 0,
								color: [val.colorStops[1]], // 100% 处的颜色
							}],
							global: false // 缺省为 false
						}],
						outline: {
							show: true,
							borderDistance: 1,
							itemStyle: {
								borderColor: val.borderColor,
								borderWidth: 1
							}
						}
					}]
				})
			}
        const bieechart=()=>{
        var echarts = require('echarts');
        // console.log('获取',leftform.value);
        
        option.value = {
            series: [
                {
                type: 'gauge',
                startAngle:210,
                endAngle: -30,
                center: ['50%', '50%'],
                radius: '90%',
                min: 0,
                max: 1,
                splitNumber: 20,
                axisLine: {
                    lineStyle: {
                    width: 0,
                    color: [
                        [0.30,'#07B1CB'],
                        [1, '#ccc'],
                    ]
                    }
                },
                pointer: {
                    icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                    length: '12%',
                    width:0,
                    offsetCenter: [0, '-60%'],
                    itemStyle: {
                    color: 'auto'
                    }
                },
                axisTick: {
                    length: 12,
                    splitNumber:3,
                    lineStyle: {
                    color: 'auto',
                    width: 2,
                    cap:'round'
                    }
                },
                splitLine: {
                    length: 22,
                    distance: 3,
                    lineStyle: {
                    color: 'auto',
                    width: 3,
                    cap:'round'
                    }
                },
                axisLabel: {
                    color: '#07B1CB',
                    fontSize: 20,
                    show:false,
                    distance: -60,
                    rotate: 'tangential',
                },
                detail: {
                    fontSize: 30,
                    offsetCenter: [0, '-10%'],
                    valueAnimation: true,
                    formatter: function (value) {
                    return Math.round(value * 100) + '%';
                    },
                    color: 'inherit'
                },
                data: [
                    {
                    value: leftform.value.Similarity[0].value,
                    name: ''
                    }
                ]
                },
            ]
            };

        }
        const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
        }
        const handleSizeChange = (val) => {
            console.log(`${val} 显示多少页`)
            getform.value.count=val
            getdelogs()
            }
        const handleCurrentChange = (val) => {
            console.log(`选择第几: ${val}`)
            getform.value.page=val

            getdelogs()
            }
        return{
            bgcolor,
            getform,
            loading,
            dialogTableVisible,
            falge,
            option,
            imglist,
            topecharts,
            toptitles,
            plist,
            leftlable,
            tableDate,
            formecahrt,
            formvideo,
            Totles,
            leftform,
            picimg,
            token,
            player,
            title,
            delogs,

            closes,
            drawLiquidfill,
            mouseenter,
            mouseleave,
            open,
            close,
            bieechart,
            tableRowClassName,
            handleSizeChange,
            handleCurrentChange,
            gettops,
            getdelogs,
            pic,
            openleft,
            gettoken,
            playvioce,
            clsoe,
            openmonitor

        }
    }
}
</script>
<style lang="scss">
.homemonitor{
    margin-top: 50px!important;
    opacity: 1;
    box-sizing: border-box!important;
    .el-dialog__header{
        display: none!important;
    }
    .el-dialog__body{
        padding: 10px!important;
    }
    .el-table{
        --el-table-row-hover-bg-color:rgba(1, 194, 255, 0.6);
    }
    .el-table .warning-row {
        //   --el-table-tr-bg-color: #000 !important;
        background: rgba(15, 43, 63, 0.6)!important;
    }
    
}
</style>
<style lang="scss" scoped>
:deep(.delogss){
    margin-top: 5vh!important;
}
.monitor{
    position: relative;
    width: 100%;
    height: 100%;
    &-echarts{
        justify-content: center;
        align-items: center;
        display: grid;
        grid-template-columns: repeat(5,7%);
        &-one{
            display: flex;
            align-items: center;
            flex-direction: column;
        }
    }
    .morplay{
        width: 230px;
        height: 150px;
        border: 1px solid;
    }
}
.Similarity{
    width: 100%;
    height: 100%;
}
.Similaritysum{
    width: 100%;
    height: 100%;
    position: relative;
    &-p{
        position: absolute;
        bottom: 20%;
            left: 38%;
    font-size: 20px;
    font-weight: bold;
    }
}
.homemonitor-body-three{
        display: grid;
        grid-template-columns:30% 40% 30%;
        margin:10px 20px;
        justify-items: center;
    }
.homemonitor{
    &-one{
        display: grid;
        grid-template-columns:40% 60%;
        color: aqua;
    }
    &-p{
      padding: 10px;  
    }
    &-body{
        &-two{
            display: grid;
            grid-template-columns: repeat(2,50%);
            justify-items: start;
            p{
                padding: 10px;
            }
        }
    }
    .monitor1{
        grid-row: 1/2 span;
        grid-column: 2/2 span;
    }
    .tablezpevent{
        // gr
    }
    &-inform{
        display: grid;
        grid-template-columns: repeat(2,50%);
        // justify-items: center;
        justify-items: start;
        .morplay{
            grid-row: 2;
            grid-column: 1/2 span;
        }
    }
}
.imgpoting{
    position: absolute;
    width: 65px;
    height: 75px;
}
</style>