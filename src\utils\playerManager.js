/**
 * EZUIKit 播放器管理工具
 * 用于统一管理播放器的创建、销毁和错误处理
 */

export class PlayerManager {
  constructor() {
    this.players = new Map(); // 使用 Map 存储播放器实例
  }

  /**
   * 创建播放器实例
   * @param {Object} config - 播放器配置
   * @param {string} config.id - 播放器容器ID
   * @param {string} config.url - 播放地址
   * @param {string} config.accessToken - 访问令牌
   * @param {number} config.width - 播放器宽度
   * @param {number} config.height - 播放器高度
   * @param {string} config.template - 播放器模板
   * @param {Function} config.handleError - 错误处理回调
   * @returns {Object|null} 播放器实例或null
   */
  createPlayer(config) {
    const { id, url, accessToken, width = 300, height = 200, template = 'simple', handleError } = config;
    
    if (!id || !url || !accessToken) {
      console.error('播放器创建失败：缺少必要参数', { id, url, accessToken });
      return null;
    }

    try {
      // 如果已存在同ID的播放器，先销毁
      this.destroyPlayer(id);

      // 清理可能存在的旧容器
      this.cleanupPlayerContainer(id);

      // 创建新播放器
      const player = new EZUIKit.EZUIKitPlayer({
        autoplay: true,
        id,
        accessToken,
        url,
        width,
        height,
        template,
        handleError: (res) => {
          console.log(`播放器 ${id} 错误回调:`, res);
          // 播放出错时自动清理
          this.destroyPlayer(id);
          if (handleError && typeof handleError === 'function') {
            handleError(res);
          }
        }
      });

      // 存储播放器实例
      this.players.set(id, player);
      console.log(`播放器 ${id} 创建成功`);
      
      return player;
    } catch (error) {
      console.error(`创建播放器 ${id} 时出错:`, error);
      return null;
    }
  }

  /**
   * 销毁指定播放器
   * @param {string} id - 播放器ID
   * @returns {boolean} 是否成功销毁
   */
  destroyPlayer(id) {
    const player = this.players.get(id);
    if (!player) {
      return false;
    }

    try {
      console.log(`开始销毁播放器 ${id}`);
      
      // 停止播放
      if (typeof player.stop === 'function') {
        player.stop();
      }

      // 延迟销毁，确保停止操作完成
      setTimeout(() => {
        try {
          // 调用销毁方法
          if (typeof player.destroy === 'function') {
            player.destroy();
          }
          
          // 清理DOM容器
          this.cleanupPlayerContainer(id);
          
          console.log(`播放器 ${id} 销毁完成`);
        } catch (destroyError) {
          console.error(`销毁播放器 ${id} 时出错:`, destroyError);
        }
      }, 100);

      // 从Map中移除
      this.players.delete(id);
      return true;
      
    } catch (error) {
      console.error(`销毁播放器 ${id} 时出错:`, error);
      this.players.delete(id);
      return false;
    }
  }

  /**
   * 清理播放器DOM容器
   * @param {string} id - 播放器ID
   */
  cleanupPlayerContainer(id) {
    try {
      // 清理主容器
      const mainContainer = document.getElementById(id);
      if (mainContainer) {
        mainContainer.innerHTML = '';
      }

      // 清理可能的包装容器
      const wrapContainer = document.getElementById(id + '-wrap');
      if (wrapContainer && wrapContainer.parentNode) {
        wrapContainer.parentNode.removeChild(wrapContainer);
      }
    } catch (error) {
      console.error(`清理播放器 ${id} 容器时出错:`, error);
    }
  }

  /**
   * 停止指定播放器
   * @param {string} id - 播放器ID
   * @returns {boolean} 是否成功停止
   */
  stopPlayer(id) {
    const player = this.players.get(id);
    if (!player) {
      return false;
    }

    try {
      if (typeof player.stop === 'function') {
        player.stop();
        console.log(`播放器 ${id} 停止成功`);
        return true;
      }
    } catch (error) {
      console.error(`停止播放器 ${id} 时出错:`, error);
    }
    return false;
  }

  /**
   * 更换播放器URL
   * @param {string} id - 播放器ID
   * @param {string} url - 新的播放地址
   * @returns {boolean} 是否成功更换
   */
  changePlayerUrl(id, url) {
    const player = this.players.get(id);
    if (!player || !url) {
      return false;
    }

    try {
      if (typeof player.changePlayUrl === 'function') {
        player.changePlayUrl({ url });
        console.log(`播放器 ${id} URL更换成功`);
        return true;
      }
    } catch (error) {
      console.error(`更换播放器 ${id} URL时出错:`, error);
    }
    return false;
  }

  /**
   * 销毁所有播放器
   */
  destroyAllPlayers() {
    console.log('开始销毁所有播放器');
    
    const playerIds = Array.from(this.players.keys());
    playerIds.forEach(id => {
      this.destroyPlayer(id);
    });
    
    this.players.clear();
    console.log('所有播放器销毁完成');
  }

  /**
   * 获取播放器实例
   * @param {string} id - 播放器ID
   * @returns {Object|null} 播放器实例
   */
  getPlayer(id) {
    return this.players.get(id) || null;
  }

  /**
   * 检查播放器是否存在
   * @param {string} id - 播放器ID
   * @returns {boolean} 是否存在
   */
  hasPlayer(id) {
    return this.players.has(id);
  }

  /**
   * 获取所有播放器ID
   * @returns {Array} 播放器ID数组
   */
  getAllPlayerIds() {
    return Array.from(this.players.keys());
  }
}

// 创建全局实例
export const globalPlayerManager = new PlayerManager();

// 在窗口卸载时清理所有播放器
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    globalPlayerManager.destroyAllPlayers();
  });
}
