<template>
  <div class="home">
    <template v-if="tabtotlse=='智慧地磅过磅记录'">
      <img v-for="(item, index) in filteredImgList" 
        :key="index" 
        class="imgpoting cursor"
        :src="falge==index ? item.src1 : item.src" 
        :style="getPositionStyle(item)"
        alt=""
        @click="open(item)"
        @mouseenter="mouseenter(item, index)" 
        @mouseleave="mouseleave(item, index)">
    </template>
    
    <el-dialog v-model="dialogTableVisible" 
    class="homewqmit delogss" :width="showfalge==0?'30%':'70%'" >

    <selection ref="selection" @colses="closede" :titles="title"></selection>
    <div  v-if="showfalge==0" class="bodybottom">
        <div class="homewqmit-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
          <div class="homewqmit-body-one">
              <img src="@/assets/img/home/<USER>" alt="">
              <p>智慧地磅信息</p>
          </div>
          <div class="homewqmit-nut">
              <p class="cursor" @click="more(1)">智慧地磅</p>
          </div>
        </div>
        <div class="homewqmit-content">
            <p  v-for="(item,index) in form" :key="index">{{item.name}}：{{delogform[item.value]}}</p>
        </div>
        <div class="homewqmit-moner">
            <div v-for="(item,index) in tower" :key="index" :class="['homewqmit-two-btn cursor',{'changindex':falge1==index}]" 
            :style="[falge1==index?`background:${bgcolor.titlecolor};color:#FFF`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
            rgba(7, 93, 184, 0.6) 100%);color:#FFF`]" @click="change(item,index)"
            >{{item.name}}</div>
            <div style="width:300px;height:200px" class="playshart" id="playshart"></div>
        </div>
    </div>
    <!-- 地磅记录 -->
    <picimg ref="picimg"></picimg>
    </el-dialog>
    <delog1 ref="delogtbale1"></delog1>

  </div>
</template>

<script>
import { computed, nextTick, onMounted, reactive, ref,watch } from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import picimg from "@/components/connet/Common/picimg.vue";
import selection from "@/components/connet/Common/selection.vue";
import delog1 from "@/components/connet/material/content/delog.vue";
import { labelist } from "@/components/connet/material/content/lables.js";

export default {
props:['showcontent'],

components:{
        picimg,
        selection,
        delog1
        // datamodel
    },
setup(props,ctx){
    // 基础数据
    let imglist=ref([])                  // 图标列表数据
    let falgetitle=ref('')               // 标题标志
    let delogform=ref({})                // 对话框表单数据
    let token=ref('')                    // 认证令牌
    let falge=ref(-1)                    // 鼠标悬停索引
    let falge1=ref(0)                    // 选中标记
    let bgcolor=ref({})                  // 背景颜色配置
    let showfalge=ref(-1)                // 显示更多标记
    let loading=ref(false)               // 加载状态
    let dialogTableVisible=ref(false)    // 对话框可见状态
    let delogtbale1=ref(null)            // 表格引用
    let tabtotlse=ref('智慧地磅过磅记录') // 当前标签标题
    let amplifyindex=ref(0)              // 放大索引
    let picimg=ref(null)                 // 图片组件引用
    let bdcode=ref('')                   // 条码
    let tablelist = ref([])              // 表格数据列表
    let labeform=ref([])                 // 标签表单数据
    let title=ref('')                    // 标题
    let getplay=ref({})                  // 播放配置

    // 表单配置
    let getform=ref({
        ProjectCode:store.getters.code,  // 项目代码
        UsingPage:'物料管理',            // 使用页面
        IconType:'',                     // 图标类型
        DetialType:'',                   // 详情类型
        EquipCode:'',                    // 设备编码
        SupplierName:'',                 // 供应商名称
        MaterialClass:'',                // 材料类别
        ReceiveTime:'',                  // 接收时间
        page:1,                          // 页码
        count:10                         // 每页数量
    })
    let form=ref([
        {
            name:'磅房名称',
            value:'WaagName'
        },
        {
            name:'磅房编码',
            value:'WaagCode'
        },{
            name:'材料员',
            value:'Receiver'
        },{
            name:'手机号',
            value:'CellPhone'
        },
    ])
    let tower=ref([
        {
            name:'磅房监控',
            value:'MonitorAddr'
        },{
            name:'进磅监控',
            value:'EnterAddr'
        },{
            name:'出磅监控',
            value:'OutAddr'
        },{
            name:'俯视监控',
            value:'LookdownAddr'
        }
    ])
    // let labelserch=ref(['过磅日期','供货商','材料种类','材料名称'])
    const background = ref(false)

    // watch
    watch([()=>getform.value.ReceiveTime,()=>getform.value.SupplierName,()=>getform.value.MaterialClass], (newVal, oldVal) => {
        // console.log('监听数据',newVal);
        if (!newVal[0]) {
            getform.value.ReceiveTime=""
        }
      },
    {immediate: true})
    window.addEventListener('setthcolor', ()=> {
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--dialog-bg-color', bgcolor.value.delogcolor);
    
    })
    onMounted(()=>{
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        document.documentElement.style.setProperty('--title-color', bgcolor.value.titlecolor);
        document.documentElement.style.setProperty('--dialog-bg-color', bgcolor.value.delogcolor);
    
        getlocation()
    })
    const showdelog=(name,val)=>{
        console.log('查询',val);
        tabtotlse.value=name
        dialogTableVisible.value=true
        showfalge.value=1
        switch (name) {
            case '材料验收记录':
            // getform.value.MaterialClass=val.name
            delogtbale1.value.showdelog(val,'材料种类验收分析')

                break;
            case '供货商验收记录':
            // getform.value.SupplierName=val.name
            delogtbale1.value.showdelog(val,'供货商验收统计')

                break;
        }
        // getform.value=
        // getghstable()

    }

    const pic = (val) => {
        let imgtype = ['png', 'jpg', 'jpeg', 'gif', 'bmp']
        if (val) {
            let lastIndex = val.lastIndexOf('.')
            let file = val.substring(lastIndex + 1).toLowerCase()
            if (imgtype.includes(file)) {
                // console.log('获取',val);
                picimg.value.piclist(val)
            } else {
                let type = val.substring(val.lastIndexOf('.') + 1).toLowerCase()
                
                if (type !== 'mp4') {
                    window.open('https://f.zqface.com/?fileurl=' + val, '_slef')
                }
            }
        }
    }
    const getlocation=async()=>{
        let loction=[]
        const {data:res}=await gettable('GetHomePageIconPosition',getform.value)
        if (res.code=="1000") {
            loction=res.data
            // loction
            loction.forEach((item,index)=>{
                switch (item.IconType) {
                    case '智慧地磅':
                        item.src=require('@/assets/img/material/weighbridge.png')
                        item.src1=require('@/assets/img/material/weighbridge1.png')

                        break;
                }
                // item.
            });
            imglist.value=loction
        }
    }
    const geticonty=async(val)=>{
        // console.log('提交',getform.value,showfalge.value);
        let GUID={
            IconType:val.IconType,
            EquipCode:val.EquipCode,
            ProjectCode:store.getters.code,
        }
        getplay.value=GUID
        // showfalge.value=0
        getform.value.IconType=val.IconType
        getform.value.EquipCode=val.EquipCode
        const {data:res}=await gettable('GetMaterialManagerInfo',GUID)
        if (res.code=="1000") {
            delogform.value=res.data
            gettoken(res.data)
        }
    }
    const gettoken=async(val)=>{
        // console.log('获取token',val);
        
        let gettablesmodes={
            InUserName:store.getters.username,
            Type:''
        }
		let Type=store.getters.username
		gettablesmodes.Type='掌勤扬尘'
        const {data:res}=await gettable('GetElevatorMonitoringToken',gettablesmodes)
        // console.log('获取token',res);
        
        if (res.code=="1000") {
            token.value=res.data.token
            playvioce(val.MonitorAddr)
        }

    }
    // 监控
    const playvioce=(val)=>{
        // console.log('播放',val);
        
        let play =  new EZUIKit.EZUIKitPlayer({
				  autoplay: true,
				  id:'playshart',
				  accessToken:token.value,
				  url:val,
                  width:300,
                  height:200,
				  template: "simple", // simple - 极简版;standard-标准版;security - 安防版(预览回放);voice-语音版；
                  handleError:(res)=>{
                        console.log('播放错误回调',res);
                }
                });
    }
    const open=(item)=>{
        geticonty(item)
        falgetitle.value=item.IconType
        showfalge.value=0
        dialogTableVisible.value=true
            
    }
    /**
     * 处理鼠标进入图标事件
     * @param {Object} item - 图标数据
     * @param {Number} index - 图标索引
     */
    const mouseenter = (item, index) => {
        falge.value = index;
    };

    /**
     * 处理鼠标离开图标事件
     * 重置悬停状态
     */
    const mouseleave = () => {
        falge.value = -1;
    };
    // 更多
    const more=(val)=>{
        title.value='智慧地磅'
        delogtbale1.value.showdelog(getform.value,'智慧地磅')
        nextTick(()=>{
            dialogTableVisible.value=false
        })
    }
    const closede=()=>{
        dialogTableVisible.value=false
    }
    const amplifyopen=(val)=>{
        amplifyindex.value=val
        ctx.emit("getamplify", val);
    }
    const change=(val,index)=>{
        // console.log('获取',val);
        falge1.value=index
        playvioce(delogform.value[val.value])
    }

    /**
     * 根据传入的图标数据生成其位置样式
     * @param {Object} item - 图标数据对象
     * @returns {String} - CSS样式字符串
     */
    const getPositionStyle = (item) => {
      return `top:${item.YPosition}%;left:${item.XPosition}%`;
    };

    /**
     * 计算属性：根据条件过滤图片列表
     * 当amplifyindex为0时，只显示XPosition在20-80范围内的图标
     * 否则显示所有图标
     */
    const filteredImgList = computed(() => {
      if (amplifyindex.value === 0) {
        // 将字符串转换为数字进行比较，确保数据类型一致
        return imglist.value.filter(item => {
          const xPosition = parseFloat(item.XPosition);
          return !isNaN(xPosition) && xPosition > 20 && xPosition < 80;
        });
      } else {
        return imglist.value;
      }
    });

    return{
        delogform,
        picimg,
        tabtotlse,
        // bdcode,
        // pic,
        getform,
        // addform,
        // delogget,
        dialogTableVisible,
        amplifyindex,
        // MaterialClassDrops,
        token,
        falge1,
        form,
        bgcolor,
        showfalge,
        falgetitle,
        imglist,
        filteredImgList, // 导出计算属性
        falge,
        loading,
        background,
        tower,
        tablelist,
        labeform,
        title,
        getplay,
        delogtbale1,

        mouseenter,
        mouseleave,
        // getmore,
        close,
        open,
        more,

        amplifyopen,
        getlocation,
        change,
        geticonty,
        showdelog,
        closede,
        // getMaterial,
        // closes,
        // opens,
        pic,
        getPositionStyle,
    }
}
}
</script>
<style lang="scss">
.homewqmit{
    // margin-top: 2%!important;
    // background: rgba(2, 193, 253, 0.24)!important;
    opacity: 1;
    box-sizing: border-box!important;
    .el-dialog__header{
        display: none!important;
    }
    .el-dialog__body{
        padding: 10px!important;
    }
    .el-table{
        --el-table-row-hover-bg-color:rgba(1, 194, 255, 0.6);

    }
    .el-table .warning-row {
        //   --el-table-tr-bg-color: #000 !important;
        background: rgba(15, 43, 63, 0.6)!important;
    }
    #playshart-wrap{
        grid-row: 1/span 4;
        grid-column: 2;
    }
    
}
</style>
<style lang="scss" scoped>
.home{
    width: 100%;
    height: 100%;
    .btn{
    margin: 10px;
    width: 20%;
    font-size: 20px;
    font-weight: bold;
    }
}
.lable{
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    h2{
        grid-column: 1 /span 3;
        color: #fff;
    }
}
.bodybottom{
    text-align: left;

}
.text{
    color: #01C2FF;
    margin: 20px 10px;
}
.cursor{
    margin: 5px;
}
.el-scrollbar{
    grid-column: 1 /span 3!important;

}
.iconfont{
    font-size: 45px!important;
    position: absolute;
    left: 2%;
}
.imgpoting{
    position: absolute;
    width: 65px;
    height: 75px;
}
.tower:hover{
    background-image: url('@/assets/img/home/<USER>/tower1.png');
    width: 65px;
    height: 75px;
}
.homewqmit{
    .Record{
        line-height: 35px;
        color: #fff;
    }
    &-nut{
        p{
            display: inline-block;
            margin:0 5px;
        }
    }

    &-content{
        display: grid;
        grid-template-columns: repeat(2,50%);
        margin: 10px;
        height: 80px;
        p{
            text-align: start;
            padding: 10px;
            color: #fff;

        }
    }
    &-two-btn{
        position: relative;
        padding: 10px;
        grid-column: 1;
    }
    &-moner{
        display: grid;
        grid-template-columns: 20% 80%;
        // grid-template-rows: repeat(4,60px);
        align-items: center;
        justify-items: start;
        margin: 0;
    }
    #playshart-wrap{
        grid-row: 1/span 4!important;
        grid-column: 2!important;
    }
    // #playshart{
    //     grid-row: 1/4 span;
    //     grid-column: 1/2 span;
    // }
    .playshart{
        grid-row: 1/span 4;
        grid-column: 2;
    }
    .table-top{
        display: grid;
        grid-template-columns: repeat(3,33.3%);
        margin: 20px 10px;
        justify-items: start;
        color: #fff;
        span{
            margin: 10px;
        }
    }

    &-header{
        margin: 20px 0;
        width: 93%;
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        p{
            font-size: 18px;
            font-weight: bold;
        }
    }
    &-body-one{
        display: flex;
        align-items: center;
        
        }
.play{
    width: 260px;
    height: 150px;
    border: 1px solid #ccc;
}

}
.labdessoun{
color: #fff;
text-align: start;
}

</style>