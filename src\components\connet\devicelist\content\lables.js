export function labelist(params) {
    // 表单
    let lablist=[]
    let formcout=[
        {
         name:'告警记录列表',
         value:[{
            name:'序号',
            value:'rowNum',
            widths:'60',
            },{
            name:'告警日期',
            value:'warnDate',
            widths:'',
            },{
            name:'告警时间',
            value:'warnTime',
            widths:'',
            },{
            name:'设备类型',
            value:'EquipType',
            widths:'',
            },{
            name:'设备名称',
            value:'EquipName',
            widths:'',
            },{
            name:'告警类型',
            value:'WarnType',
            widths:'',
            },{
            name:'告警名称',
            value:'WarningDescribe',
            widths:'',
            }
         ]
        },{
        name:'告警记录列表查询',
        value:[{
              name:'告警日期',
              value:'WarnTime',
              type:3,
              widths:'150',
              },{
              name:'设备类型',
              value:'EquipType',
              type:2,
              list:[
                {name:'塔式起重机',value:'塔式起重机'},
                {name:'施工升降机',value:'施工升降机'},
                {name:'卸料平台',value:'卸料平台'}
              ],
              widths:'150',
              },{
              name:'设备名称',
              value:'EquipName',
              type:1,
              widths:'150',
              },{
              name:'告警类型',
              value:'WarnType',
              type:2,
              list:[
                  {name:'系统预警',value:'系统预警'},
                  {name:'违章操作',value:'违章操作'},
                  {name:'传感器报警',value:'传感器报警'},
                  {name:'系统报警',value:'系统报警'}
              ],
              widths:'150',
              },{
              name:'告警名称',
              value:'WarningDescribe',
              type:1,
              widths:'150',
              }
        ]},{
         name:'设备维保详情',
         value:[{
               name:'设备类型',
               value:'rowNum',
               widths:'',
               },{
               name:'设备名称',
               value:'EquipName',
               widths:'',
               },{
               name:'设备编号',
               value:'EquipCode',
               widths:'',
               },{
               name:'维保时间',
               value:'OperateDate',
               widths:'',
               },{
               name:'维保人',
               value:'Submitter',
               widths:'',
               },{
               name:'维保类型',
               value:'TypeName',
               widths:'',
               },{
               name:'维保部位',
               value:'PositionName',
               widths:'',
               },{
               name:'检查情况',
               value:'ConditionName',
               widths:'',
               },{
               name:'位置定位',
               value:'location',
               widths:'',
               },{
               name:'维保结果',
               value:'MaintainMode',
               widths:'',
               },{
               name:'备注',
               value:'Remarks',
               widths:'',
               },{
               name:'维保人员证件照',
               value:'MaintainAfterUrl',
               widths:'',
               img:'true'
               },{
               name:'维保过程照片',
               value:'MaintainAgoUrl',
               widths:'',
               img:'true'
               }
         ]
        }
        
    ]


    formcout.forEach((item,index)=>{
        if (item.name==params) {
            lablist=item.value
            
        }
    })
    
    return lablist
}