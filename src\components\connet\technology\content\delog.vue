<template>
    <el-dialog v-model="dialogTableVisible"   destroy-on-close
      class="delogss" :width="widths.includes(title)?'50%':'70%'">
      <selection ref="selections" @colses="closes" :titles="title"></selection>
      <div v-if="falge=='0'" class="datedelog bodybottom bules" :style="getstyle">
          <h2 v-if="toptitle">{{title}}</h2>
          <div class="serch" v-for="(item,index) in serch" :key="index">
              <span>{{item.name}}:</span>
              <el-input v-if="item.type==1" v-model="getform[item.value]" :style="getwidth(item)"  placeholder="请输入关键字" size="small" clearable></el-input>
              <el-date-picker v-if="item.type==3" v-model="getform[item.value]" type="date" size="small" placeholder="选择日期"
               format="YYYY-MM-DD" value-format="YYYY-MM-DD"  :style="getwidth(item)"  />
              <el-select v-else-if="item.type==2" popper-class="bules" size="small" :popper-append-to-body="true"
               clearable :style="getwidth(item)" v-model="getform[item.value]" 
                class="m-2" placeholder="请选择" @click="getMaterial(item)">
                      <el-option  v-for="(items,index) in item.list" :key="index"
                      :label="items.name" :value="items.value" />
                  </el-select>
          </div>
          <el-button type="primary" size="small" @click="search">搜索</el-button>
          <el-table :data="tableDate" class="cursor" :style="['width: 100%',`color:${bgcolor.font};
              --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
              :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}" max-height="550px"
              empty-text="暂无数据"
              >
              <template #empty>
                  <el-empty  v-loading="loading"></el-empty>
              </template>
              <el-table-column v-for="(item,index) in labelist1" :key="index" 
              :width="item.widths" :prop="item.value" :label="item.name" align="center"> 
              <template #default="scope">
                  <span v-if="item.type=='1'" :style="getcolor(item,scope.row)">{{scope.row[item.value]}}</span>
                  <img v-else-if="item.img&&scope.row[item.value]" :src="scope.row[item.value]" alt="" style="width: 80px;height: 80px;" class="cursor" @click="pic(scope.row[item.value])">
                  <el-button v-else-if="filelist.includes(item.name)" type="primary" text size="small" @click="pic(scope.row[item.value1])">{{scope.row[item.value]}}</el-button>
                  <span v-else>{{scope.row[item.value]}}</span>
              </template>
              </el-table-column>
              <el-table-column label="操作" align="center" v-if="opter" width="100">
                  <template #default="scope">
                      <el-button type="primary" text size="small" @click="open(scope.row)">查看</el-button>
                  </template>
              </el-table-column>
          </el-table>
          <el-pagination  v-model:current-page="getform.page" popper-class="bules" v-model:page-size="getform.count" 
          :page-sizes="[10, 20, 40, 50, 100]"  :background="false" layout="total, sizes, prev, pager, next, jumper"
          :total="total"  @size-change="handleSizeChange"  @current-change="handleCurrentChange"/>
      </div>
      <div v-else :class="`datedelog bodybottom bules ${widths.includes(title)?'lables':'lable'}`" :style="getstyle">
          <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
            <div class="datedelog-body-one">
                <img src="@/assets/img/home/<USER>" alt="">
                <p class="datedelog-p">{{dates+title}}</p>
                <div class="cursor-box" v-if="username.includes(getform.InUserName)">
                    <el-icon class="cursor icon" :style="getcolors(count,0)" @click="getnext(list,0)" :disabled="count==0"><ArrowLeftBold /></el-icon>
                    <span>{{count+1}}/{{list.length}}</span>
                    <el-icon class="cursor icon" :style="getcolors(count,1)" @click="getnext(list,1)" :disabled="count==list.length-1"><ArrowRightBold /></el-icon>
                </div>
            </div>
            </div>
          <div v-for="(item,index) in labeform" :class="[tilelist.includes(item.name)?'':'text', item.type=='1'?'full-width':'']" :key="index" :style="getcloume(item)">
              <h2 v-if="tilelist.includes(item.name)" class="h2">{{item.name}}</h2>
              <span v-else-if="item.type=='0'&&item.name">{{item.name}}：</span>
              <span v-if="item.type=='0'">{{addform[item.value]}}</span>
              <img v-else-if="item.img" :src="addform[item.value]" class="cursor" @click="pic(addform[item.value])" alt="" style="width:180px;height:180px">

              <div v-if="item.type=='1'" class="table">
                <el-table :data="addform[item.value]" class="cursor" :style="['width: 100%',`color:${bgcolor.font};
                --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
                :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}" max-height="200px"
                empty-text="暂无数据" @row-click="rowopen"
                > 
                    <template #empty>
                        <el-empty  v-loading="loading"></el-empty>
                    </template>
                    <el-table-column v-for="(item,index) in smalltable" :key="index" 
                    :width="item.widths" :prop="item.value" :label="item.name" align="center"
                    show-overflow-tooltip>
                    </el-table-column>
                </el-table>
              </div>
          </div>
      </div>
      <picimg ref="picimgs"></picimg>
      <delogshow ref="delogshows"></delogshow>
      </el-dialog>
  </template>
  
  <script setup>
  
  import selection from "@/components/connet/Common/selection.vue";
  import picimg from "@/components/connet/Common/picimg.vue";
  import { onMounted, ref, computed } from 'vue';
  import { labelist } from "@/components/connet/technology/content/lables.js";
  import { gettable,setdata,deldata} from "@/network/api/requestnet";
  import store from "@/store";
  import { updateFormListFields } from '@/utils/dataTransform'
  import  delogshow  from "@/components/connet/technology/ecahrtline/delog.vue";
  
  let dialogTableVisible=ref(false)
  let bgcolor=ref({})
  let title=ref('')
  let tableDate=ref([])
  let loading=ref(false)
  let labelist1=ref([])
  let serch=ref([])
  let total=ref(0)
  let titleist=[]
  let falge=ref(0)//0为供货商验收统计，1为详情
  let tilelist=['基本信息','方案附件列表','审批信息','交底附件列表','附件列表']
  let getform=ref({
      page:1,
      count:10,
      CreateTime:'',
      GetSpecialProgramTable:'',
      ProjectCode:store.getters.code,
      InUserName:store.getters.username
  
  })
  let count=ref(0)
  let widths=['施工日志详情']
  let url=ref('')
  // 添加历史记录数组，用于存储页面状态
  let history=ref([])
  // 添加当前导航层级记录
  let currentLevel=ref(0)
  // 添加物料验收记录标记
  let isMaterialRecord=ref(false)
  let mater={
      url:'',
      title:'',
      value:''
  }
  let labeform=ref([])
  let onelist=['运单编号','偏差情况','基本信息','备注','方案附件列表','审批信息',
  '交底附件列表','附件列表','记录内容','气温','照片','尚待解决问题']
  let twolist=[]
  let geturl=''
  let addform=ref({})
  let picimgs=ref(null)
  let tablelist=ref([])
  let opter=true
  let Twolayers=['施工方案']
  let toptitle=ref('')
  let smalltable=ref([
    {
    name:'文件名称',
    value:'FileName',
    widths:'',
    },{
    name:'文件大小',
    value:'FileSize',
    widths:'',
    },{
    name:'上传人',
    value:'Uploader',
    widths:'',
    },{
    name:'上传时间',
    value:'UploadTime',
    widths:'',
    }
  ])
  let filelist=['关联实施方案','文件']
  let delogshows=ref(null)
  let username=ref(['新盛建设740'])
  let dates=ref('')
  let list=ref([])

  window.addEventListener('setthcolor', ()=> {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
  })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getform.value.ProjectCode=store.getters.code
  })
  const showdelog=(val,titleText)=>{
      console.log('titleText',val,titleText);
      toptitle.value=''
      title.value=titleText
      titleist[0]=titleText
      falge.value=0
      opter=true
    //   labelist1.value=labelist(titleText)
      // 初始化历史记录和导航层级
      history.value = []
      currentLevel.value = 0
      // 重置物料验收记录标记施工日志列表
      isMaterialRecord.value = false
      
      switch (titleText) {
          case '施工方案':
              url.value='GetSpecialProgramTable'
              serch.value=labelist('施工方案列表查询')
              labelist1.value=labelist('施工方案列表')
              break;
          case '技术交底':
              url.value='GetDisclosureTable'
              serch.value=labelist('施工方案列表查询')
              serch.value[1].value='DisclosureName'
            //   serch.value[1].value=''
              labelist1.value=labelist('技术交底列表')
              break;
          case '施工日志':
              url.value='GetConstructionLogTableInfo'
              serch.value=labelist('施工方案列表查询')
              serch.value[1].value='RecordContent'
              labelist1.value=labelist('施工日志列表')
              break;
           case '会议纪要':
              url.value='GetMeetingSummaryTable'
              serch.value=labelist('施工方案列表查询')
              serch.value[0].value='MeetingTime'
              labelist1.value=labelist('会议纪要列表')
              break;
            }
      getdetable()
      dialogTableVisible.value=true
  }
  
  const search=()=>{
      // console.log('搜索',getform.value);
      getform.value.page=1
      getform.value.count=10
      getdetable()
  }
  // 获取材料下拉框
  const getMaterial=async(val)=>{

  }
   const pic=(val)=>{
      let imgtype=['png','jpg','jpeg','gif','bmp']
      if (val) {
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1).toLowerCase()
          if (imgtype.includes(file)) {
              // console.log('获取',val);
              picimgs.value.piclist(val)
          }else{
              let type = val.substring(val.lastIndexOf('.') + 1).toLowerCase()
              
              if (type !== 'mp4') {
                  window.open('https://f.zqface.com/?fileurl='+val,'_slef')
              }
  
          }
      }
  }
  const getcloume=(val)=>{
      // 检查当前是否为两列布局
      const isTwoColumnLayout = widths.includes(title.value);

      // 如果 val.name 在 onelist 数组中，根据布局类型合并列
      if (onelist.includes(val.name)) {
          return isTwoColumnLayout ? 'grid-column: 1 /span 2' : 'grid-column: 1 /span 3';
      }
      // 如果 val.name 在 twolist 数组中，合并两列
      else if (twolist.includes(val.name)) {
          return 'grid-column: 1 /span 2';
      }
      // 如果都不在，则不合并列
      return '';
  }
  const rowopen=(row)=>{
    // console.log('行',row);
    pic(row.FileUrl)
  }
   const getdetable=async()=>{ 
      loading.value=true
      const {data:res}=await gettable(url.value,getform.value)
      // console.log('获取数据',res);
      loading.value=false
      tableDate.value=res.data
      total.value=res.Total || 0
   }
  //  获取详情
   const getdetil=async(val)=>{
      let GUID={
          GUID:val.GUID,
          ConstructionDate:val.Date||val.ConstructionDate,
          ProjectCode:store.getters.code,
      }
      const {data:res}=await gettable(geturl,GUID)
    //   console.log('获取数据',res);
      if (res.code=='1000') {
          addform.value = Array.isArray(res.data) ? res.data[0] : res.data
          list.value=res.data

      }
   }
    // 获取下一页
    const getnext=(val,index)=>{
            if(list.value.length>=1){
                if(index === 0 && count.value > 0) {
                    count.value--;
                } else if(index === 1 && count.value < list.value.length-1) {
                    count.value++;
                }
                addform.value=list.value[count.value]
            }
    }
    const getcolors=(val,index)=>{
            if((index === 0 && count.value === 0) || (index === 1 && count.value === list.value.length-1)){
                return 'color: #CCC;'
            }else{
                return 'color: #01C2FF;'
            }
        }
   const getstyle=()=>{
      return `border:2px solid ${bgcolor.value.titlecolor};
      background:rgba(${bgcolor.value.delogcolor},0.35)`
   }
   const getwidth=(val)=>{
      if (val.widths) {
          return `width: ${val.widths}px`
      }else{
          return 'width: 200px'
      }
   }
   // 显示磅单详情的公共函数
   const showReceiptDetail = (row) => {
    //   falge.value = 1
    //   title.value = ` ${row.ReceivingCode}`
    //   labeform.value = labelist('施工方案详情')
      
      getdetil(row)
      return true // 返回true表示已处理，调用处可以直接return
   }
   
   const open=(row)=>{
      console.log('查看', row);
      opter=true
      toptitle.value=''
      // 如果是物料验收记录且当前已经是第二层，不再增加导航层级
      if (isMaterialRecord.value && currentLevel.value >= 1 ) {
          if (showReceiptDetail(row)) return // 使用公共函数并直接返回
      }
      // 保存当前页面状态到历史记录
      saveCurrentState();
      // 增加导航层级
      currentLevel.value++;
      switch (title.value) {
          case '施工方案':
              labeform.value = labelist('施工方案详情');
              falge.value=1
            //   console.log('供货商列表',row.ProgrammeName);
              geturl='GetSpecialProgramDetailInfo'
              title.value = row.ProgrammeName;
              toptitle.value=row.ProgrammeName
            showReceiptDetail(row)
              break;
          case '技术交底':
              labeform.value = labelist('技术交底详情');
              falge.value=1
              geturl='GetDisclosureDetailInfo'
              title.value = row.DisclosureName;
              toptitle.value=row.DisclosureName
            showReceiptDetail(row)
              break;
          case '施工日志':
            falge.value=1
            // delogshows.value.showdelog(0,row)
            labeform.value = labelist('施工日志详情');
            // console.log('施工日志',labeform.value);
            geturl='GetConstructionLogWeatherDetail'
            title.value = '施工日志详情';
            dates.value=row.ConstructionDate || row.Date
            toptitle.value=row.ConstructionDate
            getdetil(row)
              break;

          default:
              if (currentLevel.value >= 2) {
                  if (row.GUID) { 
                      if (showReceiptDetail(row)) return // 使用公共函数并直接返回
                  } else {
                      title.value = `${title.value} - 详情`;
                  }
              }
              break;
      }
    //   getdetable();
  }
   const getcolor=(val,row)=>{
      if (val.name=='偏差情况') {
          if (row.Deviation=='负偏差') {
              return 'color:  #01C2FF'
          }else if(row.Deviation=='超负差'){
              return 'color:  red'
          }else{
              return 'color:  #008000'
          }
      }
      return ''
   }
  
   // 保存当前页面状态到历史记录的函数
   const saveCurrentState = () => {
      history.value.push({
          title: title.value,
          labelist1: [...labelist1.value],
          serch: [...serch.value],
          url: url.value,
          getform: {...getform.value},
          tableDate: [...tableDate.value],
          total: total.value,
          level: currentLevel.value
      });
      // console.log('保存状态:', title.value, '层级:', currentLevel.value);
   }
  
  /**
   * 恢复页面状态的辅助函数
   * @param {Object} state - 要恢复的状态对象
   * @param {boolean} clearHistory - 是否清空历史记录
   * @param {number} newLevel - 要设置的新导航层级
   */
  const restoreState = (state, clearHistory = false, newLevel = null) => {
      // 恢复基础状态
      falge.value = 0;
      opter = true;
      // 恢复所有页面状态
      title.value = state.title;
      labelist1.value = state.labelist1;
      serch.value = state.serch;
      url.value = state.url;
      getform.value = state.getform;
      tableDate.value = state.tableDate;
      total.value = state.total;
      currentLevel.value = newLevel !== null ? newLevel : state.level;
      if (clearHistory) {
          history.value = [];
      }
      
      // 重新获取数据
      getdetable();
  };
  
  const closes = () => {
      // 特殊情况：物料验收记录的详情页直接返回第一层
      if (isMaterialRecord.value && falge.value === 1 && history.value.length > 0) {
          const firstLayerState = history.value.pop();
          restoreState(firstLayerState, true, 0);
          return;
      }
      
      // 常规情况：根据历史记录决定返回上一页或关闭对话框
      if (history.value.length > 0) {
          const prevState = history.value.pop();
          restoreState(prevState);
      } else {
          // 没有历史记录，关闭对话框
          dialogTableVisible.value = false;
      }
  };
  
  const tableRowClassName=({row,rowIndex,})=>{
          // console.log('获取当前行',row,rowIndex);
          if (rowIndex%2 != 0) {
              return 'warning-row'
          }
          return ''
      }
  const handleSizeChange = (val) => {
      console.log(`${val} 显示多少页`)
      getform.value.count=val
      getform.value.page=1
      getdetable()
      }
  const handleCurrentChange = (val) => {
      console.log(`选择第几: ${val}`)
      getform.value.page=val
      getdetable()
      }
  
  defineExpose({
    showdelog
  })
  </script>
  <style lang="scss" scoped>
  .datedelog-header{
    grid-column: 1/span 2;
  }
  .serch{
      color: #fff;
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
  }
  .bodybottom{
      text-align: left;
      h2{
          color: #fff;
          margin-bottom: 10px;
      }
  }
  .h2{
    //   color: #01C2FF;
    // grid-column: 1 /span 3;

  }
//   .round{
//       width: 10px;
//       height: 10px;
//       border-radius: 50%;
//       background-color: #01C2FF;
//   }
.lables{
    display: grid;
    grid-template-columns: 1fr 1fr;
    color: #fff;
    .text{
    color: #fff;
    margin: 10px;
    }
}
  .lable{
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      h2{
          grid-column: 1 /span 3;
          color: #fff;
      }
      
      /* 确保表格容器在网格中正确占据空间 */
    //   div[class*="table"] {
    //       grid-column: 1 /span 3;
    //       width: 100%;
    //       overflow-x: auto;
    //   }
  }

  .text{
      color: #01C2FF;
      margin: 20px 10px;
      margin-left: 100px;
  }
  .cursor{
      margin: 5px;
  }
  .el-scrollbar{
      grid-column: 1 /span 3!important;
  
  }
  /* 添加新的CSS类 */
//   .full-width {
//       grid-column: 1 /span 3 !important;
//       width: 100%;
//   }
  </style>