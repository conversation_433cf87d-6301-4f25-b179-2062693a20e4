<template>
  <div :id="ids" class="qwtj" :ref="refsid" :style="{width:'100%',height:'100%'}"></div>
</template>
<script>
import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
export default {
props:['ids','heights','refsid','options1','IDstyle'],
setup(props, context){
    let refsid=ref('')
    onMounted(()=>{
        nextTick(()=>{
            getecharts()
        })
    })
    // console.log('获取数据',props.heights);
    // calc(40vh-120px)
    
    const getecharts=(val)=>{
        var echarts = require('echarts');
       let myChart = echarts.init(document.getElementById(props.ids));

        //  myChart = echarts.getInstanceByDom(
        //   context.refs[refsid.value]
        // );
        // if (myChart == null) {
        //   myChart = echarts.init(context.refs[refsid.value]);
        // }
        // myChart = echarts.init(document.getElementById(ids));
        // let options=ref(null)
        // options.value=val
        myChart.setOption(props.options1);
        window.addEventListener("resize", function() {
          myChart.resize();
        });
    }
    onBeforeUnmount(()=>{
      myChart.dispose();

    })
    return{
        refsid,
        getecharts
    }
}
}
</script>
<style lang="scss" scoped>
.qwtj{
  width: 100%;
  height: 100%;
}
</style>