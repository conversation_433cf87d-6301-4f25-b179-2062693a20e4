<template>
  <!-- 在场 -->
  <div class="presence padding" :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"></Chamfering>
    
    <div class="presence-two">
      <div class="presence-two-top">
        <div class="presence-two-top1">
          <span>在场人数：{{forms.zcrs}}</span>
          <span>考勤人员：{{forms.kqrs}}</span>
          <span style="color:red"> 考勤率：{{forms.kqrate}}%</span>
        </div>
      </div>
      <div class="echatr">
        <presenceechart  :ids="'onchangeechart'" @popsdelog="popsdelog" :options1="lineoptions"></presenceechart>
      </div>
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    <delog3 ref="delog3"></delog3>
  </div>
</template>

<script>
import presenceechart from "@/components/connet/Common/echartscom.vue";
import { nextTick, onMounted, reactive, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog3 from "@/components/connet/personnelcon/content/perdelog.vue";

export default {
// props:['homeindex'],
components:{
  presenceechart,
  Chamfering,
  delog3
},
setup(){
   let bgcolor=ref({})
    let lineoptions=ref({})
    let delog3=ref(null)  // 添加 delog3 的 ref 声明

    let getform=ref({
      ProjectCode:store.getters.code,

    })
    let quliet=ref([])
    let falge=ref(false)
    let forms=ref({})
    let topforms=ref({
      url:require('@/assets/img/personnelcon/presence.png'),
      name:"在场人员变动情况"
    })
  window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
    // getecharts1()
    gettableqe()
  })
  const popsdelog=(val)=>{
    nextTick(()=>{
      // console.log('点击',val.seriesName);
      // falge.value=true
        // if (val.seriesName=='考勤人数') {
          
        // }else
        delog3.value.showdelog(val,val.seriesName)
      // }
    })
    // delog3.value.showdelog(val,'班组考勤统计')

  }
  const gettableqe=async()=>{
    // falge.value=false
    const {data:res}=await gettable('WorkerAttendance',getform.value)
        // console.log('返回数据',res);
        quliet.value=res.data
          forms.value=res
        // nextTick(()=>{
          // falge.value=true
          getecharts1()

          // falge.value=true
        // })
  }
  const getecharts1=()=>{
      let KQCount=[]//65,54,62
      let ZCCount=[]
      // let addpro2=[]
      let AttendanceDay=[]
      // console.log('获取数据');
      
      // console.log('获取数据',quliet.value[0]);
    if (quliet.value.length>0) {
      // console.log('获取在场人数',quliet.value);
      
    KQCount= quliet.value.map((item)=>{
      return item.KQCount
      })
    ZCCount= quliet.value.map((item)=>{
      return item.ZCCount
      })
    AttendanceDay=quliet.value.map((item)=>{
      return item.AttendanceDay
      })
    // //   // console.log('虎丘',);
      
    }
    // console.log('在场人数',ZCCount,KQCount,AttendanceDay);
    
     lineoptions.value={
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        itemGap: 24,
        textStyle: {
						fontSize: '12px',
						color: '#A8D6FF',
					},
        data: ['在场人数', '考勤人数']
      },
      grid: {
        top:'20%',
        left: '3%',
        right: '5%',
        bottom: '0',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLine: {
						lineStyle: {
							color: 'rgba(3, 251, 255, 0.3)'
						}
            
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						interval: 1,
						// textStyle: {
							color:'#03FBFF',
						// },
						// 默认x轴字体大小
						fontSize: 12,
						// margin:文字到x轴的距离
						margin: 15
					},
        data: AttendanceDay
      },
      yAxis: {
        type: 'value',
        axisTick: {
						show: false
					},
					axisLabel: {
						// textStyle: {
							color: '#03FBFF'
						// }
					},
					 splitLine :{    //网格线
              lineStyle:{
                  type:'dashed',
                  color:'rgba(3, 251, 255, 0.3)'   //设置网格线类型 dotted：虚线   solid:实线
              },
              show:true //隐藏或显示
          }
      },
      series: [
        {
          name: '在场人数',
          type: 'line',
          stack: 'Total',
          color:'#03FBFF',
          data: ZCCount
        },
        {
          name: '考勤人数',
          type: 'line',
          stack: '',
          color:'#C80202',
          data: KQCount
        },
      ]
    }
  }

return{
  // heights,
  lineoptions,
  forms,
  falge,
  quliet,
  getform,
  bgcolor,
  topforms,
  delog3,

  getecharts1,
  gettableqe,
  popsdelog
}
}
}
</script>
<style lang="scss" scoped>
.presence{
  &-two{
    height: 80%;
    &-top{
      height: 15%;
    }
    &-top1{
      font-size: 12px;
      color: #A8D6FF;
      margin: 5px;
      height: 15%;
      span{
        display: inline-block;
        width: 28%;
        margin: 5px;
      }
    }
  }
}
// #changeechart{
//   width: 100%;
//   // height: 100%;
//   height:calc(21.5vh - 20px)
// }
.echatr{
  width: 100%;
  height: 85%;
  // height: 173px;
  // height: calc(21.5vh - 20px);
}
</style>