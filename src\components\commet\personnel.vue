<template>
  <div class="model" v-if="destorys">
    <div class="leftmodel left wid"  v-show="amplify==0">
        <Attendance  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :materialtype="materialtype"></Attendance>
         <teamslistss  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :teamtype="Typeworlist"></teamslistss>
         <teamslistss  class="Homebgco" :style="`background:linear-gradient(90deg, ${bgcolor.bgcolor} 7%,
         rgba(0,52,75,0.00) 97%)`" :teamtype="teamtype"></teamslistss>
    </div>
    <div class="homecontent"  :style="amplify==0?'width:60%':'width:100%'">
      <concer  @getamplify1="getamplify1"></concer>
    </div>
    <div class="rightmodel right wid"  v-show="amplify==0">
      <presence class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        ></presence>
      <Agepersonnel class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        ></Agepersonnel>
      <documentwaring class="Homeright" 
        :style="`background:linear-gradient(90deg,rgba(1, 194, 255, 0) 0%,${bgcolor.bgcolor} 97%)`" 
        ></documentwaring>
    </div>

  </div>
</template>

<script>
import Attendance from "@/components/connet/personnelcon/Attendance.vue";
// import Typeworkss from "@/components/connet/personnelcon/Typeworkss.vue";
import teamslistss from "@/components/connet/personnelcon/teamslist.vue";
import presence from "@/components/connet/personnelcon/presence.vue";
import documentwaring from "@/components/connet/personnelcon/documentwaring.vue";
import Agepersonnel from "@/components/connet/personnelcon/Agepersonnel.vue";
import concer from "@/components/connet/personnelcon/content/concer.vue";


import { onMounted, ref ,computed, onUnmounted, onBeforeUnmount} from 'vue';
import { onBeforeRouteLeave } from 'vue-router';

export default {
components:{
        Attendance,
        // Typeworkss,
        teamslistss,
        presence,
        documentwaring,
        Agepersonnel,
        concer
    },
setup(){
    let themelist=ref([])

    let bgcolor=ref({})
    let amplify=ref(0)
    let materialtype=ref({
        src:require('@/assets/img/home/<USER>'),
        titles:'在场人数统计',
        type:'人员',
        ids:''
    })
    let teamtype=ref({
        src:require('@/assets/img/personnelcon/teamslist.png'),
        titles:'班组考勤统计',
        type:'人员',
        ids:'workecharts'
    })
    let Typeworlist=ref({
        src:require('@/assets/img/personnelcon/Typeworkss.png'),
        titles:'工种考勤分析',
        type:'人员',
        ids:'Typeworkcharts'
    })
    let destorys=ref(true)
    window.addEventListener('setItem', ()=> {
      // console.log('项目引导');
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
   })
   window.addEventListener('setthcolor', ()=> {
      // console.log('主题切换');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(()=>{
      destorys.value=true

      // let divs =document.querySelector('.leftmodel')
      themelist.value=JSON.parse(sessionStorage.getItem('theme'))
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    })
    const getamplify1=(val)=>{
      // console.log('放大',val);
      
      amplify.value=val
    }
    // onBeforeUnmount(()=>{
    //   console.log('销毁',show.value=false);

    // })
    // onBeforeRouteLeave
  onBeforeUnmount(()=>{
      destorys.value=false

  })
    return{
        destorys,

        bgcolor,
        themelist,
        amplify,
        materialtype,
        teamtype,
        Typeworlist,
        getamplify1
    }
}
}
</script>
<style lang="scss" scoped>
@mixin homeflex($deg) {
  height: 31.3%;
  width: 95%;
  opacity: 1;
  margin:7px 10px;
  background: linear-gradient($deg, #0096C7 7%, rgba(0,52,75,0.00) 97%);
}
@mixin leftdjx($left,$right,$top,$bottom,) {
  position: absolute;
  content: '';
  display: block;
  left: $left;
  top: $top;
  right: $right;
  bottom: $bottom;
  width: 10.06px;
  height: 10.84px;
  opacity: 1;
}

.Homebgco{
  position: relative;
  @include homeflex(90deg)
  }
.Homeright{
  position: relative;
  @include homeflex(-90deg);

}
</style>
<style lang="scss" scoped>
.model{
  width: 100%;
  height: 100%;
  display: flex;
  .wid{
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .homecontent{
    width: 60%;
    height: 100%;
    // position: relative;
  }
}
</style>