<template>
  <el-menu :default-active="activeIndex" class="el-menu-demo"
    mode="horizontal" @select="handleSelect" >
    <el-menu-item v-for="(item,index) in menus" :index="item.value" :key="index">Processing Center</el-menu-item>
  </el-menu>
</template>
<script setup>
import { ref } from 'vue'
let activeIndex = ref('1')
// let store = useStore();
let menus=[
  {
    name:'首页',
    value:'index'
  },
  {
    name:'人员管理',
    value:'personnel'
  },
  {
    name:'设备管理',
    value:'device'
  },
  {
    name:'物料管理',
    value:'material'
  },
  {
    name:'生产技术',
    value:'produce'
  },
  {
    name:'绿色施工',
    value:'construction'
  },
  {
    name:'质量安全',
    value:'qure'
  },
  {
    name:'智能监控',
    value:'monitor'
  },
  {
    name:'党建管理',
    value:'building'
  },{
    name:'后台',
    value:'4'
  }
]
const handleSelect=(val)=>{
    console.log('导航选择',val);
    
}
</script>
<style lang="scss" scoped>

</style>