<template>
    <el-dialog v-model="dialogTableVisible"   destroy-on-close
      :class="`delogss `"
      :width="widths.includes(title)?'50%':'70%'"
      :style="martop.includes(title) ? 'margin-top: 15vh !important;' : ''">
      <selection ref="selections" @colses="closes" :titles="title" ></selection>
      <div :class="`datedelog bodybottom bules ${getclass()}`" :style="computedStyle">
          <div v-if="title=='天气状况'" class="condition">
           <h2>{{ dates}} {{ city }}<span class="lunar-date">{{ lunarDate }}</span></h2>
           <div class="weaters">
                <div v-for="(item,index) in weaterlist" :key="index" class="weaters-tt">
                    <span>{{item.weekDay}}</span>
                    <img :src="`https://ai-zqface-com-wjgl.oss-cn-hangzhou.aliyuncs.com/fillist/Realsystem/icons/${item.iconDay}.png`" 
                    alt="" style="width: 70px;height: 70px;">
                    <span>{{item.tempMin}}°C~{{item.tempMax}}°C</span>
                    <span>{{item.textDay}}</span>
                    <span>{{item.windDirDay+item.windScaleDay}}</span>
                </div>
           </div>
          </div>
          <div v-if="title=='基础信息'" class="btns">
                <el-button  v-for="(item,index) in btns" :key="index" style="width: 100px;"
                 :type="falge==index?'primary':'default'" @click="getbtns(item,index)">{{item}}</el-button>
          </div>
          <div v-if="falge=='1'" class="table full-width">
              <template v-for="(item,index) in serch" :key="index">
                  <div class="serch" v-if="item">
                      <span>{{item.name}}:</span>
                      <el-input v-if="item.type==1" v-model="getform[item.value]" :style="'width:240px'"  placeholder="请输入关键字" size="small" clearable></el-input>
                      <el-date-picker v-if="item.type==3" v-model="getform[item.value]" type="date" size="small" placeholder="选择日期"
                      format="yyyy-MM-dd" value-format="yyyy-MM-dd"  :style="'width:240px'"  />
                      <el-select v-else-if="item.type==2" popper-class="bules" size="small" :popper-append-to-body="true"
                      clearable :style="'width:240px'" v-model="getform[item.value]"
                        class="m-2" placeholder="请选择" >
                              <el-option  v-for="(items,index) in item.list || []" :key="index"
                              :label="items.name" :value="items.value" />
                          </el-select>
                  </div>
              </template>
                <el-button type="primary" v-if="btn1.includes(title)" size="small" @click="search">搜索</el-button>
                <el-table :data="tableDate" class="cursor" :style="['width: 100%',`color:${bgcolor.font};
                --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
                :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}" max-height="500px"
                empty-text="暂无数据" 
                > 
                    <template #empty>
                        <el-empty  v-loading="loading"></el-empty>
                    </template>
                    <el-table-column v-for="(item,index) in smalltable" :key="index" 
                    :width="item.widths" :prop="item.value" :label="item.name" align="center"
                    show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column  v-if="btn1.includes(title)" align="center" label="操作" >
                      <template #default="scope">
                        <el-button @click="look(scope.row)" type="primary" link>查看</el-button>
                    </template>
                    </el-table-column>
                </el-table>
                <el-pagination  v-if="btn1.includes(title)" v-model:current-page="getform.page" v-model:page-size="getform.count" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                :total="Number(Totles) " @size-change="handleSizeChange" @current-change="handleCurrentChange" />
           </div>
           <div v-for="(item,index) in computedManagerList" :class="[tilelist.includes(title)?'manger':'text', item.type=='1'?'full-width':'']" :key="index" :style="getcloume(item)">
               <h2 v-if="tilelist.includes(item.name)" class="h2">{{item.name}}</h2>
               <span v-else-if="item.type=='0'&&item.name">{{item.name}}：</span>
               <span v-if="item.type=='0'">{{addform[item.value]}}</span>
               <span v-if="item.type=='2'">{{item.name}}</span>
               <span v-if="item.type=='2'">{{item.value}}</span>
           </div>
      </div>
      <picimg ref="picimgs"></picimg>
      <!-- <concer ref="concers" @close="showdelog"></concer> -->
    </el-dialog>
    <concer ref="concers" @close="showdelog"></concer>

  </template>
  
  <script setup>

  import selection from "@/components/connet/Common/selection.vue";
  import picimg from "@/components/connet/Common/picimg.vue";
  import { onMounted, ref, computed,getCurrentInstance} from 'vue';
  import { labelist } from "@/components/connet/Home/content/lables.js";
  import { gettable,setdata,deldata} from "@/network/api/requestnet";
  import store from "@/store";
  import { updateFormListFields } from '@/utils/dataTransform'
  import  delogshow  from "@/components/connet/technology/ecahrtline/delog.vue";
  import { formatDateTime } from "@/utils/date";
  import { solarToLunar } from "vue-solar-lunar";
  import concer from "./concer.vue";
  const $http = getCurrentInstance().appContext.config.globalProperties.$http
  
  let dialogTableVisible=ref(false)
  let bgcolor=ref({})
  let title=ref('')
  let tableDate=ref([])
  let loading=ref(false)
  let total=ref(0)
  let falge=ref(0)//0为供货商验收统计，1为详情
  let tilelist=['现场管理']
  let getform=ref({
      page:1,
      count:10,
      CreateTime:'',
      GetSpecialProgramTable:'',
      ProjectCode:store.getters.code,
      InUserName:store.getters.username,
      EquipType:"",//类型下拉框 10101塔机 10103升降机 10104卸料
      EquipName:"",
      CraneState:"",//状态下拉框 1在线 2离线 3未绑定

  })
  let widths=['现场管理']
  let url=ref('')
  let btns=['项目信息','参建单位']
  let labeform=ref([])
  let onelist=['项目名称']
  let twolist=[]
  let geturl=''
  let addform=ref({})
  let picimgs=ref(null)
  let toptitle=ref('')
  let smalltable=ref([])
  let weaterlist=ref([])
  let dates=ref([])
  let lunarDate=ref('') // 添加农历日期引用
  let mangerlist=ref([])
  let martop=['天气状况']
  let city=ref('')
  let id=ref('')
  let serch=ref([])
  let btn1=['设备统计']
  let Totles=ref(0) // 总条数
  let concers=ref(null)
  // 将 getstyle 函数转换为计算属性
  const computedStyle = computed(() => {
    if (!bgcolor.value || !bgcolor.value.titlecolor || !bgcolor.value.delogcolor) {
      return '';
    }
    return `border:2px solid ${bgcolor.value.titlecolor};
    background:rgba(${bgcolor.value.delogcolor},0.35)`;
  });

  // 将 getcomper 函数转换为计算属性
  const computedManagerList = computed(() => {
    let result = falge.value == 0 ? labelist('项目信息') : [];
    if (title.value == '现场管理') {
      // 使用 mangerlist.value 中的数据，而不是固定的数组
      return mangerlist.value;
    }
    return result;
  });

  window.addEventListener('setthcolor', ()=> {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
  })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getform.value.ProjectCode=store.getters.code
  })
  const showdelog=(val,titleText)=>{
    //   console.log('titleText',val,titleText);
      toptitle.value=''
      title.value=titleText
      labeform.value=[]
      falge.value=0
      switch (titleText) {
          case '天气状况':
              getAreaCode()
                // getweater()
                falge.value=2

              break;
          case '基础信息':
            falge.value=0
            geturl='GetNewProjectDetailInfo'
            labeform.value=labelist('项目信息')
            getdetil()
              break;
          case '现场管理':
          mangerlist.value=[
                    {
                        name:'职位',
                        value:'人员',
                        type:'2'
                    }
                ]
            geturl='GetPostByProject'
            getdetil()
              break;
          case '设备统计':
            falge.value=1
            serch.value=labelist('设备统计搜索')
            smalltable.value=labelist('设备统计')
            url.value='GetEquipByProject'
            
              getdetable()

              break;
            }
    //   getdetable()
      dialogTableVisible.value=true
  }
  const search=()=>{
      // console.log('搜索',getform.value);
      getform.value.page=1
      getform.value.count=10
      getdetable()
  }
  const getclass=(val)=>{
    if (title.value!='现场管理') {
        return 'lable'
    }
    return ''
  }
  const look=(val)=>{
      // console.log('查看',val);
      if (title.value=='设备统计') {
          falge.value=4
          dialogTableVisible.value=false
          concers.value.more(val)
      }
    }
   const pic=(val)=>{
      let imgtype=['png','jpg','jpeg','gif','bmp']
      if (val) {
          let lastIndex= val.lastIndexOf('.')
          let file=val.substring(lastIndex+1).toLowerCase()
          if (imgtype.includes(file)) {
              // console.log('获取',val);
              picimgs.value.piclist(val)
          }else{
              let type = val.substring(val.lastIndexOf('.') + 1).toLowerCase()
              
              if (type !== 'mp4') {
                  window.open('https://f.zqface.com/?fileurl='+val,'_slef')
              }
  
          }
      }
  }
  const getbtns=(val,index)=>{
    falge.value=index
    switch (index) {
        case 0:
            labeform.value=labelist('项目信息')
            getdetil()
            break;
        case 1:
            smalltable.value=labelist('参建单位')
            url.value='GetCropTable'
            getdetable()
            break;
    }
  }
  const getcloume=(val)=>{
      // 如果 val.name 在 onelist 数组中，合并三列
      if (onelist.includes(val.name)) {
          return 'grid-column: 1 /span 2';
      }
      return '';
  }
  // 获取星期几的中文名称
  const getWeekDay = (dateString) => {
    const date = new Date(dateString);
    const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return weekDays[date.getDay()];
  }
  // 获取地区code
  const getAreaCode = async() => {
    const {data:res}=await gettable('GetCityByProject',getform.value)
    // return res.data.areaCode;
    console.log('获取地区code',res);
    if (res.code=='1000') {
      city.value = res.data.Area1+ ' ' + res.data.Area2;
      getweatercode(res.data.Area1,res.data.Area2)

    }
    // getweatercode()
  }
  // 获取天气的code
  const getweatercode=async(val,val1)=>{ 

	const {data:res1} = await $http.get(`https://mm2tun63x2.yun.qweatherapi.com/geo/v2/city/lookup?key=6cdb9d105ae0411ea7822072fac3092f&location=${val1}&adm=${val}`)
    if (res1.code=='200') {
      id.value=res1.location[0].id
      getweater()
    }
    // console.log('获取天气code',res1);
    
  }

  
  const getweater=async()=>{
    let date=new Date()
    // let tody=date.getDate()
    let datashow=['今天','明天','后天']
    // console.log('获取日期',date.getDate());
	const {data:res1} = await $http.get(`https://devapi.qweather.com/v7/weather/7d?key=e0b66f1dcbbe4a76aea59aa878a59a29&location=${id.value}`)
    //   console.log('获取日期',res1);
      if (res1.code=="200") {
        weaterlist.value=res1.daily
        dates.value= formatDateTime(new Date(res1.updateTime),'yyyy-MM-dd')
        const dateArr = dates.value.split('-');
        const year = parseInt(dateArr[0]);
        const month = parseInt(dateArr[1]);
        const day = parseInt(dateArr[2]);
        const lunar = solarToLunar(year, month, day);
        lunarDate.value = `农历${lunar.monthStr}${lunar.dayStr}`;
        weaterlist.value.map((item,index)=>{
            item.weekDay = getWeekDay(item.fxDate);
            if (index<3) {
                item.weekDay = datashow[index]+'('+item.weekDay+')'
            }
            // console.log('获取星期几',item.weekDay);
        })
      }
   }
  const rowopen=(row)=>{
    // console.log('行',row);
    pic(row.FileUrl)
  }
   const getdetable=async()=>{ 
      loading.value=true
      const {data:res}=await gettable(url.value,getform.value)
      // console.log('获取数据',res);
      loading.value=false
      tableDate.value=res.data
      total.value=res.Total || 0
   }
  //  获取详情
   const getdetil=async(val)=>{
      let GUID={
        //   GUID:val.GUID,
          ProjectCode:store.getters.code,
      }
      const {data:res}=await gettable(geturl,GUID)
    //   console.log('获取数据',res);
      if (res.code=='1000') {
          addform.value = Array.isArray(res.data) ? res.data[0] : res.data
            if (title.value=='现场管理') {
                // 注释掉直接修改 mangerlist.value 的代码
                mangerlist.value=[
                    {
                        name:'职位',
                        value:'人员',
                        type:'2'
                    }
                ]
                let arr=res.data.map(item=>{
                    return {
                        name:item.PostName,
                        value:item.PostWorker,
                        type:'2'
                    }
                })
                mangerlist.value.push(...arr)
            }
      }
   }
   const handleSizeChange = (val) => {
        console.log(`${val} 显示多少页`)
        getform.value.count=val
        getdetable()
      }
    const handleCurrentChange = (val) => {
        console.log(`选择第几: ${val}`)
        getform.value.page=val
        getdetable()
      }
   const getwidth=(val)=>{
      if (val.widths) {
          return `width: ${val.widths}px`
      }else{
          return 'width: 200px'
      }
   }
   // 显示磅单详情的公共函数
   const showReceiptDetail = (row) => {
    //   falge.value = 1
    //   title.value = ` ${row.ReceivingCode}`
    //   labeform.value = labelist('施工方案详情')
      
      getdetil(row)
      return true // 返回true表示已处理，调用处可以直接return
   }
   
  const closes = () => {
    dialogTableVisible.value=false
  };
  
  const tableRowClassName=({row,rowIndex,})=>{
          // console.log('获取当前行',row,rowIndex);
          if (rowIndex%2 != 0) {
              return 'warning-row'
          }
          return ''
      }
  
  defineExpose({
    showdelog
  })
  </script>
  <style lang="scss" scoped>
  
  .serch{
      color: #fff;
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
  }
  .bodybottom{
      text-align: left;
      h2{
          color: #fff;
          margin-bottom: 10px;
      }
  }
  :deep(.martops) {
    margin-top: 15vh !important;
  }

  /* 如果上面的方法不行，尝试直接针对dialog */
  :deep(.el-dialog.martops) {
    margin-top: 15vh !important;
  }

  /* 更具体的选择器 */
  :deep(.el-dialog.delogss.martops) {
    margin-top: 15vh !important;
  }
  .condition{
    grid-column: 1 /span 3;

  }
  .manger{
    display: flex;
    justify-content: space-evenly;
    color: #01C2FF;
    margin: 10px;

  }
  .weaters{
    color: #fff;
    // grid-column: 1 /span 3;
    display: grid;
    grid-template-columns: repeat(7,14.28%);
    // justify-content: space-between;
    align-items: center;
    // span{
        
    // }&&
    &-tt{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        span{
            // margin-bottom: 10px;
            display: inline-block;
            margin: 10px;
        }
    }
  }
  .lable{
      display: grid;
      grid-template-columns: 1fr 1fr;
      h2{
          grid-column: 1 /span 3;
          color: #fff;
      }
  }
  .text{
      color: #01C2FF;
      margin: 20px 10px;
      margin-left: 100px;
      display: inline-block;
  }
  .cursor{
      margin: 5px;
  }
  .el-scrollbar{
      grid-column: 1 /span 3!important;
  
  }
  /* 添加新的CSS类 */
  .full-width {
      grid-column: 1 /span 3 !important;
      width: 100%;
  }
  .btns{
    //   display: flex;
    //   justify-content: center;
    width: 100%;
      margin-bottom: 10px;
      grid-column: 1 /span 2;
  }
  /* 农历日期样式 */
  .lunar-date {
      font-size: 14px;
      margin-left: 10px;
      color: #01C2FF;
  }
  </style>