<template>
  <el-dialog v-model="dialogTableVisible" :style="[`border:2px solid ${bgcolor.titlecolor};background:rgba(${bgcolor.delogcolor},0.35)`]" 
  class="homedelog" width="70%" title="页面布局设置">
    <div class="homedelog-sum">
        <div class="homedelog-sum-left"  :style="`border:2px solid ${bgcolor.titlecolor}`">
            <div class="homedelog-sum-top" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%, rgba(2, 193, 253, 0) 89%);
            border-bottom:2px solid ${bgcolor.titlecolor}`">
                <img src="@/assets/img/home/<USER>" alt="">
                <p>页面展示</p>
            </div>
            <div class="homedelog-sum-content" >
                <div v-for="(item,index) in showdemo" :key="index" class="homedelog-sum-detil" 
                @dragleave="dragleave($event)" 
                @dragover="dragover($event)" 
                @drop='drop($event,item,index)'

                @dragstart="dragstart(item,0)" @dragend="dragend($event,item)" 
                @drag="drag($event,item)"
                :style="`border:2px solid ${bgcolor.titlecolor}`"
                >   
                    <img v-if="item.name" :src="item.src" alt="" srcset="" style="width:100%;height:100%">
                    <span v-else>{{index+1}}</span>
                </div>
            </div>
            <p class="dropmk">拖动模块至方块中，完成拖拽</p>
        </div>
        <div class="homedelog-sum-right">
            <el-scrollbar height="700px">
                <div class="homedelog-sum-right-list">
                    <div class="cursor imgcur" v-for="(item,index) in imglist" :key="index" 
                    @dragstart="dragstart(item,1)" @dragend="dragend($event,item)" 
                    @drag="drag($event,item)"
                    title="拖拽至左侧进行替换" 
                    >
                        <img :src="item.src" style="width:100%;height:100%" alt="">
                    </div>
                   
                </div>
            </el-scrollbar>
        </div>
    </div>
    <div slot="footer" class="dialog-footer">
        <el-button @click="cancelExpert('addForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('addForm')" :disabled="loading" :loading="loading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { nextTick, onMounted, ref ,getCurrentInstance} from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import { ElMessage } from 'element-plus'
export default {
setup(){
    let dialogTableVisible=ref(false)
    let time=ref(null)
    let loading=ref(false)
    let changename=ref('')
    let draginde=ref(null)
    let showdemo=ref([
        // {
        //     index:'1',
        //     name:'',
        //     src:''
        // },
        // {
        //     index:'2',
        //     name:'',
        //     src:''
        // },
        // {
        //     index:'3',
        //     name:'',
        //     src:''
        // },
        // {
        //     index:'4',
        //     name:'',
        //     src:''
        // },
        // {
        //     index:'5',
        //     name:'',
        //     src:''
        // },
        // {
        //     index:'6',
        //     name:'',
        //     src:''
        // }
    ])
    let imglist=ref([
        // {
        //     // index:1,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'Imageprogress'
        // },
        // {
        //     // index:2,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'Equipment'
        // },
        // {
        //     // index:3,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'produce'
        // },
        // {
        //     // index:4,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'project'
        // },
        // {
        //     // index:5,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'quality'
        // },
        // {
        //     // index:6,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'secure'
        // },
        // {
        //     // index:4,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'Attendance'
        // },
        // {
        //     // index:5,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'scene'
        // },
        // {
        //     // index:6,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'weather'
        // },
        // {
        //     // index:6,
        //     src:require('@/assets/img/home/<USER>'),
        //     name:'faults'
        // },
    ])
    let bgcolor=ref({})
    const $sseion = getCurrentInstance().appContext.config.globalProperties.$sseion

    window.addEventListener('setthcolor', ()=> {
            // console.log('导航');
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // debolor.value =`rgba(${bgcolor.value.navcolor},0.4)`
        // 获取包含伪元素的元素
    })
    const getdeolog=async()=>{
        let GUID={
            ProjectCode:store.getters.code
        }
        const {data:res}=await gettable('CreateAllModule',GUID)
        // console.log('获取弹窗',res);
        
        if (res.code=="1000") {
            showdemo.value=res.data.LModule
            imglist.value=res.data.RModule
        }
    }
    const showdelog=()=>{
        getdeolog()
        // console.log('登录名称',store.getters.username);
        
        dialogTableVisible.value=true
        bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        // debolor.value =`rgba(${bgcolor.value.navcolor},0.4)`
        nextTick(()=>{
          let delog = document.querySelector(".el-dialog__header");
        //   console.log('获取',);
          
          let headerco=`linear-gradient(90deg, ${bgcolor.value.titlecolor} 0%, rgba(2, 193, 253, 0) 89%)`
            delog.style.background=`${headerco}`
        })


    }
    const dragstart=(val,value)=>{
        // console.log('开始拖拽',val);
        // changename.value=val
        changename.value=JSON.parse(JSON.stringify(val))
        draginde.value=value
    }
    const dragend=(e,val)=>{
        // console.log('拖拽中');
        
    }
    const drag=(e,val)=>{
        // console.log('拖拽停止');
        
    }
    const dragleave=()=>{
        // console.log('当拖拽离开这个div时');
        
    }
    const dragover=(e)=>{
        e.preventDefault()
        // console.log('拖拽在这个div里面拖拽时');
        
    }
    const drop=(e,val,ind)=>{
        // console.log('在div里拖拽停止时',val);
        // let index = arr.findIndex(item => item.name === 'Mike');
        // arr.splice(index, 1);
        // 右侧拖拽到左侧替换
        if (draginde.value==1) {
            if (showdemo.value[ind].name) {
                imglist.value.push({
                    name:val.name,
                    src:val.src
                })
                showdemo.value[ind].name=""
                showdemo.value[ind].src=""
                
            }
            showdemo.value[ind].name=changename.value.name
            showdemo.value[ind].src=changename.value.src
        }
        // showdemo.value[ind].name=changename.value.name
        // showdemo.value[ind].src=changename.value.src
        if (draginde.value==0) {
            // console.log('拖拽',changename.value);
            if (val.name) {
                // 左侧模块替换
                // console.log('有值',showdemo.value,changename.value);
                showdemo.value[changename.value.index-1].name=val.name
                showdemo.value[changename.value.index-1].src=val.src

                showdemo.value[ind].name=changename.value.name
                showdemo.value[ind].src=changename.value.src

                
            }else{
                // 没有值时替换
            showdemo.value[ind].name=changename.value.name
            showdemo.value[ind].src=changename.value.src

            showdemo.value[changename.value.index-1].name=""
            showdemo.value[changename.value.index-1].src=""
            }
        }else{
            // 右侧拖拽实现
            showdemo.value[ind].name=changename.value.name
            showdemo.value[ind].src=changename.value.src
            let index = imglist.value.findIndex(item => item.name == changename.value.name);
            imglist.value.splice(index, 1);
        }
        changename.value=null
        // console.log('左侧数据',showdemo.value);
        // console.log('右侧',imglist.value);
        
    }
    // 取消
    const cancelExpert=()=>{
        dialogTableVisible.value=false
    }
    const submitForm=async()=>{
        loading.value=true
        let addform={
            ProjectCode:store.getters.code,
            InUserName:store.getters.username,
            LModule:showdemo.value
        }
        const {data:res}=await gettable('GetSubmitModule',addform)
        loading.value=false
        // console.log('提交',res);
        
        if (res.code=="1000") {
            // $sseion('theme',JSON.stringify(theme))
            getdeolog()
            let theme=showdemo.value
            $sseion('theme',JSON.stringify(theme))
            dialogTableVisible.value=false
            ElMessage({
                type:'success',
                message: res.msg
            });
        }else{
            ElMessage({
                type:'error',
                message: res.msg
            });
        }

    }

    return{
        dialogTableVisible,
        bgcolor,
        // debolor,
        loading,
        draginde,
        time,
        imglist,
        showdemo,
        changename,
        showdelog,
        dragend,
        drag,
        dragstart,
        dragleave,
        dragover,
        drop,
        cancelExpert,
        submitForm,
        getdeolog
        
    }
}
}
</script>
<style lang="scss">
.homedelog{
    margin-top: 2%!important;
    // background: rgba(2, 193, 253, 0.24)!important;
    opacity: 1;
    box-sizing: border-box!important;
    // border: 2px solid #02C1FD!important;
    .el-dialog__header{
        text-align: start!important;
        margin: 10px;
        padding: 10px;
        // background: linear-gradient(90deg, #02C1FD 0%, rgba(2, 193, 253, 0) 89%)!important;
        .el-dialog__title{
            color: #fff!important;
        }
    }
    .el-dialog__body{
        padding: 10px!important;
    }
    
}
</style>
<style lang="scss" scoped>
.homedelog-sum{
    color: #fff;
    height: 700px;
    display: flex;
    align-items: center;
    &-left{
        border: 2px solid #02C1FD;
        height: 80%;
        width: 50%;
        p{
            padding: 5px;
        }
        .dropmk{
            font-size: 28px;
        }
    }
    &-top{
        border-bottom: 2px solid #02C1FD;
        text-align: start;
        font-size: 18px;
        font-weight: bold;
        background: linear-gradient(90deg, #02C1FD 0%, rgba(2, 193, 253, 0) 89%);
        display: flex;
        align-items: center;
    }
    &-content{
        // display: flex;
        // flex-wrap: wrap;
        // justify-content: space-between;
        height: 95%;
        display: grid;
        justify-content: space-between;
        grid-auto-flow: column dense;
        grid-template-columns: repeat(2, 45%);
        grid-template-rows: repeat(3, 33%);

// display: inline-grid;
    }
    &-detil{
        // width: 43%;
        // height: 30%;
        margin: 10px;
        border: 2px solid #02C1FD;
        display: flex;
    align-items: center;
    justify-content: center;
    span{
        font-size: 40px;
        font-weight: bold;
    }
    }
    &-right{
        width: 50%;
        &-list{
            height: 300px;
            // width: 40px;
            margin: 10px;
                // border: 2px solid #02C1FD;
                display: flex;
                flex-wrap: wrap;
            // img{
            //     margin: 20px 10px;
            //     }
            .imgcur{
                margin: 20px 10px;
                height: 200px;
                width: 45%;
            }
            }
            .imgcur:hover{
                border: 2px solid #02C1FD;
            }

    }
}
.dialog-footer{
    margin-top: 10px;
}
</style>