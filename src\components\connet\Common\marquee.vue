<template>
<div class="rollBox">
    <div class="marquee">
        {{ texts }}
    </div>
</div>
</template>

<script>
export default {
    props:['texts']

}
</script>
<style lang="scss" scoped>
.rollBox{
    float: left;
    width: 100%;
    overflow: hidden;
    // height: .25rem;
    // line-height: .25rem;
    font-size: .0729rem;
    color: #fff;
    // background: rgba(0,0,0,0.6);
}
.marquee {
    white-space: nowrap;
    // overflow: hidden;
    // animation: marquee-right 20s linear infinite;
    /* @keyframes 动画指定名称。 */
    animation-name:marquee-left;
    /* 定义动画完成一个周期需要多少秒或毫秒 */
    animation-duration: 40s;
    /* 速度曲线 */
    animation-timing-function:linear;
    /* 定义动画应该播放多少次 */
    animation-iteration-count: infinite;
}
// 从右向左
@keyframes marquee-left {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}
// 从左向右
@keyframes marquee-right {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
// 从上到下
@keyframes marquee-top {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(100%);
    }
}
// 从下到上
@keyframes marquee-bottom {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(100%);
    }
}
</style>