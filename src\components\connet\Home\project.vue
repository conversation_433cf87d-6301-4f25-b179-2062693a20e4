<template>
  <!-- 项目里程碑 -->
  <div class="project padding" :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="topforms"></Chamfering>

    <div class="project-two">
        <div class="project-two-one lefts">
          <el-icon
            :class="['cursor lefts', { 'disabled': isAtStart }]"
            @click="!isAtStart && upclck()"
            :style="{ color: isAtStart ? '#666' : 'aqua' }">
            <DArrowLeft />
          </el-icon>
        </div>
        <div class="project-two-content">
          <div  v-for="(item,index) in prolsit" :key="index" class="moulist"
           v-show="item.showfalge==1" >
              <span class="moulist-one fontsty">{{item.MilepostName}}</span>
              <img :src="require(`@/assets/img/home/<USER>/${item.MilepostStatus}.png`)" alt="">
              <div class="moulist-two" :style="item.MilepostStatus=='已完成'?`color:#ccc`:''">
                <span class="fontsty" :style="item.MilepostStatus=='未开始'?`color:red`:''">{{item.MilepostStatus}}</span>
                <span>计划完成</span>
                <span>{{item.MilepostEndTime}}</span>
                <span>实际完成</span>
                <span>{{item.RealEndTime}}</span>
              </div>

          </div>

        </div>
        <div class="project-two-one lefts">
          <el-icon
            :class="['cursor lefts', { 'disabled': isAtEnd }]"
            @click="!isAtEnd && nextclck()"
            :style="{ color: isAtEnd ? '#666' : '#fff' }">
            <DArrowRight />
          </el-icon>
        </div>
    </div>
    <Chamfering :homeindex="homeindex" :horn="0"></Chamfering>
  </div>
</template>

<script>
import { ref,onMounted,computed } from 'vue'
import store from "@/store";
import { gettable } from "@/network/api/requestnet";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
props:['homeindex'],
components:{
  Chamfering
},
setup(){
   let bgcolor=ref({})
  let getform=ref({
    ProjectCode:store.getters.code,
    count: 100,
    page:1
  })
  // let proicon=['MilepostName','12','MilepostStatus','计划完成','MilepostEndTime','实际完成','RealEndTime' ]
  let prolsit=ref([])
  let topforms=ref({
    url:require('@/assets/img/home/<USER>'),
    name:'项目里程碑'
  })
  window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
   onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      getMilepost()
   })
  const upclck=()=>{
  let fist= prolsit.value.find(function(x) {
      // console.log('h',x);
      
      return x.showfalge == 1;
    })
  let arrlist=[]
  prolsit.value.forEach((item,index)=>{
    if (item.showfalge==1) {
      arrlist.push(item)
      
    }
  })

  if (fist.index>0&&prolsit.value.length>=3) {
    prolsit.value[fist.index-1].showfalge=1
    prolsit.value[fist.index+2].showfalge=0
  }
    // console.log('左边',aa);
    
  }
  const nextclck=()=>{
    let end= prolsit.value.findLastIndex(function(x) {
      return x.showfalge == 1;
    }) 
    let arrlist=[]
    prolsit.value.forEach((item,index)=>{
      if (item.showfalge==1) {
        arrlist.push(item)
      }
    })
    // console.log('下一次',end);
    
  if (end<prolsit.value.length-1&&prolsit.value.length>=3) {
    // console.log('下一次');
    
    prolsit.value[end-2].showfalge=0
    prolsit.value[end+1].showfalge=1
  }
  }

  // 计算属性：判断是否在最左侧（不能再向左滚动）
  const isAtStart = computed(() => {
    if (prolsit.value.length === 0) return true
    const firstVisible = prolsit.value.find(item => item.showfalge === 1)
    return !firstVisible || firstVisible.index === 0
  })

  // 计算属性：判断是否在最右侧（不能再向右滚动）
  const isAtEnd = computed(() => {
    if (prolsit.value.length === 0) return true
    const lastVisibleIndex = prolsit.value.findLastIndex(item => item.showfalge === 1)
    return lastVisibleIndex === -1 || lastVisibleIndex === prolsit.value.length - 1
  })

  const getMilepost=async()=>{
      const {data:res}=await gettable('GetMilepostBytimeAsc',getform.value)
      if (res.code=="1000") {
        prolsit.value=res.data
        prolsit.value.forEach((item,index)=>{
          item.index=index
          if (index<=2) {
          item.showfalge=1

          }else{
          item.showfalge=0

          }

        })
      }
  }
  
  return{
    getform,
    bgcolor,
    prolsit,
    topforms,
    upclck,
    nextclck,
    getMilepost,
    isAtStart,
    isAtEnd
  }
  }
}
</script>
<style lang="scss" scoped>
.project{
.moulist{
    font-size: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 33%;
    color: #fff;
    padding-top: 10px;
    span{
      margin:5px  5px;
    }
    &-one{
      height: 23px;
      color: #03FBFF;
      font-weight: 500;
    }
    &-two{
      display: flex;
      flex-direction: column;
      margin-top: 10px;
      span:nth-child(1){
        font-size: 14px;
      }
    }
}
.fontsty{
  font-size: 14px;
}
&-two{
  display: flex;
  align-items: center;
  // margin-top: 15px;
  .lefts{
    color: aqua!important;
    font-size: 18px;
  }
  // .nexticon{
  //   color: #fff;
  //   font-size: 18px;

  // }
  // 禁用状态样式
  .disabled {
    cursor: not-allowed !important;
    opacity: 0.5;
    pointer-events: none;
  }
  &-content{
    width: 90%;
    display: flex;
  }
  &-one{
    width: 5%;
  }
}
.circle{
  width: 39px;
  height: 39px;
  opacity: 1;

  background: #CCCCCC;
}
}
</style>