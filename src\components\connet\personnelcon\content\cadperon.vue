<template>
  <el-dialog  class="posting" id="bgcancas"
   :fullscreen="falgedelog" :close-on-click-modal="false" :destroy-on-close="true"
   v-model="dialogVisiblede" width="20%" @close="clsoe()">
    <div v-loading="loading" class="dialog-content">
      <div id="doms" :class="['doms',{bgimg:getform.InUserName=='新盛建设548'}]"></div>
      <div class="topimg" >
          <div class="topfq">
              <span  v-for="(item,index) in form" :key="index">{{item.DTYPE}}：{{item.PCOUNT}}</span>
          </div>
          <p class="titles">智慧工地人员定位系统</p>
      </div>
      <div class="echarts" v-if="gjfalge==1">
          <div class="echarts-table">
              <div class="echarts-one">
                  <div id="bre"></div>
                  <div v-for="(item,index) in bresechart" :key="index" class="echarts-icon">
                      <div class="icons" :style="`background:${WorkTypecolor[index]}`"></div>
                      <span>{{item.name}}{{item.value}}</span>
                  </div>
              </div>
              <div id="lines"></div>
          </div>
          
      </div>
      <!-- <el-dialog title="人员信息" :loading="loading1" custom-class="diy-dialog diy--ts-dialog pers" width="20%" append-to-body :destroy-on-close="true" :visible.sync="dialogVisibledes">
          <div class="imgsor">
              <img  class="cursor" :src="`data:image/jpeg;base64,${formsget.HeadImage}`" alt="" @click="pic(`data:image/jpeg;base64,${formsget.HeadImage}`)">
              <div class="names">
                  <span  class="trajectory">{{formsget.WorkerName}}</span>
                  <span class="cursor" @click="trajectory(formsget)"> <i class="iconfont icon-locus-full"></i>今日轨迹</span>
              </div>
              <p v-for="(item,index) in list" :key="index">{{item.name}}：{{formsget[item.vaule]}}</p>
          </div>
      <picimg ref="picimg"></picimg>

      </el-dialog> -->
      <div class="comback cursor"  @click="clsoe()" :style="gjfalge==0?'right:50px;top:45px':'right:18%;top:71px'">
          <i :class="`iconfont ${gjfalge==0?'icon-guanji':'icon-fanhui'}`"></i>
          <span>{{gjfalge==0?'退出':'返回'}}</span>
      </div>
    </div>
</el-dialog>
</template>

<script setup>
import { ref, onMounted, defineComponent,getCurrentInstance, onBeforeUnmount, nextTick } from 'vue'
import { useStore } from 'vuex'
import picimg from "@/components/connet/Common/picimg.vue"
// import $ from 'jquery'
// import * as echarts from 'echarts'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import { loadBimfaceSDK } from '@/utils/loadBimface'

const $http = getCurrentInstance().appContext.config.globalProperties.$http
const $moist = getCurrentInstance().appContext.config.globalProperties.$moist

// 组件声明
const components = {
  picimg
}

// 状态定义
let canvass = null
let viewer2D = null
let app = null
let ctx = null
let modelId = '0'
let viewAdded = false
let viewapp = null

const store = useStore()

// 响应式数据
const dialogVisiblede = ref(false)
const dialogVisibledes = ref(false)
const falgedelog = ref(true)
const falge = ref(false)
const gjfalge = ref(0)
const posting = ref({})
const username = ref('')
const loading = ref(false)
const loading1 = ref(false)
const drawi = ref(0)
const posticon = ref('dw')
const tokens = ref('')

const WorkTypecolor = ref(["#407fff","#a682e6","#e15d68"])
const form = ref([])
const bresechart = ref([])
const lineechart = ref([])

const getform = ref({
  InUserName: store.getters.username,
  ProjectCode: store.getters.code,
  IDCardNumber: ''
})

const eqmentcount = ref([
  {
    name:'218600315988',
    x:2280,
    y:3400
  },
  // ... 其他设备数据
])

const postingrl = ref([])

let timer = null

// 方法定义
const showdelog = (val) => {
  drawi.value = 0
  getform.value.ProjectCode = store.getters.code
  getform.value.InUserName = store.getters.username
  
  dialogVisiblede.value = true
  
  if (store.getters.username == '华东工程674') {
    loading.value = true
    gettopper()
    cad()
    
    timer = setInterval(() => {
      drawi.value = 1
      getposting()
    }, 60000)
  }
}

const cad = async () => {
  const takoen = {
    fileid: 10000864981479
  }
//   gettable
  const { data: res } = await gettable('GetTokenInfo', takoen)
  if (res.code) {
    tokens.value = res.token
    prow2d()
  }
}

const prow2d = async () => {
  let _this = this;
  var viewToken = tokens.value;

  try {
    // 确保 BimfaceSDK 已加载
    await loadBimfaceSDK();
    
    var options = new BimfaceSDKLoaderConfig();
    options.viewToken = viewToken;
    BimfaceSDKLoader.load(options, successCallback, failureCallback);
  } catch (error) {
    console.error('加载 BimfaceSDK 失败:', error);
    loading.value = false;
    return;
  }

  // 加载成功回调函数
  function successCallback(viewMetaData) {
    if (viewMetaData.viewType == "drawingView") {
      let dom4Show = document.getElementById('doms');
      // 构造WebApplication配置项
      let webAppConfig = new Glodon.Bimface.Application.WebApplicationDrawingConfig();
      webAppConfig.domElement = dom4Show;
      // 构造WebApplication对象
      app = new Glodon.Bimface.Application.WebApplicationDrawing(webAppConfig);
      // 获取viewer对象
      viewer2D = app.getViewer();
      // 加载图纸
      viewer2D.loadDrawing({
        viewToken: viewToken
      });
      
      // 监听添加view完成的事件
      viewer2D.addEventListener(Glodon.Bimface.Viewer.ViewerDrawingEvent.Loaded, function () {
        setTimeout(()=>{
          viewer2D.enableViewport(true);
          let staus = {
            ViewAngle: 0,
            ViewCenter: {x: 2194.925137489452, y: 3342.956854790223},
            WorldScale:0.0036377180748396742,
            ZoomFactor:7144.950286641509,
            ver:1,
            viewId:0
          };
          viewer2D.setState(staus);
          getposting();
          loading.value = false;
        }, 3000);

        //自适应屏幕大小
        window.onresize = function () {
          viewer2D.resize(document.documentElement.clientWidth, document.documentElement.clientHeight - 40);
        };
      });
    }
  }

  // 加载失败回调函数
  function failureCallback(error) {
    console.log(error);
    loading.value = false;
  }
};

const DrawableContainer = () => {
  var drawableConfig = new Glodon.Bimface.Plugins.Drawable.DrawableContainerConfig();
  drawableConfig.viewer = viewer2D;
  var drawableContainer = new Glodon.Bimface.Plugins.Drawable.DrawableContainer(drawableConfig);
  return drawableContainer;
}

const addTag = (container,optins,form) => {
  let _this=this
  // console.log('先走',optins);
  
  var config = new Glodon.Bimface.Plugins.Drawable.CustomItemConfig();
  config.offsetX = -6;
  config.offsetY = -6;
  var circle = document.createElement('div');
  circle.style.width = '20px';
  circle.style.height = '20px';
  circle.style.border = 'solid';
  circle.style.borderColor = '#FFFFFF';
  circle.style.borderWidth = '2px';
  circle.style.backgroundImage = `url(${require(`../../../../assets/img/personnelcon/${posticon.value}.png`)})`
  circle.style.backgroundSize = '100%';
  circle.style.backgroundRepeat = 'no-repeat';
  circle.style.borderRadius = '50%';
  config.content = circle;
  config.draggable = false;
  config.viewer = viewer2D;
  config.worldPosition =optins;
  config.tooltip=form.WorkerName
//   config.tooltip=form.IDCardNumber
//   config.offsetY=-20
//   circle.setAttribute('WorkerName', form.WorkerName);
  circle.setAttribute('IDCardNumber', form.IDCardNumber);
//   circle.title = '这是一个提示文本'
  circle.addEventListener('click', function(e) {
    // console.log('点击');
      if (gjfalge.value==0) {
        
      getform.value.IDCardNumber=this.getAttribute('IDCardNumber')
      loading1.value=true
      getdetil()

      dialogVisibledes.value=true
      }

  });
    circle.addEventListener('mouseenter',function(e){
      // console.log('移入');
      
      circle.style.cursor='pointer'
  })
  circle.addEventListener('mouseout',function(e){
      circle.style.cursor='default'
  })
  var customItem = new Glodon.Bimface.Plugins.Drawable.CustomItem(config);
    customItem.setTooltipStyle({
      top:'-30px',
      left:'-10px'
  })
  // console.log('只显示',customItem);
  
  container.addItem(customItem)
  container.update();

}

const addTags = () => {
  let _this=this

  var drawableContainer = DrawableContainer();

  // getAllItems()
  // console.log('获取',drawableContainer.clear());
  if (drawi.value==1) {
    // console.log('是否清除');
    
    drawableContainer.clear()
  }
  for (let index = 0; index < eqmentcount.value.length; index++) {
    for (let i = 0; i < postingrl.value.length; i++) {
      if (eqmentcount.value[index].name==postingrl.value[i].DeviceID) {
        let option={
          x:parseInt(postingrl.value[i].X)+parseInt(eqmentcount.value[index].x),
          y:parseInt(postingrl.value[i].Y)+parseInt(eqmentcount.value[index].y)
        }
        let form={
          IDCardNumber:postingrl.value[i].IDCardNumber,
          WorkerName:postingrl.value[i].WorkerName
        }
        // console.log('是否显示');
        
        // let form={
        //     IDCardNumber:_this.eqmentcount[index].name
        // }
        // let option={
        //     x:parseInt(_this.eqmentcount[index].x),
        //     y:parseInt(_this.eqmentcount[index].y)
        // }
        addTag(drawableContainer,option,form);

      }
      
    }
  }
}

const getdetil = async () => {
  const { data: res } = await gettable('GetCardDataPersonDetail', getform.value)

  loading1.value=false
  if (res.code=="1000") {
    formsget.value=res.data
  }
}

const pic = (val) => {
  this.$refs.picimg.piclist(val)
}

// 获取图
const getdatas = async () => {
  const { data: res } = await gettable('GetWorkerTrajectoryEchart', getform.value)

  // console.log('获取图标数据',res);
  if (res.code=="1000") {
    bresechart.value=res.data.Echarts1
    lineechart.value=res.data.Echarts2
  }else{
    bresechart.value=[]
    lineechart.value=[]
  }
  nextTick(()=>{
    getecahrts()
    getbre()
  })
}

const getbre = () => {
  var echarts = require('echarts');
   let myChart = echarts.getInstanceByDom(document.getElementById('bre'));
   if (myChart == null) {
       myChart = echarts.init(document.getElementById('bre'));
   }
  var colorList = WorkTypecolor
  let echart=[]
  if (bresechart.value.length>0) {
    echart=bresechart.value.map((item,index)=>{
      return {
        name:item.name,
        value:item.Percent
      }
    })
  }

  let option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '数据来源',
        type: 'pie',
        radius:'85%',
        itemStyle: {
          normal: {
            color: function(params) {
              return colorList[params.dataIndex];
            },
          },
        },
        center: ['50%', '50%'],
        label: {
          normal: {
            show: true,
            position: 'inner', // 数值显示在内部
            formatter: '{d}%', // 格式化数值百分比输出
          },
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: echart
      }
    ]
  };
  myChart.setOption(option);
  window.addEventListener("resize", function() {
    myChart&&myChart.resize();
  });
}

const getecahrts = () => {
  var echarts = require('echarts');
  let myChart = echarts.getInstanceByDom(document.getElementById('lines'));
  if (myChart == null) {
    myChart = echarts.init(document.getElementById('lines'));
  }
  let date=[]
  if (lineechart.value.length>0) {
    date=lineechart.value.map((item,index)=>{
      return item.name
    })
  }
  let option = {
    grid: {
      left: "2%",
      right: "4%",
      bottom: "2%",
      top: "20%",
      containLabel: true,
    },
    xAxis: [{
      type: "category",
      data: date,
      axisLine: {
        lineStyle: {
          color: "#002860",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        interval: 0,
        textStyle: {
          color: "#ffffff",
        },
        // 默认x轴字体大小
        fontSize: 12,
        // margin:文字到x轴的距离
        margin: 15,
      },
      axisPointer: {},
      boundaryGap: false,
    }, ],
    yAxis: [{
      type: "value",
      splitNumber:3,
      min:0,
      max:3,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#002860",
        },
      },
      axisLabel: {
        lineHeight:10,
        textStyle: {
          color: "#ffffff",
        },
        formatter: function (value) {
          if (value == 0) {
            return "施工区";
          } else if (value == 1) {
            return "办公区";
          } else if (value == 2) {
            return "生活区";
          }
        },},
        boundaryGap: true,
        splitLine: {
          show: true,
          lineStyle: {
            color: "#002860",
          },
        },
      }, 
    ],
    series: [
      {
        data: lineechart.value,
        type: 'line',
        smooth: true
      }
    ],
  };
  myChart.setOption(option);
  window.addEventListener("resize", function() {
    myChart&&myChart.resize();
  });
}

// 轨迹
const trajectory = (val) => {
  // console.log('打开轨迹',val);
  let _this=this
  gjfalge.value=1
  // console.log('触发关闭');513522197206211410
  clearInterval(_this.timer)
  drawi.value=1
  posticon.value='gjimg'
  getform.value.IDCardNumber=val.IDCardNumber
  dialogVisibledes.value=false
  let staus={
    ViewAngle: 0,
    ViewCenter: {x: 2194.925137489452, y: 3342.956854790223},
    WorldScale:0.0036377180748396742,
    ZoomFactor:7144.950286641509,
    ver:1,
    viewId:0
  }
  viewer2D.setState(staus)
  gettrajectory()
  getdatas()
  // this.$nextTick(()=>{
  // this.getecahrts()
  // this.getbre()
  // })
}

const gettrajectory = async () => {
  const containers = document.querySelector('.bf-drawing-wrap');
  const points = document.getElementsByClassName('bf-drawable-text');

  let _this=this
  const {data:res }= await this.$http.post('/aiot/Api.ashx?PostType=get&Type=GetWorkerTrajectory', this.getform)
  // console.log('获取轨迹图',res);
  if (res.code=="1000") {
    postingrl.value=res.data
    addTags()
    getcamvas()

    var options = { attributes: true, childList: true,subtree:true,attributeOldValue:true};
    //回调事件
    function callback(mutationsList, observer) {
      //   console.log(mutationsList);
      // console.log('是否变化',aaaa);
      // console.log('');
      // _this.getcamvas()
      for(let mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const addedClasses = mutation.target.classList;
          // 检查是否添加了特定的类
          // console.log('是否增加');
          if (gjfalge.value==1) {
            canvass=document.getElementById('canvasline')
            ctx = canvass.getContext('2d');
            ctx.clearRect(0, 0, canvass.width, canvass.height);
            ctx.beginPath();
            ctx.strokeStyle = '#3979CE';
            ctx.lineWidth = 5;

            // 遍历div元素，除了最后一个
            for (let i = 0; i < points.length - 1; i++) {
              let rect1 = points[i].getBoundingClientRect();
              let rect2 = points[i + 1].getBoundingClientRect();
              // console.log('遍历元素',rect1.left +10+ rect1.width / 2,rect1.top +10+ rect1.height / 2);
              
              // 简化处理：连接两个div的中心点
              if (rect1.left +10!=10&&rect2.left +10!=10) {
                // console.log('不等于10',rect1.left +10,rect1.top +10,rect2.left +10);
                
                ctx.moveTo(rect1.left +10, rect1.top +10);
                ctx.lineTo(rect2.left +10, rect2.top +10);
              }

            }

            ctx.stroke();
          }

        }
      }
    }
    // console.log(observer);
    // console.log(111111111);
  }
  var mutationObserver = new MutationObserver(callback);
  mutationObserver.observe(containers, options);
}

const getcamvas = () => {
  const container = document.getElementById('doms');
  const points = document.getElementsByClassName('bf-drawable-text');
  let svgContainer = document.getElementsByClassName('bf-drawing-container')[0];
  var canvasline = document.getElementById('canvasline');

  if (canvasline) {
    let ctxs = canvasline.getContext('2d');
    ctxs = null;
    // 从DOM中移除Canvas元素
    if (canvasline.parentNode) {
      canvasline.parentNode.removeChild(canvasline);
    }
    // 清除对Canvas元素的引用（可选，但有助于垃圾回收）
    canvasline = null;
  }
  
  let canvas = document.createElement('canvas')
  canvas.width = container.offsetWidth
  canvas.height = container.offsetHeight
  canvas.id='canvasline'
  svgContainer.appendChild(canvas);
  ctx = canvas.getContext('2d');
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  ctx.beginPath();
  ctx.strokeStyle = '#3979CE';
  ctx.lineWidth = 5;

  // 遍历div元素，除了最后一个
  for (let i = 0; i < points.length - 1; i++) {
    let rect1 = points[i].getBoundingClientRect();
    let rect2 = points[i + 1].getBoundingClientRect();

    // 简化处理：连接两个div的中心点
    // ctx.moveTo(rect1.left +10+ rect1.width / 2, rect1.top +10+ rect1.height / 2);
    // ctx.lineTo(rect2.left +10+ rect2.width / 2, rect2.top +10+ rect2.height / 2);
    if (rect1.left +10!=10&&rect2.left +10!=10) {
      
      ctx.moveTo(rect1.left +10, rect1.top +10);
      ctx.lineTo(rect2.left +10, rect2.top +10);
    }
  }

  ctx.stroke();

}

const clsoe = () => {
  let _this=this
  if (getform.value.InUserName=='华东工程674') {
    
    // console.log('触发关闭');
    clearInterval(_this.timer)
    if (gjfalge.value==0) {
      app.destroy()
      viewer2D.destroy()
      dialogVisiblede.value=false
    }else{
      gjfalge.value=0
      drawi.value=1
      var canvas = document.getElementById('canvasline');
      var ctx = canvas.getContext('2d');
      ctx = null;
      posticon.value='dw'

      // 从DOM中移除Canvas元素
      if (canvas.parentNode) {
        canvas.parentNode.removeChild(canvas);
      }

      // 清除对Canvas元素的引用（可选，但有助于垃圾回收）
      canvas = null;
      getposting()
      _this.timer=setInterval(()=>{
        getposting()
      },60000)
    }
  }else{
    dialogVisiblede.value=false
  }
  
}

const open = (val) => {
  getform.value.IDCardNumber=val.IDCardNumber
  getdetil()
  dialogVisibledes.value=true
}

const getposting = async () => {
  let _this=this
  const { data: res } = await gettable('GetCardDataInfo', getform.value)

  // const  {data:res }= await this.$http.post('/aiot/Api.ashx?PostType=get&Type=GetCardDataInfo', this.getform)
  // console.log('获取人员定位',res);
  if (res.code=="1000") {
    postingrl.value=res.data
    // this.$nextTick(()=>{
      addTags(); 
    
  }
}

const gettopper = async () => {
  const { data: res } = await gettable('GetCardDataPersonInfo', getform.value)
//   const  {data:res }= await this.$http.post('/aiot/Api.ashx?PostType=get&Type=GetCardDataPersonInfo', this.getform)
  // console.log('获取人员定位',res);
  if (res.code=="1000") {
    form.value=res.data
  }
}

// 生命周期钩子
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }
})

// 暴露方法给父组件
defineExpose({
  showdelog
})
</script>
<style lang="scss">
.posting{
    margin-top: 0!important;
    padding: 0!important;
    .el-dialog__header{
        display: none!important;
    }
    .el-dialog__body{
        height: 100%;
        padding: 0!important;
    }
}
.pers{
    .el-dialog__body{
        padding: 30px 5px!important;
    }
}
.bf-toolbar-search{
    display: none!important;
}
.bf-drawing-container {
    canvas{
        // width: 100%!important;
        // height: 100%!important;
        left: 0!important;
    }
}
</style>
<style lang="scss" scoped>
.topimg{
    height: 100px;
    position: fixed;
    top: 0;
    z-index: 1000;
    width: 100%;
    background-image: url('../../../../assets/img/personnelcon/u561.png');
    background-size: 100%;
}
.trajectory{
    display: inline-block;
}
.bgimg{
    // background-image: url('../../assets/img/gzm/bgimg.png');
    background-size: 100% 100%;
}
#bre{
    width: 100px;
    height: 100px;
    margin-left: 20px;
}
#lines{
    width: 450px;
    height: 160px;
}
.echarts{
    // width: 450px;
    // height: 260px;
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 1;
    color: #fff;
    top: 0;
    &-table{
        width: 450px;
        position: absolute;
        left: 20%;
        top: 28%;
    }
    &-one{
        display: flex;
        align-items: center;
        width: 450px;
    }
    &-icon{
        width: 25%;
    }
    // background: #fff;
}
.topfq{
    display: grid;
    grid-template-columns: 20% 20% 60%;
    font-size: 18px;
    color: #fff;
    justify-items: center;
    position: relative;
    top: 10px;
}
.icons{
    width: 10px;
    height: 10px;
    border-radius: 100%;
    display: inline-block;
}
.names{
    margin: 5px 10px;
    color: #fff;
    display: flex;
    justify-content: space-between;
}
.imgsor{
    display: grid;
    grid-template-columns: 30% 70%;
    align-items: center;
    img{
        grid-row: 1/span 6;
    }
    p{
        margin: 5px 10px;
        color: #fff;
    }
}
.titles{
    display: inline-block;
    position: fixed;
    left: 42%;
    top: 40px;
    font-size:30px;
    font-weight: bold;
    color: #fff;
}
.postingsr{
    position: fixed;
    display: inline-block;
    color: #fff;
}
.imgpost{
    position: fixed;
}
.comback{
    position: fixed;
    right: 50px;
    top: 45px;
    display: flex;
    align-items: center;
    color: #66FFFF;
    font-size: 18px;
    z-index: 1000;
    img{
        width: 35px;
        height: 35px;
    }
}
.doms{
    width: 100%;
    height: 100%;
}
#line-svg{
    position: absolute;
    top: 0;
}
.dialog-content {
  height: 100%;
  position: relative;
  
  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.7);
  }
}
</style>