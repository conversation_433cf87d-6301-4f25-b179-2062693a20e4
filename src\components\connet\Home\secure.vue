<template>
  <!-- 质量 -->
  <div class="secure padding" :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="topforms"></Chamfering>

    <div class="secure-two">
      <div class="secure-two-top">
        <div class="secure-two-top1">
          <span @click="counts('安全隐患记录','')" class="cursor">总计：{{forms.AQQuestionCount}}</span>
          <span @click="counts('隐患整改记录','')" class="cursor">已整改：{{forms.RectificationNum}}</span>
          <span style="color:red"> 整改率：{{forms.RectificationRate}}%</span>
        </div>
        <div class="secure-two-top2">

        </div>
      </div>
      <div class="echatr">
        <secureechart v-if="falge" :ids="'secureechart'" 
        :refs="'secureechart'" :options1="options" @popsdelog="popsdelog"></secureechart>
      </div>
    </div>
    <delog ref="delogtbale"></delog>
    <Chamfering :homeindex="homeindex" :horn="0"></Chamfering>
  </div>
</template>

<script>
import secureechart from "@/components/connet/Common/echartscom.vue";
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import delog from "@/components/connet/safety/content/delog.vue";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
props:['homeindex'],
components:{
  secureechart,
  delog,
  Chamfering
},
setup(){
   let bgcolor=ref({})
    let getform=ref({
      ProjectCode:store.getters.code,

    })
    let quliet=ref([])
    let falge=ref(false)
    let forms=ref({})
    let topforms=ref({
      url:require('@/assets/img/home/<USER>'),
      name:'安全巡检'
    })
    let delogtbale=ref(null)
  window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
    bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    gettableqe()
  })
  // let heights='40vh-120px'
  let options=ref({})
   const gettableqe=async()=>{
    // falge.value=false
    const {data:res}=await gettable('SafeCurve',getform.value)
        // console.log('返回数据',res);
        if (res.code=="1000") {
          forms.value=res.data[0]
          quliet.value=res.data
          
        }else{
          quliet.value=[]

        }
        // forms.value=res.data[0]
        nextTick(()=>{
          falge.value=true
          getecharts1()

          // falge.value=true
        })
  }
  const getecharts1=()=>{
    // console.log('父传子');
    let addpro=[]
    let addpro1=[]
    let addpro2=[]
    let addprotime=[]
    if (quliet.value.length==3) {
    addpro= quliet.value[0].qs.map((item)=>{
      return item.CurveCount
      })
    addpro1= quliet.value[1].qs.map((item)=>{
      return item.CurveCount
      })
    addpro2= quliet.value[2].qs.map((item)=>{
      return item.CurveCount
      })
    addprotime=quliet.value[0].qs.map((item)=>{
      return item.time
      })
      }
    let option={
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        itemGap: 24,
        textStyle: {
						fontSize: '12px',
						color: '#A8D6FF',
					},
        data: ['整改隐患', '新增隐患', '待整改隐患']
      },
      grid: {
        top:'20%',
        left: '3%',
        right: '4%',
        bottom: '8%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLine: {
						lineStyle: {
							color: 'rgba(3, 251, 255, 0.3)'
						}
            
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						interval: 0,
						// textStyle: {
							color:'#03FBFF',
						// },
						// 默认x轴字体大小
						fontSize: 12,
						// margin:文字到x轴的距离
						margin: 15
					},
        data: addprotime
      },
      yAxis: {
        type: 'value',
        axisTick: {
						show: false
					},
					axisLabel: {
						// textStyle: {
							color: '#03FBFF'
						// }
					},
					 splitLine :{    //网格线
              lineStyle:{
                  type:'dashed',
                  color:'rgba(3, 251, 255, 0.3)'   //设置网格线类型 dotted：虚线   solid:实线
              },
              show:true //隐藏或显示
          }
      },
      series: [
        {
          name: '整改隐患',
          type: 'line',
          stack: 'Total',
          color:'#03FBFF',
          data: addpro1
        },
        {
          name: '新增隐患',
          type: 'line',
          stack: '',
          color:'#C80202',
          data: addpro
        },
        {
          name: '待整改隐患',
          type: 'line',
          stack: '',
          color:'#8A38F5',
          data: addpro2
        },
      ]
    }
   options.value=option
  }
  const counts=(val,name)=>{
    delogtbale.value.showdelog(val,1,name,'')
  }
  const popsdelog=(val)=>{
    // console.log('点击获取',val);
    
    delogtbale.value.showdelog(val,1,'','')
  }

return{
  // heights,
  forms,
  falge,
  quliet,
  getform,
  bgcolor,
  options,
  delogtbale,
  topforms,

  popsdelog,
getecharts1,
gettableqe,
counts,


}
}
}
</script>
<style lang="scss" scoped>
.secure{
  font-family: 'HarmonyOS Sans SC';
  &-two{
    height: 78%;
    &-top1{
      font-size: 12px;
      color: #A8D6FF;
      margin: 5px;
      span{
        display: inline-block;
        width: 28%;
        margin: 5px;
      }
    }
  }
}
.echatr{
  width: 100%;
  height: 20vh;
}
</style>