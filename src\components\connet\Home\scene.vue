<template>
<!-- 现场 -->
  <div class="scene padding" :style="{color:bgcolor.font}">
    <Chamfering :homeindex="homeindex" :horn="1" :form="topformss" @opens="opentable"></Chamfering>

    <div class="scene-content">
      <div v-for="(item,index) in project" :key="index" class="scene-content-one">
        <img src="" alt="" style="width:50px;height:50px">
        <span class="scene-content-ones">{{item.PostName}}</span>
        <span class="scene-content-one2">{{item.PostWorker}}</span>
      </div>
    </div>
    <delog ref="delogs"></delog>
    <Chamfering :homeindex="homeindex" :horn="0"></Chamfering>
  </div>
</template>

<script>
import { ref,onMounted } from 'vue'
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/Home/content/delog.vue";

export default {
props:['homeindex'],
components:{
  Chamfering,
  delog
},
setup(props){
   let bgcolor=ref({})
   let getform=ref({
      ProjectCode:store.getters.code,
      PageModular: "安全生产天数",
      PageName: "首页"

    })
  // let dd=ref
  let project=ref([
      {
        name:'项目经理',
        value:'某某某'
      },{
        name:'技术负责人',
        value:''
      },{
        name:'安全员',
        value:''
      },{
        name:'质量员',
        value:''
      },{
        name:'质检员',
        value:''
      },{
        name:'材料员',
        value:''
      }
    ])
  let topformss=ref({
    url:require('@/assets/img/home/<USER>'),
    name:'现场管理人员',
    // text:'更多记录',
    // lefs:'lefs',
    // order:'2'
  })
  let delogs=ref(null)
  window.addEventListener('setthcolor', ()=> {
      // console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
   onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      topformss.value.lefs=props.homeindex>4?'lefs':'rigs'
      topformss.value.order=props.homeindex>4?'1':'2'
      getdetil()
   })
   const opentable=()=>{
    delogs.value.showdelog(0,'现场管理')
   }
  const getdetil =async()=>{
    const {data:res}=await gettable('GetPostByProject',getform.value)
    project.value=res.data
   }

  //  GetPostByProject
 return{
    bgcolor,
    project,
    topformss,
    delogs,
    getdetil,
    opentable
  }
}
}
</script>
<style lang="scss" scoped>
.scene{
  font-family: 'HarmonyOS Sans SC';
  &-top{
      span{
        opacity: 1;
        font-size: 16px;
        letter-spacing: 0px;
        // color: #03FBFF;
  }
  }
  &-content{
        font-size: 14px;
        line-height: 21px;
        letter-spacing: 0px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        // color: #fff;
      &-ones{
        // color: #03FBFF;
        }
      &-one{
        display: grid;
        grid-template-columns:35% 65%;
        justify-items: start;
        // justify-content: space-between;
        margin: 10px 7px;
        img{
          grid-row: 1/span 2;
        }
      }
    }
}
</style>