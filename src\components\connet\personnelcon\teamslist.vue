<template>
  <!-- 出勤 -->
  <div class="teamslist padding"  :style="{color:bgcolor.font}">
    <div :class="['teamslist-top','lefticon']"
	:style="{background:'linear-gradient(90deg, '+bgcolor.titlecolor+' 0%, rgba(1, 194, 255, 0) 97%)'}"
	>     
      <div class="rightshow">
        <img :src="teamtype.src"  >
        <span class="padding-text-span" >{{teamtype.titles}}</span>
      </div>
      <el-button v-if="btns.includes(teamtype.titles)" :class="`rigs`" link  @click="getemits()" >更多告警</el-button>
    </div>
    <div class="teamslist-two">
      <div class="teamslist-two-top" ref="container">
        <swiper class="topline"  :slides-per-view="3"  :direction="'vertical'" @swiper="onSwiper"
         :autoplay="{ delay: 2000, disableOnInteraction: false }" :modules="modules" >
            <swiper-slide v-for="(row, index) in groups(teamtype.ids)" :key="index">
              <div class="row-content">
                <div v-for="(item,i) in row" :key="i" class="teamslist-two-label cursor" :title="gethove(item)" @click="changtype(item,index)">
                  <div class="teamslist-two-label-one" >
                    <div class="label-center-before tranline" :style="`background:${item.itemStyle.color}`"></div>
                    <div class="label-center" :style="`border-color:${item.itemStyle.color}`"></div>
                    <div class="label-center-after tranline" :style="`background:${item.itemStyle.color}`"></div>
                  </div>
                  <div>{{item.name}}</div>
                </div>
              </div>
            </swiper-slide>
        </swiper>
      </div>
      <div class="echatr">
        <span class="digit">{{teamtype.ids=='Typeworkcharts'?sumcount:teamcout}}</span>
        <workecharts :ids="teamtype.ids" :options="gettopline(teamtype.ids)" :dispose="dispose" @opendelog="changtype"></workecharts>
      </div>
      <tablelist ref="tablelist" v-if="falgess"></tablelist>
      <delog ref="delogtbale"></delog>
      <delog1 ref="delogtbale1"></delog1>
      <delog2 ref="delog2"></delog2>
      <delog3 ref="delog3"></delog3>
    </div>
    <Chamfering :homeindex="'1'" :horn="0"></Chamfering>
    
  </div>
</template>

<script>
import { nextTick, onBeforeUnmount, onMounted, ref,computed,watch } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import workecharts from "@/components/connet/personnelcon/echarts3d/workerkq.vue";
import tablelist from "@/components/connet/material/content/home.vue";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import delog from "@/components/connet/safety/content/delog.vue";
import delog1 from "@/components/connet/material/content/delog.vue";
import delog2 from "@/components/connet/devicelist/content/delog.vue";
import delog3 from "@/components/connet/personnelcon/content/perdelog.vue";

export default {
props:['homeindex','teamtype'],
components:{
  workecharts,
  tablelist,
  Swiper,
  SwiperSlide,
  delog,
  Chamfering,
  delog1,
  delog2,
  delog3
},
setup(props){

  let bgcolor=ref({})
  let falge=ref(0)
  let falgess=ref(false)
  let echart=ref(true)
  let countlist=ref([])
  let loading=ref(false)
  let teamcout=ref(0)
  let dispose=ref(false)
  let delogtbale1=ref(null)
  // let echartsfaleg=ref(true)
  let getform=ref({
      ProjectCode:store.getters.code,
    })
  let WorkTypecolor=[
					"#407fff",'#1F9DF5','#21F5D6','#5c2223','#eea2a4',
          "#a682e6",'#b598a1','#c08eaf','#813c85','#806d9e',
          "#e15d68",'#5e616d','#3170a7','#8fb2c9','#c3d7df',
          "#f29961",'#12a182','#737c7b','#92b3a5','#1a6840',
          "#00cccd",'#bec936','#373834','#5bae23','#e4bf11',
          "#dedede",'#b78d12','#f0d695','#b4a992','#fa5d19',
          "#FE8463",'#de7622','#f1908c','#207f4c','#22a2c3',
          "#9BCA63",'#815c94','#e16c96','#12a182','#bec936',
          '#D7504B', '#C6E579', '#F4E001', '#F0805A', '#26C0C0',
          '#FFB7DD', '#660077', '#FFCCCC', '#FFC8B4', '#550088',
          '#FFFFBB', '#FFAA33', '#99FFFF', '#CC00CC', '#FF77FF',
          '#C63300', '#9955FF', '#66FF66', '#129393', '#395203',
          '#C1232B', '#B5C334', '#FCCE10', '#E87C25', '#27727B',
          '#FAD860', '#F3A43B', '#60C0DD', '#0D7CAA'
				]
  let gridData=ref([])
  let tablelist=ref(null)
  let titles=ref('')
  let addteions=ref([])
  let teateions=ref([])
  let sumcount=ref(0)
  let delogtbale=ref(null)
  let swipersss=ref(null)
  let slids=ref(true)
  let delog2=ref(null)
  let delog3=ref(null)
  let btns=['告警类型统计分析','设备告警统计分析']

  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      
      // console.log('获取',props.teamtype.titles);材料种类验收分析
      titles.value=props.teamtype.titles
      switch (props.teamtype.titles) {
        case '工种考勤分析':
	        getsunm()
          // getWorkerTeam()
          break;
        case '班组考勤统计':
	        // getsunm()
          getWorkerTeam()

          break;
        case '质量问题类型分析':
          getquestion()

          break;
        case '材料种类验收分析':
          // console.log('质量问题分析');
          getmaterType()
          break;
        case '供货商验收统计':
          // console.log('质量问题分析');
          GetSupplierNameCheck()
          break;
        case '设备告警统计分析':
          // console.log('质量问题分析');
          gethispy()
          break;
        case '告警类型统计分析':
          // console.log('质量问题分析');
          getcurrent()
          break;
      }
  })
  // 获取工种考勤
  const getsunm=async()=>{
    let wokrteype=ref([])

    const {data:res}=await gettable('WorkerType',getform.value)
    // console.log('获取考勤',res);

    wokrteype.value=res.data
    if (res.code=="1000") {
    addteions.value = wokrteype.value.map((item,index)=>{
        // console.log('获取考勤工种',parseInt(item.Workvalue));+parseInt(sumcount.value)
        sumcount.value +=parseInt(item.Workvalue)
        return{
          name:item.Workname,
          value:parseInt(item.Workvalue),
          value1:item.attendance,
          itemStyle:{
            color:WorkTypecolor[index]
          }
        }
      })
      groups(addteions.value)
    }
    // console.log('获取考勤',sumcount.value);
    
  }
  const getWorkerTeam=async()=>{
    let WorkerTeam=ref([])
    const {data:res}=await gettable('WorkerTeam',getform.value)
      // console.log('班组信息',res);
      WorkerTeam.value=res.data
      if (res.code=="1000") {
        teateions.value = WorkerTeam.value.map((item,index)=>{
        // console.log('获取考勤工种',parseInt(item.Workvalue));+parseInt(sumcount.value)
        teamcout.value +=parseInt(item.Teamvalue)
        return{
          name:item.Teamname,
          value:parseInt(item.Teamvalue),
          value1:item.attendance,
          itemStyle:{
            color:WorkTypecolor[index]
          }
        }
      })
      }
  }
  // 质量问题类型分析
  const getquestion=async()=>{
    let WorkerTeam=ref([])
    const {data:res}=await gettable('QualityPatTypeShow',getform.value)
      // console.log('质量问题类型分析',res);
      WorkerTeam.value=res.data
      if (res.code=="1000") {
        teateions.value = WorkerTeam.value.map((item,index)=>{
        // console.log('获取考勤工种',parseInt(item.Workvalue));+parseInt(sumcount.value)
        teamcout.value +=parseInt(item.count)
        return{
          name:item.QualityPatType,
          value:parseInt(item.count),
          value1:0,
          itemStyle:{
            color:WorkTypecolor[index]
          }
        }
      })
      }
  }
  // 获取材料种类验收分析
  const getmaterType=async()=>{
    let WorkerTeam=ref([])
    const {data:res}=await gettable('GetMaterialTypeCheck',getform.value)
      // console.log('材料种类验收分析',res);
      WorkerTeam.value=res.data.CheckList
      if (res.code=="1000") {
        teamcout.value=res.data.AllCount
        teateions.value = WorkerTeam.value.map((item,index)=>{
          // console.log('获取',item);
          
        // console.log('获取考勤工种',parseInt(item.Workvalue));+parseInt(sumcount.value)
        // teamcout.value +=parseInt(item.count)
        return{
          name:item.name,
          value:parseInt(item.value),
          value1:item.Percent,
          itemStyle:{
            color:WorkTypecolor[index]
          }
        }
      })
      }
  }
  // 获取供货商GetSupplierNameCheck
  const GetSupplierNameCheck=async()=>{
    let WorkerTeam=ref([])
    const {data:res}=await gettable('GetSupplierNameCheck',getform.value)
      // console.log('获取供货商',res);
      WorkerTeam.value=res.data.CheckList
      if (res.code=="1000") {
        teamcout.value=res.data.AllCount
        teateions.value = WorkerTeam.value.map((item,index)=>{
        return{
          name:item.name,
          value:parseInt(item.value),
          value1:0,
          itemStyle:{
            color:WorkTypecolor[index]
          }
        }
      })
      // console.log('获取',teateions.value);
      
      }
  }
  // 当期告警分析
  const getwaring=async()=>{

  }
  // 历史告警分析
  const gethispy=async()=>{
    let WorkerTeam=ref([])
    const {data:res}=await gettable('GetEquipHisWarningAnalysis',getform.value)
      // console.log('历史分析',res);
      WorkerTeam.value=res.data.ECList

      if (res.code=="1000") {
      teamcout.value=res.data.Total
      gridData.value=res.data.WarnTable
        teateions.value = WorkerTeam.value.map((item,index)=>{
        return{
          name:item.name,
          value:parseInt(item.value),
          value1:0,
          itemStyle:{
            color:WorkTypecolor[index]
          }
        }
      })
  }
  }
  // 当前告警分析
  const getcurrent=async()=>{
    let WorkerTeam=ref([])
    const {data:res}=await gettable('GetWarningTypeAnalysis',getform.value)
      // console.log('历史分析',res);
      WorkerTeam.value=res.data.ECList

      if (res.code=="1000") {
      sumcount.value=res.data.Total
      gridData.value=res.data.WarnTable
        addteions.value = WorkerTeam.value.map((item,index)=>{
        return{
          name:item.name,
          value:parseInt(item.value),
          value1:0,
          itemStyle:{
            color:WorkTypecolor[index]
          }
        }
      })
  }
  }
  const isLongText = (text) => {
      const containerWidthPx = 340;
      const averageCharWidth = 12; // 假设每个字符平均16px宽
      const textWidth = text.length * averageCharWidth;
      return textWidth > containerWidthPx; // 如果文本宽度超过容器40%则视为长文本
    };
  const getemits=()=>{
    delog2.value.showdelog('',props.teamtype.titles)
  }
  // 346
  const groups=(val)=>{
    // console.log('截取数据',addteions.value);
      const result = [];
      let currentRow = [];
      let currentRowWidth = 0;
      const maxRowWidth = 340 - 60; // 考虑padding和间距
      // slids.value=false
      gettopline(val).forEach(item => {
        const textWidth = item.name.length * 12; // 估算文本宽度
        const itemMargin = 20; // 项目间距
        const itemTotalWidth = textWidth + itemMargin;

        if (currentRowWidth + itemTotalWidth > maxRowWidth) {
          if (currentRow.length > 0) {
            result.push([...currentRow]);
            currentRow = [];
            currentRowWidth = 0;
          }
          currentRow.push(item);
          currentRowWidth = itemTotalWidth;
        } else {
          currentRow.push(item);
          currentRowWidth += itemTotalWidth;
        }
      });
      
      if (currentRow.length > 0) {
        result.push(currentRow);
      }
      // slids.value=true

      // swiper.value.autoplay.start();
      return result;
  }
  // 获取鼠标移入数据
  const gethove=(item)=>{

    return item.name+' '+(titles.value=='材料种类验收分析'?item.value+'批次'+'\n'+'占比'+item.value1+'%':'\n'
            +'占比'+item.value+'%')
  }
  const onSwiper = (swiper) => {
    // console.log('自动启动滚动',swiper);
      swiper.autoplay.start();
    };
  // 顶部数据
  const gettopline=(val)=>{
      // console.log('班组考勤数据',val,addteions.value);
    return val=='Typeworkcharts'?addteions.value:teateions.value
  }
  const changtype=(val,index)=>{
    // console.log('点击',val,index);
    let values= val.seriesName?val.seriesName:val.name
    falgess.value=true
    nextTick(()=>{
      switch (titles.value) {
      case '材料种类验收分析':
        // tablelist.value.showdelog('材料验收记录',values)
      delogtbale1.value.showdelog(values,'材料种类验收分析')

        break;
      case '供货商验收统计':
      delogtbale1.value.showdelog(values,'供货商验收统计')
        break;
      case '质量问题类型分析':
        delogtbale.value.showdelog('质量问题记录',0,'',values)
        break;
      case '工种考勤分析':
        delog3.value.showdelog(values,'工种考勤分析')
        break;
      case '班组考勤统计':
        // console.log('班组考勤分析',values);
        
        delog3.value.showdelog(values,'班组考勤统计')
        break;
    }
    })
    
  }
  const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
  onBeforeUnmount(()=>{
    console.log('销毁表图');
    dispose.value=true
    // echartsfaleg.value=false
  })

	return{
		falge,
    tablelist,
    falgess,
    loading,
    teamcout,
    echart,
		countlist,
    teateions,
		getform,
		bgcolor,
    WorkTypecolor,
    titles,
    sumcount,
    addteions,
    delogtbale1,
    btns,
    delog2,
    delog3,

    isLongText,
    modules: [Autoplay, Pagination, Navigation],
    swipersss,
    slids,
    delogtbale,
    dispose,
    getcurrent,
    gettopline,
    // updateContainerWidth,
    gridData,
    tableRowClassName,
    getWorkerTeam,
    getquestion,
    getmaterType,
    GetSupplierNameCheck,
    changtype,
    // opendelog,
    groups,
    gethove,
    onSwiper,
    getwaring,
    gethispy,
    getemits
  

	}
}
}
</script>
<style lang="scss">
.workerpopove{
.el-table{
    --el-table-tr-bg-color:transparent!important;
    // border:1px solid transparent!important;
    // --el-table-border-color:transparent;
    --el-table-row-hover-bg-color:transparent;
    --el-table-bg-color:transparent;
}
.el-table .warning-row {
//   --el-table-tr-bg-color: #000 !important;
  background: rgba(15, 43, 63, 0.6)!important;
}
}
// :deep(.swiper-wrapper) {
//     transition-timing-function: linear !important; /* 没错就是这个属性 */
// }
.topline{
  height: 100%!important;
  .swiper-slide{
    height: 20px!important;
  }
  .swiper-wrapper {
    transition-timing-function: linear !important; /* 没错就是这个属性 */
}
}
</style>
<style lang="scss" scoped>
.teamslist{
   .rightshow{
    display: flex;
    align-items: center;
  }
  &-top{
    justify-content: space-between;

  }
.rigs{
  color: #fff;
}

.row-content {
  display: flex;
  flex-wrap: wrap;
  max-width: 340px;
  justify-content: flex-start;
  align-items: stretch;
}
&-two{
  // height: 80%;
  height: 220px;
  .lables{
    display: flex;
    flex-wrap: wrap;
  }
 
  &-label{
    display: flex;
    align-items: center;
    padding: 5px;
    .label-center{
      width: 10px;
      height: 10px;
      border-radius: 100%;
      border: 1px solid #000;
    }
    .tranline{
        width: 5px;
        height: 2px;
        background: #000;
      }
  &-one{
    display: flex;
    align-items: center;
    margin-right: 5px;
    // position: relative;
  }
  }
  &-top{
    height: 27%;
    font-size: 12px;
  }
  &-btn{
    padding: 10px;
    width: 80%;
    position: relative;
    border: 2px solid #03558F;
    // margin: 10px;
  }
  .bgcolorcz{
        background: #F29961;

    }
    .bgcolorsj{
          background: #4582ff;

    }
}
}
.echatr{
  // display: flex;
  // align-items: center;
  position: relative;
  width: 100%;
  height: 80%;
  .digit{
    position: absolute;
    left: 41%;
    top: 24%;
    z-index: 1;
    font-size: 37px;
    font-weight: 900;
  }
}

</style>