<template>
  <!-- 偏差分析 -->
  <div class="Requisition padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"
      @opens="opentable()" ></Chamfering>
    <div class="Requisition-two">
        <el-table :data="gridData"  :style="['width: 100%',`color:${bgcolor.font};
        --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
        :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
        empty-text="暂无数据" :loading="loading" max-height="200px" 
        >
            <el-table-column  prop="TakingDate" align="center" label="领取日期" width="70"></el-table-column>
            <el-table-column  prop="MaterialName" align="center"  label="材料名称"  width="70"></el-table-column>
            <el-table-column  prop="MaterialModel" align="center"  label="规格型号" width="70"></el-table-column>
            <el-table-column  prop="TakingNum" align="center"  label="数量" width="50"></el-table-column>
            <el-table-column  prop="WorkerName" align="center"  label="领取人" width="65"></el-table-column>
        </el-table>
      
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    <delog ref="delog"></delog>
  </div>
</template>

<script>
import { nextTick, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import attecahrt from "@/components/connet/personnelcon/echarts3d/attecahrt.vue";
import Chamfering from "@/components/connet/Common/Chamfering.vue";
import delog from "@/components/connet/material/content/delog.vue";
import store from "@/store";
export default {
components:{
    attecahrt,
    Chamfering,
    delog
},
setup(){

  let bgcolor=ref({})
  let loading=ref(false)
  let getform=ref({
      ProjectCode:store.getters.code,
      page:1,
      count:1000
    })
  let gridData=ref([])
  let options1=ref([])
  let echartcount=ref(true)
  let topforms=ref({
    url:require('@/assets/img/material/Requisition.png'),
    name:"领用记录",
    text:'更多记录',
    lefs:'lefs'
  })
  let delog=ref(null)
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
      gettableRecord()
  })
  const opentable=()=>{
    console.log('打开');
    delog.value.showdelog(null,'领用记录列表')
  }
  const gettableRecord=async()=>{
      loading.value=true
      const {data:res}=await gettable('GetTakingRecordTable',getform.value)
      // console.log('或',res);
      
      loading.value=false
      gridData.value=res.data
  }
  const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }

	return{
    echartcount,
    loading,
		getform,
		bgcolor,
    gridData,
    topforms,
    delog,

    tableRowClassName,
    gettableRecord,
    opentable
	// getRequisition,
	}
}
}
</script>
<style lang="scss">
.Requisition{
  .el-table{
    --el-table-row-hover-bg-color:rgba(1, 194, 255, 0.6);
  }
  .Requisition-two{
//     .pice{
//     font-size: 12px;
//     }
    .el-table .cell{
      padding:  0 4px!important;
    }

  }
  .el-table .warning-row {
    background: rgba(15, 43, 63, 0.6)!important;
  }
}

</style>
<style lang="scss" scoped>
.Requisition{
&-two{
  height: 86%;
  padding: 10px;
//   display: grid;
//   grid-template-columns: 45% 55%;
//   align-items: center;
  &-one{
    width: 100%;
    height: 100%;
  }
  &-table{
    width: 100%;
    height: 100%;
    padding: 5px;
  }
//   #ghsechart{
//     width: 100%;
//     height: 100%;
//   }
}
}



</style>