<template>
  <!-- 在线监控 -->
  <div class="monitor padding"  :style="{color:bgcolor.font}">
    <Chamfering :classname="'heighttop'" :homeindex="4" :horn="1" :form="topforms"></Chamfering>

    <div class="monitor-two">
      <div class="monitor-two-top">
        <div v-for="(item,index) in btnlist" :key="index" :class="['monitor-two-btn cursor',{'changindex':falge==index}]" 
        :style="[falge==index?`background:${bgcolor.changcolor};color:#FFF`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
         rgba(7, 93, 184, 0.6) 100%);color:#FFF`]" @click="change(item,index)"
        >{{item}}</div>
 
      </div>
      <div class="echatr">
        <div class="monitorlistcout"  @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave" @dblclick="dbpaly()">
          <div class="monitorlist cursor" id="monitorlist"></div>
          <div class="monitorlistcout-two cursor" :style="{'display':showdis ? 'block' : 'none'}" @click="togglePlay()">
            <el-icon class="Videoplay" v-if="falgeplay"><VideoPause /></el-icon>
            <el-icon class="Videoplay" v-else-if="!falgeplay"><VideoPlay /></el-icon>
          </div>
        </div>
        <div class="echatr-one">
            <el-scrollbar>
                <div class="scrollbar-flex-content">
                    <div v-for="(item,index) in tables " :key="index" class="echatr-one-name cursor" 
                    :style="[falge1==index?`background:${bgcolor.changcolor};color:#FFF`:`background: linear-gradient(108deg, ${bgcolor.bgcolor} 8%,
                    rgba(7, 93, 184, 0.6) 100%);color:#FFF`]" @click="change1(item,index)">
                        <p>{{item.EquipName}}</p>
                    </div>
                </div>
            </el-scrollbar>
        </div>
        
      </div>
    </div>
    <Chamfering :homeindex="4" :horn="0"></Chamfering>
    
  </div>
</template>

<script>
import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
import Chamfering from "@/components/connet/Common/Chamfering.vue";

export default {
components:{
// echartsmonitor
// attecahrt,
Chamfering
},
setup(){

  let bgcolor=ref({})
  let falge=ref(0)
  let falge1=ref(0)
  let token=ref('')
//   let echart=ref(true)
//   let countlist=ref([])
  let getform=ref({
      ProjectCode:store.getters.code,
      Type:'塔式起重机'

    })
  let player=ref(null)
  let falgeplay=ref(false)
  let showdis=ref(true)
  let tables=ref([])
  let btnlist=['塔式起重机','施工升降机','卸料平台']
  let addteion=ref([])
  let Attendslist=ref([])
  let topforms=ref({
    url:require('@/assets/img/device/monitor.png'),
    name:"在线监控"
  })
  window.addEventListener('setthcolor', ()=> {
    //   console.log('第一个模块');
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
  onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))

    // getmonitor()
	  getsunm()
    // getmangen()
    // getconstruction()
    // gettoken()

  })
  const getsunm=async()=>{

    let tablelist=[
          {
            EquipCode: "浙AD-T02466",
            EquipName: "1号塔机",
            VideoLink: "ezopen://open.ys7.com/AB3598698/1.live"
          },
          {
            EquipCode: "浙AD-T02466",
            EquipName: "2号塔机",
            VideoLink: "ezopen://open.ys7.com/AB3598471/1.live"
          },
          {
            EquipCode: "浙AD-T02466",
            EquipName: "3号塔机",
            VideoLink: "ezopen://open.ys7.com/J66396745/1.live"
          }
        ]
    const {data:res}=await gettable('GetEquipMonitorAddr',getform.value)
    // console.log('获取监控',res);
    if (res.code=="1000") {
      // tables.value=res.data
      tables.value=tablelist

      // gettoken(res.data[0])
      gettoken(tables.value[0])
    }
    
  }
  const gettoken=async(val)=>{
        // console.log('获取token',store.getters.username);
        
        let gettablesmodes={
            InUserName:store.getters.username,
            Type:'掌勤扬尘'
        }
        const {data:res}=await gettable('GetElevatorMonitoringToken',gettablesmodes)
        // console.log('获取token',res);
        if (res.code=="1000") {
            token.value=res.data.token
            // playvioce(val)
        }

    }
  // 监控
  const playvioce=(val)=>{
        // console.log('播放',player.value);
        if (player.value) {
          try {
            player.value.stop();
            if (typeof player.value.destroy === 'function') {
              player.value.destroy();
            }
          } catch (error) {
            console.error('停止播放器时出错:', error);
          }
        }
        if (val.VideoLink) {
        player.value =  new EZUIKit.EZUIKitPlayer({
				  autoplay: true,
				  id:'monitorlist',
				  accessToken:token.value,
				  url:val.VideoLink,
          width:340,
          height:140,
				  template: "simple", // simple - 极简版;standard-标准版;security - 安防版(预览回放);voice-语音版；
          handleError:(res)=>{
          console.log('播放错误回调',res);
          }
          });
        }

    }
  const togglePlay=(val)=>{
    // console.log('点击是否播放',player.value,falgeplay.value);
    
      switch (falgeplay.value) {
        case true:
          if (player.value) {
            try {
              player.value.stop();
            } catch (error) {
              console.error('停止播放器时出错:', error);
            }
          }
          falgeplay.value = false;
          break;
        case false:
          if (player.value) {
          player.value.play();
          }else{
            nextTick(()=>{
              if (tables.value.length>0) {
              playvioce(tables.value[falge1.value])
              }
            })
          }
          falgeplay.value= true;
          break; 
      }

    }
  const handleMouseEnter=()=>{
    // console.log('鼠标移入');
    showdis.value=true
  }
  const handleMouseLeave=()=>{
    // console.log('鼠标移出');
    if (falgeplay.value) {
    showdis.value=false
    }
  }
  // 双击放大
  const dbpaly=()=>{
    if (player.value) {
      // console.log('双击',player.value,);
      if (player.value.fullScreen()) {
      }else{
        player.value.cancelFullScreen()

      }
    }
  }
  const change1=(val,idnex)=>{
    falge1.value=idnex

      if (player.value) {
        try {
          player.value.stop();
          if (typeof player.value.destroy === 'function') {
            player.value.destroy();
          }
          player.value = null;
          falgeplay.value = false;
          showdis.value = true;
        } catch (error) {
          console.error('停止播放器时出错:', error);
          player.value = null;
          falgeplay.value = false;
          showdis.value = true;
        }
      }
  //       }else{
  //         playvioce(val)
  //       }
  //   })
  }
  const change=(val,idnex)=>{
    falge.value=idnex
    getform.value.Type=val
    getsunm()
  }
  // beforeUnmount
  onBeforeUnmount(()=>{
    // console.log('离开当前页面');
    if (player.value) {
      try {
        player.value.stop();
        if (typeof player.value.destroy === 'function') {
          player.value.destroy();
        }
        player.value = null;
      } catch (error) {
        console.error('销毁播放器时出错:', error);
      }
    }
  })
 

	return{
		falge,
    falge1,
    falgeplay,
    topforms,

    // echart,
		// countlist,
		getform,
		bgcolor,
    player,
    btnlist,
    addteion,
    Attendslist,
    change,
    change1,
    gettoken,
    playvioce,
    togglePlay,

    tables,
    token,
    showdis,
    handleMouseEnter,
    handleMouseLeave,
    dbpaly
	// getmonitor,
	}
}
}
</script>

<style lang="scss" scoped>
.monitor{
&-two{
  height: 86%;
  &-top{
    font-size: 12px;
    display: grid;
    grid-template-columns: repeat(3,33.3%);
    // margin-top: 10px;
    // padding: 10px 0;
  }
  &-btn{
    padding: 10px;
    width: 80%;
    position: relative;
    border: 2px solid #03558F;
    // margin: 10px;
  }
  .bgcolorcz{
        background: #F29961;

    }
    .bgcolorsj{
          background: #4582ff;

    }
}
}
.changindex::before{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        left: -2px;
        top: -2px; 
        opacity: 1;
        border-top: 2px solid #E0A538;
        border-left: 2px solid #E0A538;
    }
.changindex::after{
        content: '';
        width: 10.06px;
        height: 10.84px;
        position: absolute;
        right: -2px;
        bottom: -2px; 
        opacity: 1;
        border-bottom: 2px solid #E0A538;
        border-right: 2px solid #E0A538;
    }
.echatr{
//   display: grid;
//   grid-template-rows: 80% 10%;
    display: flex;
    flex-direction: column;
  width: 100%;
  height: 74%;
  .monitorlist{
    width: 100%;
    height: 123px;
    background: #000;
    
    
  }
  .monitorlistcout{
    position: relative;
    width: 100%;
    height: 123px;
    display: flex;
    align-items: center;
    justify-content: center;
    &-two{
      position: absolute;
    }
  }
  .Videoplay{
      font-size: 50px!important;
    }
  .scrollbar-flex-content{
        display: flex;
        height: 10%;
    }
    &-one{
        width: 100%;
        height: 18%;
        margin-top: 5px;
        &-name{
            display: flex;
        align-items: center;
        height: 29px;
        width: 100px;
        margin: 0 10px;
        opacity: 1;
        padding: 10px;
        color: #fff;
        font-size: 12px;
        clip-path: polygon(0 0px, -61px 100%, 5px 20px, 14% 100%, 100% 100%, 100% 0, 0 0);
        background: #4582ff;
        P{
            width: 75PX;
        }
        }
    }
}

</style>