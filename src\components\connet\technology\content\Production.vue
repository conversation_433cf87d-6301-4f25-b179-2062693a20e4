<template>
  <div class="technology-produce" :style="[`border:2px solid ${bgcolor.titlecolor};
    background:rgba(${bgcolor.delogcolor},0.35)`]">

    <div v-for="(item,index) in titlesp" :key="index" :class="'lable'+index">
        <div class="datedelog-header" :style="`background:linear-gradient(90deg, ${bgcolor.titlecolor} 0%,
            rgba(2, 193, 253, 0) 89%);color:${bgcolor.font}`">
          <div class="datedelog-body-one">
              <img src="@/assets/img/home/<USER>" alt="">
              <p>{{item}}</p>
              <div v-if="item=='项目进度照片'" class="nice">{{count}}</div>
              <el-progress
                v-else-if="item=='工程计划预警分析'"
                :text-inside="true"
                :stroke-width="20"
                class="progress"
                :percentage="percentage"
                striped
                striped-flow
                :duration="duration"
                status="exception"
                />

          </div>
        </div>
        <div class="imgswiper" v-if="item=='项目进度照片'">
                <swiper
                    :slides-per-view="3"
                    :navigation="{
                        nextEl: '.swiper-button-next', //前进后退按钮
                        prevEl: '.swiper-button-prev',
                    }"
                    :space-between="20"
                    :autoplay="{ disableOnInteraction: false }" 
                    class="teacher_ul"
                    >
                    <swiper-slide class="teacher_li" v-for="(item, index) in piclist" :key="index" v-show="item.IsShow=='展示中'">
                        <div class="teacher_pW">
                            <img v-lazy="item.ImgUrl" :key="index" @click="pic(item.ImgUrl)" alt="" class="cursor" style="width: 100%;height: 100%;">
                            <div class="teacher_pW-bottom">
                                {{item.PhotoDate}}
                            </div>
                            <div class="teacher_pW-top">
                                {{item.index+1}}
                            </div>
                        </div>
                    </swiper-slide>
                    
                </swiper>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
        </div>
        <div v-else-if="item=='工程计划预警分析'" class="technology-table" :style="`color:${bgcolor.font}`">
            <p class="technology-left">已完成计划列表：<span style="color:red">{{tableform.CompletionPlan}}</span></p>
            <p class="technology-right">未完成计划列表：<span style="color:red">{{tableform.InCompletePlan}}</span></p>
            <div>
            <el-table :data="gridData1" class="tableleft"  :style="['width: 99%;height:100%;',`color:${bgcolor.font};
            --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
            :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
            empty-text="暂无数据" :loading="loading" max-height="200px" 
            >
                <el-table-column  prop="rowNum" align="center" label="序号" width="80"></el-table-column>
                <el-table-column  prop="PlanName" align="center"  label="工程计划名称" ></el-table-column>
                <el-table-column  prop="PlanEndDate" align="center"  label="计划完成时间" ></el-table-column>
                <el-table-column  prop="RealEndDate" align="center"  label="实际完成时间" ></el-table-column>
            </el-table>
            </div>
            <div>
            <el-table :data="gridData"  :style="['width: 99%;height:100%;',`color:${bgcolor.font};
            --el-table-border-color:${bgcolor.titlecolor}`]" :row-class-name="tableRowClassName"
            :header-cell-style="{background:'rgba(15, 43, 63, 0.6)',color:'#fff'}"
            empty-text="暂无数据" :loading="loading" max-height="200px" 
            >
                <el-table-column  prop="rowNum" align="center" label="序号" width="80"></el-table-column>
                <el-table-column  prop="PlanName" align="center"  label="工程计划名称" ></el-table-column>
                <el-table-column  prop="PlanBeginDate" align="center"  label="计划开始时间"></el-table-column>
                <el-table-column  prop="PlanEndDate" align="center"  label="实际结束时间" ></el-table-column>
                <el-table-column  prop="RemainDays" align="center"  label="剩余天数"></el-table-column>
                <el-table-column  prop="PlanCompletioned" align="center"  label="完成度"></el-table-column>
            </el-table>
            </div>
            
        </div>
        <div v-else-if="item=='项目里程碑'" class="Milestones">
            <swiper
                :slides-per-view="3"
                :navigation="{
                    nextEl: '.swiper-button-next2', //前进后退按钮
                    prevEl: '.swiper-button-prev2',
                }"
                :space-between="20"
                :autoplay="{ disableOnInteraction: false }" 
                @slideChangeTransitionEnd="onSlideChangeTransitionEnd"
                @swiper="onSwiper"
                class="Milestones-ul"
                >
                <swiper-slide class="Milestones-li" v-for="(item, index) in Milestonesref" :key="index">
                    <div class="lineleft" :style="{borderColor:bgcolor.chamfer}"></div>
                    <div class="Milestones-box" :style="{borderColor:bgcolor.chamfer}">
                        <div class="Milestones-box-content">
                            <p>桩基及维护施工</p>
                            <p>预计完成时间:{{item.MilepostEndTime}}</p>
                            <p>实际完成时间:{{item.RealEndTime}}</p>
                            <img v-if="item.MilepostStatus=='进行中'" src="@/assets/img/technology/progress.png" alt="">
                            <img v-else-if="item.MilepostStatus=='已完成'" src="@/assets/img/technology/Completed.png" alt="">
                            <img v-else-if="item.MilepostStatus=='未开始'" src="@/assets/img/technology/Notstarted.png" alt="">
                        </div>
                        <div  v-for="(item,index) in ange" :key="index" :class="item" 
                            :style="{borderColor:bgcolor.chamfer}"
                        ></div>
                    </div>
                    <div class="lineleft"></div>

                </swiper-slide>
            </swiper>
            <div class="swiper-button-next2 swoper cursor">
                <el-icon  class="next2"><ArrowLeft /></el-icon>
            </div>
            <div class="swiper-button-prev2 swoper cursor">
                <el-icon  class="prev2"><ArrowRight /></el-icon>
            </div>
        </div>
    </div>
    <picimg ref="picimg"></picimg>
    
  </div>
</template>

<script>
import { nextTick, onMounted, onUnmounted, ref ,computed} from 'vue';
import 'swiper/swiper-bundle.css'
import SwiperCore, {
  Autoplay,
} from "swiper";
//例如
import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/vue'
SwiperCore.use([Navigation, Pagination, Scrollbar, Autoplay]);
import picimg from "@/components/connet/Common/picimg.vue";
import { gettable,setdata,deldata} from "@/network/api/requestnet";
import store from "@/store";
export default {
components:{
        Swiper,
        SwiperSlide,
        picimg
    },
setup(){
    let bgcolor=ref({})
    let mySwiper=ref(null)
    let picimg=ref(null)
    let percentage=ref(0)
    let loading=ref(false)
    let gridData=ref([
    {
      date:'541'
    },
    {
      date:'541'
    },{
      date:'541'
    },
    {
      date:'541'
    },{
      date:'541'
    },
    {
      date:'541'
    },
  ])
    let gridData1=ref([])
    let piclist=ref([])
    let count=ref('')
    let getform=ref({
        ProjectCode:store.getters.code,
    })
    let titlesp=['项目进度照片','工程计划预警分析','项目里程碑']
    let ange=['rightbefore','rightafter','leftbefore','leftafter']
    let Milestonesref=ref([])
    let tableform=ref({})
    const duration = computed(() => Math.floor(percentage.value / 10))
    window.addEventListener('setthcolor', ()=> {
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
   })
    onMounted(()=>{
      bgcolor.value=JSON.parse(sessionStorage.getItem('themecolor'))
        getpic()
        gettopleft()
        getmilepost()
        gettableles()
        // gettablerig()
    })
    const pic=(val)=>{
        picimg.value.piclist(val)
    }
    const tableRowClassName=({row,rowIndex,})=>{
        // console.log('获取当前行',row,rowIndex);
        if (rowIndex%2 != 0) {
            return 'warning-row'
        }
        return ''
    }
    // GetQualityAlbumSel
   const getpic=async()=>{
        let i=0
        const {data:res}=await gettable('GetQualityAlbumSel',getform.value)
        // console.log('获取项目照片',res);
        if (res.code=="1000") {
            piclist.value=res.data

            piclist.value.map((item,index)=>{
                if (item.IsShow=='展示中') {
                    item.index=i++
                }
                // item.count=i
            })
            count.value=i
        }
    }
    const onSlideChangeTransitionEnd=(val)=>{
        // console.log('触发',val);

        
    }
    const onSwiper=(val)=>{
        // console.log('打印',val);
        
    }
    // 已完成列表
    const gettableles=async()=>{
        const {data:res}=await gettable('GetCompletionedPlan',getform.value)
        if (res.code=="1000") {
            gridData1.value=res.data
        }
    }
    const gettablerig=async()=>{
        // const {data:res}=await gettable('GetProjectPlanAnaly',getform.value)
        // console.log('获取',res);
        
        // if (res.code=="1000") {
        //     gridData.value=res.data
        // }
    }
    const gettopleft=async()=>{
    const {data:res}=await gettable('GetProjectPlanAnaly',getform.value)
    // console.log('获取预警分析',res);
    
    if (res.code=="1000") {
        tableform.value=res.data[0]
    percentage.value=res.data[0].CompletionRate
    }
    gridData.value=res.data[0].Table
      
    }
    // 里程碑GetMilepostBytimeAsc
    const getmilepost=async()=>{
        const {data:res}=await gettable('GetMilepostBytimeAsc',getform.value)
        // console.log('里程碑',res);
        if (res.code=="1000") {
            Milestonesref.value=res.data
        }
    }
    onUnmounted(()=>{
    //     if (mySwiper.value) {
    //   mySwiper.value.destroy()
    //   mySwiper.value = null
    // }
    })
    return{
        bgcolor,
        loading,
        count,
        mySwiper,
        tableform,
        piclist,
        picimg,
        duration,
        percentage,
        gridData,
        titlesp,
        gridData1,
        ange,
        Milestonesref,
        pic,
        tableRowClassName,
        onSwiper,
        onSlideChangeTransitionEnd,
        getpic,
        gettopleft,
        getmilepost,
        gettableles,
        gettablerig
    }
}
}
</script>
<style lang="scss">
.technology-table{
    .el-table{
        color: #fff!important;
    --el-table-row-hover-bg-color:rgba(1, 194, 255, 0.6);
  }
  .el-table .warning-row {
    background: rgba(15, 43, 63, 0.6)!important;
  }
  
}
.imgswiper{
    .swiper-button-prev, .swiper-button-next{
    top: 35%!important;
  }
}
</style>
<style lang="scss" scoped>
.technology-produce{
    height: 93%;
    padding: 20px;
    width: 100%;
}
.swiper-button-disabled{
    display:none!important;
  }
.datedelog-header{
    margin: 10px 0!important;
}
.el-progress--line {
//   margin-bottom: 15px;
margin-left: 20px;
  width: 350px;
}
.tableleft{
    // padding-right: 10px;
}
.lable1{
height: 40%;
}
.lable2{
height: 25%;
}
.technology-table{
    display: grid;
    grid-template-columns: 40% 60% ;
    p{
        padding: 10px;
        font-size: 18px;
        font-weight: bold;
    }
}
.technology{
&-left{
    text-align: start;
}
&-right{
    text-align: end;
}

}

.datedelog-body-one{
    p{
        font-weight: bold;
        font-size: 18px;
    }
    .nice{
        width: 25px;
        height: 25px;
        border-radius: 100%;
        background: #7C495F;
        line-height: 25px;
        margin-left: 10px;
    }
}
.imgswiper{
    position: relative;
    
}
.swiper-button-next2{
        left: 0;
    }
.swiper-button-prev2{
        right: 0;
    }
.swoper{
    position: absolute;
    top: 35%;
    .el-icon{
        font-size: 60px;
        color: #03FBFF;
    }
    
}
.lineleft{
    width: 20%;
    margin-left: 5px;
    // height: 5px;
    border-bottom: 5px dotted #03FBFF;
}
.Milestones{
    position: relative;
    height: 70%;
    &-li{
        width: 355px!important;
        margin: 0!important;
        display: flex;
        justify-content: center;
        align-items: center;
    }

&-box{
    position: relative;
    width: 96%;
    height: 80%;
    border: 1px solid;
    background: rgba(15, 43, 63, 0.6)!important;
    &-content{
        height: 100%;
        width: 100%;
        padding: 10px 0px;
        p:nth-child(1){
            font-size: 16px;
            font-weight: bold;
            padding-bottom: 20px;
        }
        p{
            padding:5px 10px;
            text-align: start;
            font-size: 12px;
        }
        img{
            position: absolute;
            top: 25%;
            right: 2%;
        }
    }
}
}
.Milestones-ul{
    height: 150px;
    width: 93%;
}
.teacher_ul{
    height: 150px;
    width: 90%;
    .teacher_li{
        height: 100%;
    }
}
.teacher_pW{
    width: 100%;
    height: 100%;
}
// .teacher_pW{
//         height: 100%;
//         position: relative;
//     img{
//         height: 100%;
//         width: 100%;
//     }
//     &-bottom{
//         position: absolute;
//         display: flex;
//         align-items: center;
//         padding-left: 10px;
//         bottom: 0px;
//         width: 100%;
//         height: 20%;
        
//         background: rgba(15, 43, 63, 0.6);
//     }
//     &-top{
//         position: absolute;
//         top: 5px;
//         left: 5px;
//         width: 20px;
//         height: 20px;
//         background: red;
//         border-radius: 100%;
//         line-height: 21px;
//     }
// }
</style>