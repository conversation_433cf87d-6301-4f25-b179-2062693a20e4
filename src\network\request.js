import axios from 'axios'

// axios 配置
const service = axios.create({
    baseURL: process.env.VUE_APP_API_BASE_URL, // url = base url + request url 配置代理后的请求地址
    // withCredentials: true, // send cookies when cross-domain requests
    // headers:{
    //     'X-Host':'mall.film-ticket.film.list'
    // }  可以在这配置请求头
    timeout: 30000 // request timeout
})
 
 
// 添加请求拦截器
service.interceptors.request.use(
    config => {
        // do something before request is sent
        // config.headers['X-Client-Info'] = '{"a":"3000","ch":"1002","v":"5.0.4","e":"15670014718602819495825"}'
        // config.headers['X-Host'] = 'mall.film-ticket.film.list'
        // 也可以在这配置请求头
        return config
    },
    error => {
        // do something with request error
        console.log(error) // for debug
        return Promise.reject(error)
    }
)
 
 
// 添加响应拦截器
service.interceptors.response.use(

    response => {
            return response
        // }
 
    },
    error => {
        console.log('err' + error) // for debug
        // if (error.message=='timeout of 30000ms exceeded') {
        //     // alert('请求超时！')
        //     Message({
        //         type:'error',
        //         message:'请求超时'
        //     })
        //     }
        return Promise.reject(error)
    }
)
 
export default service